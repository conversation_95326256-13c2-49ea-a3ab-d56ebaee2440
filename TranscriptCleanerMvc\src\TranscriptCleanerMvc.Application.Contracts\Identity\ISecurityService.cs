using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// セキュリティサービスインターフェース
    /// </summary>
    public interface ISecurityService : IApplicationService
    {
        /// <summary>
        /// OAuth 2.0/OpenID Connect標準準拠チェック
        /// </summary>
        /// <param name="request">認証リクエスト</param>
        /// <returns>検証結果</returns>
        Task<OAuthValidationResult> ValidateOAuthRequestAsync(OAuthValidationRequest request);

        /// <summary>
        /// PKCE検証
        /// </summary>
        /// <param name="codeVerifier">コードベリファイア</param>
        /// <param name="codeChallenge">コードチャレンジ</param>
        /// <param name="codeChallengeMethod">チャレンジメソッド</param>
        /// <returns>検証結果</returns>
        Task<bool> ValidatePkceAsync(string codeVerifier, string codeChallenge, string codeChallengeMethod);

        /// <summary>
        /// State検証
        /// </summary>
        /// <param name="expectedState">期待されるState</param>
        /// <param name="actualState">実際のState</param>
        /// <returns>検証結果</returns>
        Task<bool> ValidateStateAsync(string expectedState, string actualState);

        /// <summary>
        /// Nonce検証
        /// </summary>
        /// <param name="expectedNonce">期待されるNonce</param>
        /// <param name="actualNonce">実際のNonce</param>
        /// <returns>検証結果</returns>
        Task<bool> ValidateNonceAsync(string expectedNonce, string actualNonce);

        /// <summary>
        /// HTTPS強制設定チェック
        /// </summary>
        /// <param name="requestUrl">リクエストURL</param>
        /// <returns>HTTPS使用フラグ</returns>
        Task<bool> ValidateHttpsAsync(string requestUrl);

        /// <summary>
        /// セキュリティヘッダーを生成
        /// </summary>
        /// <returns>セキュリティヘッダー</returns>
        Task<SecurityHeaders> GenerateSecurityHeadersAsync();

        /// <summary>
        /// CSRFトークンを生成
        /// </summary>
        /// <returns>CSRFトークン</returns>
        Task<string> GenerateCsrfTokenAsync();

        /// <summary>
        /// CSRFトークンを検証
        /// </summary>
        /// <param name="token">トークン</param>
        /// <returns>検証結果</returns>
        Task<bool> ValidateCsrfTokenAsync(string token);

        /// <summary>
        /// レート制限チェック
        /// </summary>
        /// <param name="clientId">クライアントID</param>
        /// <param name="ipAddress">IPアドレス</param>
        /// <returns>制限状況</returns>
        Task<RateLimitResult> CheckRateLimitAsync(string clientId, string ipAddress);

        /// <summary>
        /// セキュリティ監査ログを記録
        /// </summary>
        /// <param name="auditLog">監査ログ</param>
        /// <returns>記録処理</returns>
        Task LogSecurityAuditAsync(SecurityAuditLog auditLog);
    }

    /// <summary>
    /// OAuth検証リクエスト
    /// </summary>
    public class OAuthValidationRequest
    {
        /// <summary>
        /// クライアントID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// リダイレクトURI
        /// </summary>
        public string RedirectUri { get; set; } = string.Empty;

        /// <summary>
        /// レスポンスタイプ
        /// </summary>
        public string ResponseType { get; set; } = string.Empty;

        /// <summary>
        /// スコープ
        /// </summary>
        public string Scope { get; set; } = string.Empty;

        /// <summary>
        /// State
        /// </summary>
        public string? State { get; set; }

        /// <summary>
        /// Nonce
        /// </summary>
        public string? Nonce { get; set; }

        /// <summary>
        /// コードチャレンジ
        /// </summary>
        public string? CodeChallenge { get; set; }

        /// <summary>
        /// コードチャレンジメソッド
        /// </summary>
        public string? CodeChallengeMethod { get; set; }

        /// <summary>
        /// リクエストURL
        /// </summary>
        public string RequestUrl { get; set; } = string.Empty;

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// ユーザーエージェント
        /// </summary>
        public string? UserAgent { get; set; }
    }

    /// <summary>
    /// OAuth検証結果
    /// </summary>
    public class OAuthValidationResult
    {
        /// <summary>
        /// 検証成功フラグ
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// エラーコード
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// エラー説明
        /// </summary>
        public string? ErrorDescription { get; set; }

        /// <summary>
        /// 検証詳細
        /// </summary>
        public ValidationDetail[] Details { get; set; } = Array.Empty<ValidationDetail>();

        /// <summary>
        /// 検証日時
        /// </summary>
        public DateTime ValidatedAt { get; set; }

        /// <summary>
        /// 成功結果を作成
        /// </summary>
        public static OAuthValidationResult Success()
        {
            return new OAuthValidationResult
            {
                IsValid = true,
                ValidatedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 失敗結果を作成
        /// </summary>
        public static OAuthValidationResult Failure(string errorCode, string errorDescription)
        {
            return new OAuthValidationResult
            {
                IsValid = false,
                ErrorCode = errorCode,
                ErrorDescription = errorDescription,
                ValidatedAt = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// 検証詳細
    /// </summary>
    public class ValidationDetail
    {
        /// <summary>
        /// 検証項目
        /// </summary>
        public string Item { get; set; } = string.Empty;

        /// <summary>
        /// 検証結果
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// メッセージ
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// セキュリティヘッダー
    /// </summary>
    public class SecurityHeaders
    {
        /// <summary>
        /// Content Security Policy
        /// </summary>
        public string? ContentSecurityPolicy { get; set; }

        /// <summary>
        /// X-Frame-Options
        /// </summary>
        public string? XFrameOptions { get; set; }

        /// <summary>
        /// X-Content-Type-Options
        /// </summary>
        public string? XContentTypeOptions { get; set; }

        /// <summary>
        /// Referrer-Policy
        /// </summary>
        public string? ReferrerPolicy { get; set; }

        /// <summary>
        /// Strict-Transport-Security
        /// </summary>
        public string? StrictTransportSecurity { get; set; }

        /// <summary>
        /// X-XSS-Protection
        /// </summary>
        public string? XXssProtection { get; set; }
    }

    /// <summary>
    /// レート制限結果
    /// </summary>
    public class RateLimitResult
    {
        /// <summary>
        /// 制限内フラグ
        /// </summary>
        public bool IsAllowed { get; set; }

        /// <summary>
        /// 残りリクエスト数
        /// </summary>
        public int RemainingRequests { get; set; }

        /// <summary>
        /// リセット時刻
        /// </summary>
        public DateTime ResetTime { get; set; }

        /// <summary>
        /// 制限期間（秒）
        /// </summary>
        public int WindowSeconds { get; set; }

        /// <summary>
        /// 制限理由
        /// </summary>
        public string? Reason { get; set; }
    }

    /// <summary>
    /// セキュリティ監査ログ
    /// </summary>
    public class SecurityAuditLog
    {
        /// <summary>
        /// イベントタイプ
        /// </summary>
        public SecurityEventType EventType { get; set; }

        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// クライアントID
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// ユーザーエージェント
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// イベント詳細
        /// </summary>
        public string EventDetails { get; set; } = string.Empty;

        /// <summary>
        /// 成功フラグ
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 発生日時
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// リスクレベル
        /// </summary>
        public RiskLevel RiskLevel { get; set; }

        /// <summary>
        /// 追加データ
        /// </summary>
        public string? AdditionalData { get; set; }
    }

    /// <summary>
    /// セキュリティイベントタイプ
    /// </summary>
    public enum SecurityEventType
    {
        /// <summary>
        /// ログイン試行
        /// </summary>
        LoginAttempt,

        /// <summary>
        /// ログイン成功
        /// </summary>
        LoginSuccess,

        /// <summary>
        /// ログイン失敗
        /// </summary>
        LoginFailure,

        /// <summary>
        /// ログアウト
        /// </summary>
        Logout,

        /// <summary>
        /// アカウント連携
        /// </summary>
        AccountLink,

        /// <summary>
        /// アカウント連携解除
        /// </summary>
        AccountUnlink,

        /// <summary>
        /// パスワード変更
        /// </summary>
        PasswordChange,

        /// <summary>
        /// 権限変更
        /// </summary>
        PermissionChange,

        /// <summary>
        /// 不正アクセス試行
        /// </summary>
        UnauthorizedAccess,

        /// <summary>
        /// レート制限違反
        /// </summary>
        RateLimitViolation,

        /// <summary>
        /// セキュリティ設定変更
        /// </summary>
        SecuritySettingChange,

        /// <summary>
        /// 異常なアクティビティ
        /// </summary>
        SuspiciousActivity
    }

    /// <summary>
    /// リスクレベル
    /// </summary>
    public enum RiskLevel
    {
        /// <summary>
        /// 低
        /// </summary>
        Low,

        /// <summary>
        /// 中
        /// </summary>
        Medium,

        /// <summary>
        /// 高
        /// </summary>
        High,

        /// <summary>
        /// 重大
        /// </summary>
        Critical
    }
}