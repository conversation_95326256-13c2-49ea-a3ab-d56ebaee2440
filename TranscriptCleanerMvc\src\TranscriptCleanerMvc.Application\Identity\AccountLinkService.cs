using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// アカウント連携サービス実装
    /// </summary>
    public class AccountLinkService : ApplicationService, IAccountLinkService
    {
        private readonly IRepository<AppUser, Guid> _appUserRepository;
        private readonly IRepository<ExternalLoginLog, Guid> _externalLoginLogRepository;
        private readonly IIdentityUserRepository _identityUserRepository;

        public AccountLinkService(
            IRepository<AppUser, Guid> appUserRepository,
            IRepository<ExternalLoginLog, Guid> externalLoginLogRepository,
            IIdentityUserRepository identityUserRepository)
        {
            _appUserRepository = appUserRepository;
            _externalLoginLogRepository = externalLoginLogRepository;
            _identityUserRepository = identityUserRepository;
        }

        /// <summary>
        /// 外部アカウント連携の重複チェック
        /// </summary>
        public async Task<bool> IsExternalAccountAlreadyLinkedAsync(string provider, string providerKey, Guid? excludeUserId = null)
        {
            try
            {
                if (provider == "Google")
                {
                    var query = await _appUserRepository.GetQueryableAsync();
                    var existingUser = query.FirstOrDefault(u => u.GoogleId == providerKey);
                    
                    if (existingUser == null)
                        return false;

                    return excludeUserId == null || existingUser.Id != excludeUserId;
                }

                return false;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "外部アカウント重複チェック中にエラーが発生しました。Provider: {Provider}, ProviderKey: {ProviderKey}", provider, providerKey);
                return true; // エラー時は安全側に倒す
            }
        }

        /// <summary>
        /// ユーザーの外部アカウント連携一覧を取得
        /// </summary>
        public async Task<List<ExternalAccountLinkDto>> GetUserExternalAccountLinksAsync(Guid userId)
        {
            try
            {
                var user = await _appUserRepository.GetAsync(userId);
                var links = new List<ExternalAccountLinkDto>();

                // Google連携情報
                if (user.IsGoogleLinked && !string.IsNullOrEmpty(user.GoogleId))
                {
                    links.Add(new ExternalAccountLinkDto
                    {
                        Provider = "Google",
                        ProviderKey = user.GoogleId,
                        ProviderDisplayName = "Google",
                        IsLinked = true,
                        Email = user.GoogleEmail,
                        LastSyncTime = user.LastGoogleSync,
                        Status = "Active"
                    });
                }
                else
                {
                    links.Add(new ExternalAccountLinkDto
                    {
                        Provider = "Google",
                        ProviderKey = string.Empty,
                        ProviderDisplayName = "Google",
                        IsLinked = false,
                        Status = "NotLinked"
                    });
                }

                // 将来的に他のプロバイダー（Microsoft, GitHub等）もここに追加

                return links;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "ユーザー外部アカウント連携一覧取得中にエラーが発生しました。UserId: {UserId}", userId);
                return new List<ExternalAccountLinkDto>();
            }
        }

        /// <summary>
        /// 外部アカウント連携を強制解除（管理者用）
        /// </summary>
        public async Task<bool> ForceUnlinkExternalAccountAsync(Guid userId, string provider)
        {
            try
            {
                var user = await _appUserRepository.GetAsync(userId);

                if (provider == "Google" && user.IsGoogleLinked)
                {
                    user.UnlinkGoogleAccount();
                    await _appUserRepository.UpdateAsync(user);

                    Logger.LogInformation("管理者によりGoogleアカウント連携が強制解除されました。UserId: {UserId}", userId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "外部アカウント連携強制解除中にエラーが発生しました。UserId: {UserId}, Provider: {Provider}", userId, provider);
                return false;
            }
        }

        /// <summary>
        /// 外部アカウント連携の検証
        /// </summary>
        public async Task<AccountLinkValidationResultDto> ValidateAccountLinkAsync(Guid userId, string provider, string providerKey)
        {
            try
            {
                // 基本的な入力検証
                if (string.IsNullOrEmpty(provider) || string.IsNullOrEmpty(providerKey))
                {
                    return AccountLinkValidationResultDto.CreateError("プロバイダー情報が不正です");
                }

                // ユーザーの存在確認
                var user = await _appUserRepository.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return AccountLinkValidationResultDto.CreateError("ユーザーが見つかりません");
                }

                // 既に同じプロバイダーで連携済みかチェック
                if (provider == "Google" && user.IsGoogleLinked)
                {
                    if (user.GoogleId == providerKey)
                    {
                        return AccountLinkValidationResultDto.CreateError("既に同じアカウントで連携済みです");
                    }
                    else
                    {
                        return AccountLinkValidationResultDto.CreateError("既に別のGoogleアカウントで連携済みです");
                    }
                }

                // 他のユーザーが同じ外部アカウントを使用していないかチェック
                var isDuplicate = await IsExternalAccountAlreadyLinkedAsync(provider, providerKey, userId);
                if (isDuplicate)
                {
                    // 重複しているユーザー情報を取得
                    var duplicateUser = await GetDuplicateUserInfoAsync(provider, providerKey);
                    if (duplicateUser != null)
                    {
                        return AccountLinkValidationResultDto.CreateDuplicateError(duplicateUser);
                    }
                    else
                    {
                        return AccountLinkValidationResultDto.CreateError("このアカウントは既に他のユーザーに連携されています");
                    }
                }

                // すべての検証をパス
                return AccountLinkValidationResultDto.CreateSuccess();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "アカウント連携検証中にエラーが発生しました。UserId: {UserId}, Provider: {Provider}", userId, provider);
                return AccountLinkValidationResultDto.CreateError($"検証中にエラーが発生しました: {ex.Message}");
            }
        }

        /// <summary>
        /// 重複しているユーザー情報を取得
        /// </summary>
        private async Task<DuplicateAccountInfoDto?> GetDuplicateUserInfoAsync(string provider, string providerKey)
        {
            try
            {
                if (provider == "Google")
                {
                    var query = await _appUserRepository.GetQueryableAsync();
                    var duplicateUser = query.FirstOrDefault(u => u.GoogleId == providerKey);
                    
                    if (duplicateUser != null)
                    {
                        return new DuplicateAccountInfoDto
                        {
                            UserId = duplicateUser.Id,
                            UserName = duplicateUser.UserName ?? string.Empty,
                            Email = duplicateUser.Email ?? string.Empty,
                            LinkedAt = duplicateUser.LastGoogleSync ?? DateTime.UtcNow
                        };
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "重複ユーザー情報取得中にエラーが発生しました。Provider: {Provider}, ProviderKey: {ProviderKey}", provider, providerKey);
                return null;
            }
        }
    }
}