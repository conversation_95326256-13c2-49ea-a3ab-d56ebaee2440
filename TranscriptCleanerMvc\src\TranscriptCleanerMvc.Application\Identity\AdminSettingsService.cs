using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.SettingManagement;
using Volo.Abp.Settings;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// 管理者設定サービス実装
    /// </summary>
    [Authorize(Roles = "admin")]
    public class AdminSettingsService : ApplicationService, IAdminSettingsService
    {
        private readonly ISettingManager _settingManager;
        private readonly IConfiguration _configuration;
        private readonly IRepository<ExternalLoginLog, Guid> _externalLoginLogRepository;
        private readonly IIdentityUserRepository _userRepository;
        private readonly ILogger<AdminSettingsService> _logger;

        // 設定キー定数
        private const string GOOGLE_AUTH_ENABLED = "GoogleAuth.IsEnabled";
        private const string GOOGLE_CLIENT_ID = "GoogleAuth.ClientId";
        private const string GOOGLE_CLIENT_SECRET = "GoogleAuth.ClientSecret";
        private const string GOOGLE_REDIRECT_URI = "GoogleAuth.RedirectUri";
        private const string GOOGLE_SCOPES = "GoogleAuth.Scopes";
        private const string GOOGLE_AUTO_CREATE_ACCOUNT = "GoogleAuth.AutoCreateAccount";
        private const string GOOGLE_DEFAULT_ROLES = "GoogleAuth.DefaultRoles";

        private const string SESSION_TIMEOUT = "System.SessionTimeoutMinutes";
        private const string MAX_CONCURRENT_SESSIONS = "System.MaxConcurrentSessions";
        private const string PASSWORD_POLICY_ENABLED = "System.PasswordPolicyEnabled";
        private const string REQUIRE_TWO_FACTOR_AUTH = "System.RequireTwoFactorAuth";
        private const string LOG_RETENTION_DAYS = "System.LogRetentionDays";
        private const string MAINTENANCE_MODE = "System.MaintenanceMode";
        private const string MAINTENANCE_MESSAGE = "System.MaintenanceMessage";

        public AdminSettingsService(
            ISettingManager settingManager,
            IConfiguration configuration,
            IRepository<ExternalLoginLog, Guid> externalLoginLogRepository,
            IIdentityUserRepository userRepository,
            ILogger<AdminSettingsService> logger)
        {
            _settingManager = settingManager;
            _configuration = configuration;
            _externalLoginLogRepository = externalLoginLogRepository;
            _userRepository = userRepository;
            _logger = logger;
        }

        /// <summary>
        /// Google認証設定を取得
        /// </summary>
        public async Task<GoogleAuthSettingsDto> GetGoogleAuthSettingsAsync()
        {
            try
            {
                _logger.LogInformation("Google認証設定を取得します");

                var isEnabled = await _settingManager.GetOrNullAsync(GOOGLE_AUTH_ENABLED, GlobalSettingValueProvider.ProviderName, null);
                var clientId = await _settingManager.GetOrNullAsync(GOOGLE_CLIENT_ID, GlobalSettingValueProvider.ProviderName, null);
                var clientSecret = await _settingManager.GetOrNullAsync(GOOGLE_CLIENT_SECRET, GlobalSettingValueProvider.ProviderName, null);
                var redirectUri = await _settingManager.GetOrNullAsync(GOOGLE_REDIRECT_URI, GlobalSettingValueProvider.ProviderName, null);
                var scopes = await _settingManager.GetOrNullAsync(GOOGLE_SCOPES, GlobalSettingValueProvider.ProviderName, null);
                var autoCreateAccount = await _settingManager.GetOrNullAsync(GOOGLE_AUTO_CREATE_ACCOUNT, GlobalSettingValueProvider.ProviderName, null);
                var defaultRoles = await _settingManager.GetOrNullAsync(GOOGLE_DEFAULT_ROLES, GlobalSettingValueProvider.ProviderName, null);

                return new GoogleAuthSettingsDto
                {
                    IsEnabled = bool.TryParse(isEnabled, out var enabled) && enabled,
                    ClientId = clientId,
                    ClientSecretMasked = MaskSecret(clientSecret),
                    RedirectUri = redirectUri,
                    Scopes = scopes?.Split(',', StringSplitOptions.RemoveEmptyEntries),
                    AutoCreateAccount = bool.TryParse(autoCreateAccount, out var autoCreate) && autoCreate,
                    DefaultRoles = defaultRoles?.Split(',', StringSplitOptions.RemoveEmptyEntries),
                    LastUpdated = DateTime.UtcNow, // TODO: 実際の更新日時を取得
                    LastUpdatedBy = CurrentUser.UserName
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Google認証設定取得中にエラーが発生しました");
                throw new UserFriendlyException("Google認証設定の取得に失敗しました");
            }
        }

        /// <summary>
        /// Google認証設定を更新
        /// </summary>
        public async Task<GoogleAuthSettingsDto> UpdateGoogleAuthSettingsAsync(UpdateGoogleAuthSettingsDto input)
        {
            try
            {
                _logger.LogInformation("Google認証設定を更新します。UserId: {UserId}", CurrentUser.Id);

                // 設定を更新
                await _settingManager.SetAsync(GOOGLE_AUTH_ENABLED, input.IsEnabled.ToString(), GlobalSettingValueProvider.ProviderName, null);
                
                if (!string.IsNullOrEmpty(input.ClientId))
                {
                    await _settingManager.SetAsync(GOOGLE_CLIENT_ID, input.ClientId, GlobalSettingValueProvider.ProviderName, null);
                }

                if (!string.IsNullOrEmpty(input.ClientSecret))
                {
                    await _settingManager.SetAsync(GOOGLE_CLIENT_SECRET, input.ClientSecret, GlobalSettingValueProvider.ProviderName, null);
                }

                if (!string.IsNullOrEmpty(input.RedirectUri))
                {
                    await _settingManager.SetAsync(GOOGLE_REDIRECT_URI, input.RedirectUri, GlobalSettingValueProvider.ProviderName, null);
                }

                if (input.Scopes != null && input.Scopes.Length > 0)
                {
                    await _settingManager.SetAsync(GOOGLE_SCOPES, string.Join(",", input.Scopes), GlobalSettingValueProvider.ProviderName, null);
                }

                await _settingManager.SetAsync(GOOGLE_AUTO_CREATE_ACCOUNT, input.AutoCreateAccount.ToString(), GlobalSettingValueProvider.ProviderName, null);

                if (input.DefaultRoles != null && input.DefaultRoles.Length > 0)
                {
                    await _settingManager.SetAsync(GOOGLE_DEFAULT_ROLES, string.Join(",", input.DefaultRoles), GlobalSettingValueProvider.ProviderName, null);
                }

                // 変更履歴を記録
                await LogSettingsChangeAsync("GoogleAuth", "設定更新", "管理者による設定変更");

                _logger.LogInformation("Google認証設定が更新されました。UserId: {UserId}", CurrentUser.Id);

                // 更新された設定を返す
                return await GetGoogleAuthSettingsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Google認証設定更新中にエラーが発生しました");
                throw new UserFriendlyException("Google認証設定の更新に失敗しました");
            }
        }

        /// <summary>
        /// 外部認証統計を取得
        /// </summary>
        public async Task<ExternalAuthStatisticsDto> GetExternalAuthStatisticsAsync()
        {
            try
            {
                _logger.LogInformation("外部認証統計を取得します");

                var today = DateTime.Today;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                var monthStart = new DateTime(today.Year, today.Month, 1);

                // 総ユーザー数
                var totalUsers = await _userRepository.GetCountAsync();

                // Google連携ユーザー数
                var googleLinkedUsers = await _userRepository.GetCountAsync();

                // 認証ログから統計を取得
                var allLogs = await _externalLoginLogRepository.GetListAsync();
                
                var todayLogins = allLogs.Count(log => 
                    log.Provider == "Google" && 
                    log.CreationTime >= today && 
                    log.Success);

                var weeklyLogins = allLogs.Count(log => 
                    log.Provider == "Google" && 
                    log.CreationTime >= weekStart && 
                    log.Success);

                var monthlyLogins = allLogs.Count(log => 
                    log.Provider == "Google" && 
                    log.CreationTime >= monthStart && 
                    log.Success);

                var todayErrors = allLogs.Count(log => 
                    log.Provider == "Google" && 
                    log.CreationTime >= today && 
                    !log.Success);

                return new ExternalAuthStatisticsDto
                {
                    TotalUsers = (int)totalUsers,
                    GoogleLinkedUsers = (int)googleLinkedUsers,
                    TodayGoogleLogins = todayLogins,
                    WeeklyGoogleLogins = weeklyLogins,
                    MonthlyGoogleLogins = monthlyLogins,
                    TodayAuthErrors = todayErrors,
                    ActiveSessions = 0, // TODO: アクティブセッション数の実装
                    LastUpdated = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "外部認証統計取得中にエラーが発生しました");
                throw new UserFriendlyException("統計情報の取得に失敗しました");
            }
        }

        /// <summary>
        /// システム設定を取得
        /// </summary>
        public async Task<SystemSettingsDto> GetSystemSettingsAsync()
        {
            try
            {
                _logger.LogInformation("システム設定を取得します");

                var sessionTimeout = await _settingManager.GetOrNullAsync(SESSION_TIMEOUT, GlobalSettingValueProvider.ProviderName, null);
                var maxSessions = await _settingManager.GetOrNullAsync(MAX_CONCURRENT_SESSIONS, GlobalSettingValueProvider.ProviderName, null);
                var passwordPolicy = await _settingManager.GetOrNullAsync(PASSWORD_POLICY_ENABLED, GlobalSettingValueProvider.ProviderName, null);
                var requireTwoFactor = await _settingManager.GetOrNullAsync(REQUIRE_TWO_FACTOR_AUTH, GlobalSettingValueProvider.ProviderName, null);
                var logRetention = await _settingManager.GetOrNullAsync(LOG_RETENTION_DAYS, GlobalSettingValueProvider.ProviderName, null);
                var maintenanceMode = await _settingManager.GetOrNullAsync(MAINTENANCE_MODE, GlobalSettingValueProvider.ProviderName, null);
                var maintenanceMessage = await _settingManager.GetOrNullAsync(MAINTENANCE_MESSAGE, GlobalSettingValueProvider.ProviderName, null);

                return new SystemSettingsDto
                {
                    SessionTimeoutMinutes = int.TryParse(sessionTimeout, out var timeout) ? timeout : 30,
                    MaxConcurrentSessions = int.TryParse(maxSessions, out var maxSess) ? maxSess : 5,
                    PasswordPolicyEnabled = bool.TryParse(passwordPolicy, out var pwdPolicy) && pwdPolicy,
                    RequireTwoFactorAuth = bool.TryParse(requireTwoFactor, out var require2FA) && require2FA,
                    LogRetentionDays = int.TryParse(logRetention, out var retention) ? retention : 90,
                    MaintenanceMode = bool.TryParse(maintenanceMode, out var maintenance) && maintenance,
                    MaintenanceMessage = maintenanceMessage
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "システム設定取得中にエラーが発生しました");
                throw new UserFriendlyException("システム設定の取得に失敗しました");
            }
        }

        /// <summary>
        /// システム設定を更新
        /// </summary>
        public async Task<SystemSettingsDto> UpdateSystemSettingsAsync(UpdateSystemSettingsDto input)
        {
            try
            {
                _logger.LogInformation("システム設定を更新します。UserId: {UserId}", CurrentUser.Id);

                await _settingManager.SetAsync(SESSION_TIMEOUT, input.SessionTimeoutMinutes.ToString(), GlobalSettingValueProvider.ProviderName, null);
                await _settingManager.SetAsync(MAX_CONCURRENT_SESSIONS, input.MaxConcurrentSessions.ToString(), GlobalSettingValueProvider.ProviderName, null);
                await _settingManager.SetAsync(PASSWORD_POLICY_ENABLED, input.PasswordPolicyEnabled.ToString(), GlobalSettingValueProvider.ProviderName, null);
                await _settingManager.SetAsync(REQUIRE_TWO_FACTOR_AUTH, input.RequireTwoFactorAuth.ToString(), GlobalSettingValueProvider.ProviderName, null);
                await _settingManager.SetAsync(LOG_RETENTION_DAYS, input.LogRetentionDays.ToString(), GlobalSettingValueProvider.ProviderName, null);
                await _settingManager.SetAsync(MAINTENANCE_MODE, input.MaintenanceMode.ToString(), GlobalSettingValueProvider.ProviderName, null);
                
                if (!string.IsNullOrEmpty(input.MaintenanceMessage))
                {
                    await _settingManager.SetAsync(MAINTENANCE_MESSAGE, input.MaintenanceMessage, GlobalSettingValueProvider.ProviderName, null);
                }

                // 変更履歴を記録
                await LogSettingsChangeAsync("System", "設定更新", "管理者による設定変更");

                _logger.LogInformation("システム設定が更新されました。UserId: {UserId}", CurrentUser.Id);

                return await GetSystemSettingsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "システム設定更新中にエラーが発生しました");
                throw new UserFriendlyException("システム設定の更新に失敗しました");
            }
        }

        /// <summary>
        /// 設定変更履歴を取得
        /// </summary>
        public async Task<SettingsChangeHistoryDto[]> GetSettingsChangeHistoryAsync()
        {
            try
            {
                _logger.LogInformation("設定変更履歴を取得します");

                // TODO: 実際の変更履歴テーブルから取得
                // 現在はモック実装
                return new[]
                {
                    new SettingsChangeHistoryDto
                    {
                        Id = Guid.NewGuid(),
                        SettingName = "GoogleAuth.IsEnabled",
                        OldValue = "false",
                        NewValue = "true",
                        ChangedBy = CurrentUser.UserName ?? "admin",
                        ChangedAt = DateTime.UtcNow.AddHours(-1),
                        Reason = "Google認証を有効化",
                        IpAddress = "*************"
                    },
                    new SettingsChangeHistoryDto
                    {
                        Id = Guid.NewGuid(),
                        SettingName = "System.SessionTimeoutMinutes",
                        OldValue = "30",
                        NewValue = "60",
                        ChangedBy = CurrentUser.UserName ?? "admin",
                        ChangedAt = DateTime.UtcNow.AddDays(-1),
                        Reason = "セッションタイムアウト時間を延長",
                        IpAddress = "*************"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "設定変更履歴取得中にエラーが発生しました");
                throw new UserFriendlyException("変更履歴の取得に失敗しました");
            }
        }

        /// <summary>
        /// シークレットをマスク
        /// </summary>
        private string? MaskSecret(string? secret)
        {
            if (string.IsNullOrEmpty(secret))
            {
                return null;
            }

            if (secret.Length <= 8)
            {
                return new string('*', secret.Length);
            }

            return secret.Substring(0, 4) + new string('*', secret.Length - 8) + secret.Substring(secret.Length - 4);
        }

        /// <summary>
        /// 設定変更をログに記録
        /// </summary>
        private async Task LogSettingsChangeAsync(string settingName, string action, string reason)
        {
            try
            {
                // TODO: 実際の変更履歴テーブルに記録
                _logger.LogInformation("設定変更: {SettingName}, アクション: {Action}, 理由: {Reason}, ユーザー: {UserId}", 
                    settingName, action, reason, CurrentUser.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "設定変更ログ記録中にエラーが発生しました");
                // ログ記録の失敗は設定更新を阻害しない
            }
        }
    }
}