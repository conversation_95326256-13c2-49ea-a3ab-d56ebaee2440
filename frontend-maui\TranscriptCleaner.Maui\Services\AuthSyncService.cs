using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Storage;

namespace TranscriptCleaner.Maui.Services
{
    /// <summary>
    /// 認証状態同期サービス実装
    /// </summary>
    public class AuthSyncService : IAuthSyncService
    {
        private readonly HttpClient _httpClient;
        private readonly IJwtTokenService _jwtTokenService;
        private readonly ILogger<AuthSyncService> _logger;
        private readonly string _baseUrl;
        private readonly string _clientId;

        // セキュアストレージキー
        private const string CLIENT_ID_KEY = "auth_sync_client_id";
        private const string LAST_SYNC_KEY = "auth_sync_last_sync";

        public event EventHandler<AuthStateChangedEventArgs>? AuthStateChanged;

        public AuthSyncService(
            HttpClient httpClient,
            IJwtTokenService jwtTokenService,
            ILogger<AuthSyncService> logger)
        {
            _httpClient = httpClient;
            _jwtTokenService = jwtTokenService;
            _logger = logger;
            
            // 設定値（実際の実装では設定ファイルから読み込み）
            _baseUrl = "https://localhost:44396"; // ABP Framework API URL
            _clientId = GenerateClientId();
        }

        /// <summary>
        /// サーバー接続確認
        /// </summary>
        public async Task<ServerConnectionStatus> CheckServerConnectionAsync()
        {
            try
            {
                _logger.LogInformation("サーバー接続を確認します");

                var response = await _httpClient.GetAsync($"{_baseUrl}/api/health");
                
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("サーバー接続が確認されました");
                    return ServerConnectionStatus.Connected;
                }
                else
                {
                    _logger.LogWarning("サーバー接続に問題があります。Status: {Status}", response.StatusCode);
                    return ServerConnectionStatus.Error;
                }
            }
            catch (TaskCanceledException)
            {
                _logger.LogWarning("サーバー接続がタイムアウトしました");
                return ServerConnectionStatus.Timeout;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "サーバー接続中にネットワークエラーが発生しました");
                return ServerConnectionStatus.Disconnected;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "サーバー接続確認中にエラーが発生しました");
                return ServerConnectionStatus.Error;
            }
        }

        /// <summary>
        /// 複数クライアント間の認証状態同期
        /// </summary>
        public async Task<AuthSyncResult> SyncAuthStateAsync()
        {
            try
            {
                _logger.LogInformation("認証状態同期を開始します");

                // サーバー接続を確認
                var connectionStatus = await CheckServerConnectionAsync();
                if (connectionStatus != ServerConnectionStatus.Connected)
                {
                    return AuthSyncResult.Failure($"サーバー接続エラー: {connectionStatus}");
                }

                // JWTトークンを取得
                var jwtToken = await _jwtTokenService.GetCurrentJwtTokenAsync();
                if (string.IsNullOrEmpty(jwtToken))
                {
                    return AuthSyncResult.Failure("JWTトークンが見つかりません");
                }

                // 現在の認証状態を取得
                var currentAuthState = await GetCurrentAuthStateAsync();

                // サーバーに同期リクエストを送信
                var syncRequest = new
                {
                    ClientId = _clientId,
                    ClientType = ClientType.Maui,
                    Platform = DeviceInfo.Platform.ToString(),
                    Version = AppInfo.VersionString,
                    AuthState = currentAuthState,
                    Timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(syncRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", jwtToken);

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/sync", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var syncResponse = JsonSerializer.Deserialize<AuthSyncResponse>(responseContent);

                    if (syncResponse != null)
                    {
                        // 最後の同期時刻を更新
                        await SecureStorage.SetAsync(LAST_SYNC_KEY, DateTime.UtcNow.ToString("O"));

                        _logger.LogInformation("認証状態同期が完了しました。同期クライアント数: {Count}", 
                            syncResponse.SyncedClientCount);

                        return AuthSyncResult.Success(syncResponse.SyncedClientCount, syncResponse.FailedClientCount);
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("認証状態同期に失敗しました。Status: {Status}, Response: {Response}", 
                        response.StatusCode, errorContent);
                    return AuthSyncResult.Failure($"同期エラー: {response.StatusCode}");
                }

                return AuthSyncResult.Failure("不明なエラー");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証状態同期中にエラーが発生しました");
                return AuthSyncResult.Failure($"同期エラー: {ex.Message}");
            }
        }

        /// <summary>
        /// 一貫した認証体験を提供
        /// </summary>
        public async Task<bool> EnsureConsistentAuthExperienceAsync()
        {
            try
            {
                _logger.LogInformation("一貫した認証体験の提供を開始します");

                // 他のクライアントの認証状態を取得
                var activeClients = await GetActiveClientsAsync();
                var currentAuthState = await GetCurrentAuthStateAsync();

                // 認証状態の不整合をチェック
                foreach (var client in activeClients)
                {
                    if (client.ClientId != _clientId && client.AuthState != currentAuthState)
                    {
                        _logger.LogInformation("認証状態の不整合を検出しました。Client: {ClientId}, State: {State}", 
                            client.ClientId, client.AuthState);

                        // 不整合を解決
                        await ResolveAuthStateInconsistencyAsync(client, currentAuthState);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "一貫した認証体験提供中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// 認証状態の変更を他のクライアントに通知
        /// </summary>
        public async Task<bool> NotifyAuthStateChangeAsync(AuthenticationState authState)
        {
            try
            {
                _logger.LogInformation("認証状態変更を通知します。State: {State}", authState);

                var jwtToken = await _jwtTokenService.GetCurrentJwtTokenAsync();
                if (string.IsNullOrEmpty(jwtToken))
                {
                    _logger.LogWarning("JWTトークンが見つかりません。通知をスキップします");
                    return false;
                }

                var notification = new
                {
                    ClientId = _clientId,
                    AuthState = authState,
                    Timestamp = DateTime.UtcNow,
                    UserId = await GetCurrentUserIdAsync()
                };

                var json = JsonSerializer.Serialize(notification);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", jwtToken);

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/notify", content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("認証状態変更の通知が成功しました");
                    return true;
                }
                else
                {
                    _logger.LogError("認証状態変更の通知に失敗しました。Status: {Status}", response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証状態変更通知中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// 他のクライアントからの認証状態変更を受信
        /// </summary>
        public async Task StartListeningForAuthStateChangesAsync()
        {
            try
            {
                _logger.LogInformation("認証状態変更の受信を開始します");

                // TODO: SignalRまたはWebSocketを使用したリアルタイム通信の実装
                // 現在はポーリングベースのモック実装
                _ = Task.Run(async () =>
                {
                    while (true)
                    {
                        try
                        {
                            await PollForAuthStateChangesAsync();
                            await Task.Delay(TimeSpan.FromSeconds(30)); // 30秒間隔でポーリング
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "認証状態変更ポーリング中にエラーが発生しました");
                            await Task.Delay(TimeSpan.FromMinutes(1)); // エラー時は1分待機
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証状態変更受信開始中にエラーが発生しました");
            }
        }

        /// <summary>
        /// 認証状態変更の受信を停止
        /// </summary>
        public Task StopListeningForAuthStateChangesAsync()
        {
            try
            {
                _logger.LogInformation("認証状態変更の受信を停止します");
                // TODO: リアルタイム通信の停止処理
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証状態変更受信停止中にエラーが発生しました");
            }
            
            return Task.CompletedTask;
        }

        /// <summary>
        /// 現在のクライアント情報を登録
        /// </summary>
        public async Task<bool> RegisterClientAsync()
        {
            try
            {
                _logger.LogInformation("クライアント情報を登録します。ClientId: {ClientId}", _clientId);

                var jwtToken = await _jwtTokenService.GetCurrentJwtTokenAsync();
                if (string.IsNullOrEmpty(jwtToken))
                {
                    _logger.LogWarning("JWTトークンが見つかりません");
                    return false;
                }

                var clientInfo = new ClientInfo
                {
                    ClientId = _clientId,
                    Type = ClientType.Maui,
                    Platform = DeviceInfo.Platform.ToString(),
                    Version = AppInfo.VersionString,
                    LastActivity = DateTime.UtcNow,
                    AuthState = await GetCurrentAuthStateAsync(),
                    UserId = await GetCurrentUserIdAsync(),
                    DeviceName = DeviceInfo.Name
                };

                var json = JsonSerializer.Serialize(clientInfo);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", jwtToken);

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/clients/register", content);

                if (response.IsSuccessStatusCode)
                {
                    await SecureStorage.SetAsync(CLIENT_ID_KEY, _clientId);
                    _logger.LogInformation("クライアント情報の登録が成功しました");
                    return true;
                }
                else
                {
                    _logger.LogError("クライアント情報の登録に失敗しました。Status: {Status}", response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "クライアント情報登録中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// クライアント情報を登録解除
        /// </summary>
        public async Task<bool> UnregisterClientAsync()
        {
            try
            {
                _logger.LogInformation("クライアント情報を登録解除します。ClientId: {ClientId}", _clientId);

                var jwtToken = await _jwtTokenService.GetCurrentJwtTokenAsync();
                if (string.IsNullOrEmpty(jwtToken))
                {
                    _logger.LogWarning("JWTトークンが見つかりません");
                    return false;
                }

                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", jwtToken);

                var response = await _httpClient.DeleteAsync($"{_baseUrl}/api/auth/clients/{_clientId}");

                if (response.IsSuccessStatusCode)
                {
                    SecureStorage.Remove(CLIENT_ID_KEY);
                    _logger.LogInformation("クライアント情報の登録解除が成功しました");
                    return true;
                }
                else
                {
                    _logger.LogError("クライアント情報の登録解除に失敗しました。Status: {Status}", response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "クライアント情報登録解除中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// アクティブなクライアント一覧を取得
        /// </summary>
        public async Task<ClientInfo[]> GetActiveClientsAsync()
        {
            try
            {
                var jwtToken = await _jwtTokenService.GetCurrentJwtTokenAsync();
                if (string.IsNullOrEmpty(jwtToken))
                {
                    return Array.Empty<ClientInfo>();
                }

                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", jwtToken);

                var response = await _httpClient.GetAsync($"{_baseUrl}/api/auth/clients/active");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var clients = JsonSerializer.Deserialize<ClientInfo[]>(responseContent);
                    return clients ?? Array.Empty<ClientInfo>();
                }
                else
                {
                    _logger.LogError("アクティブクライアント取得に失敗しました。Status: {Status}", response.StatusCode);
                    return Array.Empty<ClientInfo>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "アクティブクライアント取得中にエラーが発生しました");
                return Array.Empty<ClientInfo>();
            }
        }

        /// <summary>
        /// 現在の認証状態を取得
        /// </summary>
        private async Task<AuthenticationState> GetCurrentAuthStateAsync()
        {
            try
            {
                var isExpired = await _jwtTokenService.IsTokenExpiredAsync();
                if (isExpired)
                {
                    return AuthenticationState.TokenExpired;
                }

                var jwtToken = await _jwtTokenService.GetCurrentJwtTokenAsync();
                return string.IsNullOrEmpty(jwtToken) ? AuthenticationState.NotAuthenticated : AuthenticationState.Authenticated;
            }
            catch
            {
                return AuthenticationState.Error;
            }
        }

        /// <summary>
        /// 現在のユーザーIDを取得
        /// </summary>
        private Task<string?> GetCurrentUserIdAsync()
        {
            try
            {
                // TODO: JWTトークンからユーザーIDを抽出
                return Task.FromResult<string?>("current_user_id");
            }
            catch
            {
                return Task.FromResult<string?>(null);
            }
        }

        /// <summary>
        /// 認証状態変更をポーリング
        /// </summary>
        private async Task PollForAuthStateChangesAsync()
        {
            try
            {
                var jwtToken = await _jwtTokenService.GetCurrentJwtTokenAsync();
                if (string.IsNullOrEmpty(jwtToken))
                {
                    return;
                }

                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", jwtToken);

                var response = await _httpClient.GetAsync($"{_baseUrl}/api/auth/changes?clientId={_clientId}");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var changes = JsonSerializer.Deserialize<AuthStateChangedEventArgs[]>(responseContent);

                    if (changes != null)
                    {
                        foreach (var change in changes)
                        {
                            AuthStateChanged?.Invoke(this, change);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証状態変更ポーリング中にエラーが発生しました");
            }
        }

        /// <summary>
        /// 認証状態の不整合を解決
        /// </summary>
        private async Task ResolveAuthStateInconsistencyAsync(ClientInfo client, AuthenticationState expectedState)
        {
            try
            {
                _logger.LogInformation("認証状態の不整合を解決します。Client: {ClientId}", client.ClientId);

                // 不整合解決のロジック（実装は要件に応じて調整）
                if (expectedState == AuthenticationState.Authenticated && client.AuthState == AuthenticationState.NotAuthenticated)
                {
                    // 他のクライアントに認証を促す通知を送信
                    await NotifyAuthStateChangeAsync(AuthenticationState.Authenticated);
                }
                else if (expectedState == AuthenticationState.NotAuthenticated && client.AuthState == AuthenticationState.Authenticated)
                {
                    // 他のクライアントにログアウトを促す通知を送信
                    await NotifyAuthStateChangeAsync(AuthenticationState.LoggedOut);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証状態不整合解決中にエラーが発生しました");
            }
        }

        /// <summary>
        /// クライアントIDを生成
        /// </summary>
        private string GenerateClientId()
        {
            return $"maui_{DeviceInfo.Name}_{Guid.NewGuid():N}";
        }
    }

    /// <summary>
    /// 認証同期レスポンス
    /// </summary>
    public class AuthSyncResponse
    {
        public int SyncedClientCount { get; set; }
        public int FailedClientCount { get; set; }
        public string? Message { get; set; }
    }
}