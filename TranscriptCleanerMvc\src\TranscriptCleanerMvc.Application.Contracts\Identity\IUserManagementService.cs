using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// ユーザー管理サービスインターフェース
    /// </summary>
    public interface IUserManagementService : IApplicationService
    {
        /// <summary>
        /// ユーザー一覧を取得（Google連携状況付き）
        /// </summary>
        /// <param name="input">検索条件</param>
        /// <returns>ユーザー一覧</returns>
        Task<PagedResultDto<UserWithExternalLinksDto>> GetUsersWithExternalLinksAsync(GetUsersWithExternalLinksInput input);

        /// <summary>
        /// ユーザーのGoogle連携状況を取得
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>連携状況</returns>
        Task<UserExternalLinkStatusDto> GetUserExternalLinkStatusAsync(Guid userId);

        /// <summary>
        /// 管理者による強制連携解除
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="provider">プロバイダー名</param>
        /// <returns>解除結果</returns>
        Task<bool> ForceUnlinkExternalAccountAsync(Guid userId, string provider);

        /// <summary>
        /// ユーザーに通知を送信
        /// </summary>
        /// <param name="input">通知内容</param>
        /// <returns>送信結果</returns>
        Task<bool> SendUserNotificationAsync(SendUserNotificationInput input);

        /// <summary>
        /// 複数ユーザーに一括通知を送信
        /// </summary>
        /// <param name="input">一括通知内容</param>
        /// <returns>送信結果</returns>
        Task<BulkNotificationResultDto> SendBulkNotificationAsync(SendBulkNotificationInput input);

        /// <summary>
        /// ユーザーのログイン履歴を取得
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="input">検索条件</param>
        /// <returns>ログイン履歴</returns>
        Task<PagedResultDto<UserLoginHistoryDto>> GetUserLoginHistoryAsync(Guid userId, GetUserLoginHistoryInput input);

        /// <summary>
        /// ユーザーアカウントを無効化
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="reason">無効化理由</param>
        /// <returns>無効化結果</returns>
        Task<bool> DisableUserAccountAsync(Guid userId, string reason);

        /// <summary>
        /// ユーザーアカウントを有効化
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>有効化結果</returns>
        Task<bool> EnableUserAccountAsync(Guid userId);
    }

    /// <summary>
    /// 外部連携付きユーザーDTO
    /// </summary>
    public class UserWithExternalLinksDto
    {
        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// ユーザー名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// メールアドレス
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 表示名
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 姓
        /// </summary>
        public string? Surname { get; set; }

        /// <summary>
        /// 電話番号
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// アクティブフラグ
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// メール確認済みフラグ
        /// </summary>
        public bool EmailConfirmed { get; set; }

        /// <summary>
        /// 電話番号確認済みフラグ
        /// </summary>
        public bool PhoneNumberConfirmed { get; set; }

        /// <summary>
        /// 2要素認証有効フラグ
        /// </summary>
        public bool TwoFactorEnabled { get; set; }

        /// <summary>
        /// ロックアウト終了日時
        /// </summary>
        public DateTimeOffset? LockoutEnd { get; set; }

        /// <summary>
        /// ロックアウト有効フラグ
        /// </summary>
        public bool LockoutEnabled { get; set; }

        /// <summary>
        /// アクセス失敗回数
        /// </summary>
        public int AccessFailedCount { get; set; }

        /// <summary>
        /// 作成日時
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 最終ログイン日時
        /// </summary>
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// Google連携状況
        /// </summary>
        public ExternalLinkInfo? GoogleLink { get; set; }

        /// <summary>
        /// ロール一覧
        /// </summary>
        public string[] Roles { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// 外部連携情報
    /// </summary>
    public class ExternalLinkInfo
    {
        /// <summary>
        /// プロバイダー名
        /// </summary>
        public string Provider { get; set; } = string.Empty;

        /// <summary>
        /// プロバイダーキー
        /// </summary>
        public string ProviderKey { get; set; } = string.Empty;

        /// <summary>
        /// 表示名
        /// </summary>
        public string? ProviderDisplayName { get; set; }

        /// <summary>
        /// 連携日時
        /// </summary>
        public DateTime LinkedAt { get; set; }

        /// <summary>
        /// 最終使用日時
        /// </summary>
        public DateTime? LastUsedAt { get; set; }
    }

    /// <summary>
    /// ユーザー外部連携状況DTO
    /// </summary>
    public class UserExternalLinkStatusDto
    {
        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// ユーザー名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// メールアドレス
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 外部連携一覧
        /// </summary>
        public ExternalLinkInfo[] ExternalLinks { get; set; } = Array.Empty<ExternalLinkInfo>();

        /// <summary>
        /// Google連携フラグ
        /// </summary>
        public bool HasGoogleLink => ExternalLinks.Any(l => l.Provider == "Google");

        /// <summary>
        /// 最終更新日時
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// ユーザー検索条件
    /// </summary>
    public class GetUsersWithExternalLinksInput : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// 検索キーワード
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// Google連携フィルター
        /// </summary>
        public bool? HasGoogleLink { get; set; }

        /// <summary>
        /// アクティブフィルター
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// ロールフィルター
        /// </summary>
        public string? RoleFilter { get; set; }

        /// <summary>
        /// 作成日時フィルター（開始）
        /// </summary>
        public DateTime? CreatedAfter { get; set; }

        /// <summary>
        /// 作成日時フィルター（終了）
        /// </summary>
        public DateTime? CreatedBefore { get; set; }
    }

    /// <summary>
    /// ユーザー通知送信入力
    /// </summary>
    public class SendUserNotificationInput
    {
        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 通知タイトル
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 通知内容
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 通知タイプ
        /// </summary>
        public NotificationType Type { get; set; }

        /// <summary>
        /// 緊急フラグ
        /// </summary>
        public bool IsUrgent { get; set; }

        /// <summary>
        /// 有効期限
        /// </summary>
        public DateTime? ExpiresAt { get; set; }
    }

    /// <summary>
    /// 一括通知送信入力
    /// </summary>
    public class SendBulkNotificationInput
    {
        /// <summary>
        /// 対象ユーザーID一覧
        /// </summary>
        public Guid[] UserIds { get; set; } = Array.Empty<Guid>();

        /// <summary>
        /// 通知タイトル
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 通知内容
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 通知タイプ
        /// </summary>
        public NotificationType Type { get; set; }

        /// <summary>
        /// 緊急フラグ
        /// </summary>
        public bool IsUrgent { get; set; }

        /// <summary>
        /// 有効期限
        /// </summary>
        public DateTime? ExpiresAt { get; set; }
    }

    /// <summary>
    /// 一括通知結果DTO
    /// </summary>
    public class BulkNotificationResultDto
    {
        /// <summary>
        /// 成功数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失敗数
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 総数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// エラー詳細
        /// </summary>
        public string[] Errors { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 送信日時
        /// </summary>
        public DateTime SentAt { get; set; }
    }

    /// <summary>
    /// ユーザーログイン履歴検索条件
    /// </summary>
    public class GetUserLoginHistoryInput : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// プロバイダーフィルター
        /// </summary>
        public string? Provider { get; set; }

        /// <summary>
        /// 成功フィルター
        /// </summary>
        public bool? IsSuccess { get; set; }

        /// <summary>
        /// 開始日時
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 終了日時
        /// </summary>
        public DateTime? EndDate { get; set; }
    }

    /// <summary>
    /// ユーザーログイン履歴DTO
    /// </summary>
    public class UserLoginHistoryDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// プロバイダー名
        /// </summary>
        public string Provider { get; set; } = string.Empty;

        /// <summary>
        /// プロバイダーキー
        /// </summary>
        public string? ProviderKey { get; set; }

        /// <summary>
        /// 成功フラグ
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// ユーザーエージェント
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// ログイン日時
        /// </summary>
        public DateTime LoginTime { get; set; }

        /// <summary>
        /// セッション継続時間
        /// </summary>
        public TimeSpan? SessionDuration { get; set; }
    }

    /// <summary>
    /// 通知タイプ
    /// </summary>
    public enum NotificationType
    {
        /// <summary>
        /// 情報
        /// </summary>
        Info,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// エラー
        /// </summary>
        Error,

        /// <summary>
        /// 成功
        /// </summary>
        Success,

        /// <summary>
        /// システム
        /// </summary>
        System
    }
}