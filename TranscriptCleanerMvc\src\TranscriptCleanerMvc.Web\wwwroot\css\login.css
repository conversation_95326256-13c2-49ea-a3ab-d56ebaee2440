/* シンプルで確実なログイン画面CSS */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
    box-sizing: border-box;
}

/* シンプルなコンテナ */
.login-wrapper {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
    max-width: none; /* 幅制限なし */
    width: auto; /* 自然な幅 */
}

/* ブランディングエリア */
.login-branding {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
    border-radius: 12px 12px 0 0;
}

.login-branding h1 {
    margin: 0 0 10px 0;
    font-size: 2rem;
    font-weight: 300;
}

.login-branding p {
    margin: 0;
    font-size: 1rem;
    opacity: 0.9;
}

/* ログインエリア */
.login-container {
    padding: 30px;
}

.app-title {
    text-align: center;
    font-size: 1.8rem;
    font-weight: 300;
    margin: 0 0 20px 0;
    color: #333;
}

/* 言語選択 */
.language-selector {
    text-align: right;
    margin-bottom: 25px;
}

.language-select {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    font-size: 0.9rem;
}

/* フォームスタイル */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #444;
}

.form-group input[type="text"],
.form-group input[type="password"] {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 1rem;
    box-sizing: border-box;
}

.form-group input:focus {
    border-color: #667eea;
    outline: none;
}

.form-group.checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-group.checkbox input {
    width: auto;
}

/* ログインボタン */
.login-button {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: 10px;
}

.login-button:hover {
    opacity: 0.95;
}

/* エラーメッセージ */
.error-message {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 20px;
}

/* ソーシャルログインセクション */
.social-login-section {
    margin-top: 30px;
}

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
}

.divider span {
    background: white;
    padding: 0 15px;
    color: #666;
    font-size: 0.9rem;
}

/* ソーシャルログインボタン */
.social-login-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.social-login-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 12px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: all 0.2s ease;
    background: white;
}

.social-login-button:hover {
    border-color: #667eea;
    background: #f8f9ff;
    text-decoration: none;
    color: #333;
}

.google-login {
    border-color: #4285f4;
}

.google-login:hover {
    border-color: #3367d6;
    background: #f1f5ff;
}

.google-icon {
    flex-shrink: 0;
}

/* エラーメッセージの改善 */
.error-message {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.error-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

/* レスポンシブ - 最小限 */
@media (max-width: 600px) {
    body {
        padding: 10px;
    }
    
    .login-branding,
    .login-container {
        padding: 20px;
    }
    
    .social-login-button {
        padding: 14px 16px;
        font-size: 0.95rem;
    }
}
