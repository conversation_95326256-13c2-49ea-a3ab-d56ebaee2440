<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TranscriptCleanerMvc</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\TranscriptCleanerMvc.Domain\TranscriptCleanerMvc.Domain.csproj" />
    <ProjectReference Include="..\TranscriptCleanerMvc.TestBase\TranscriptCleanerMvc.TestBase.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
  </ItemGroup>

</Project>
