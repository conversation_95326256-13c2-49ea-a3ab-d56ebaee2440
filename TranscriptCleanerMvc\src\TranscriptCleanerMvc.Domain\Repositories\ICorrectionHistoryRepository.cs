using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TranscriptCleanerMvc.Entities;
using TranscriptCleanerMvc.Transcripts;
using Volo.Abp.Domain.Repositories;

namespace TranscriptCleanerMvc.Repositories;

public interface ICorrectionHistoryRepository : IRepository<CorrectionHistory, Guid>
{
    Task<List<CorrectionHistory>> GetByTranscriptIdAsync(
        Guid transcriptId,
        CancellationToken cancellationToken = default
    );
    
    Task<List<CorrectionHistory>> GetListAsync(
        int skipCount = 0,
        int maxResultCount = 10,
        string? sorting = null,
        Guid? transcriptId = null,
        CorrectionSource? source = null,
        string? correctionType = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        CancellationToken cancellationToken = default
    );
    
    Task<long> GetCountAsync(
        Guid? transcriptId = null,
        CorrectionSource? source = null,
        string? correctionType = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        CancellationToken cancellationToken = default
    );
    
    Task<List<CorrectionHistory>> GetByUserIdAsync(
        Guid userId,
        int skipCount = 0,
        int maxResultCount = 10,
        CancellationToken cancellationToken = default
    );
}