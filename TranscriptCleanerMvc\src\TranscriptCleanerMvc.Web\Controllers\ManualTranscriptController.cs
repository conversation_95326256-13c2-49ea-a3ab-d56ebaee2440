using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TranscriptCleanerMvc.Localization;
using TranscriptCleanerMvc.Services;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace TranscriptCleanerMvc.Web.Controllers
{
    [ApiController]
    [Route("api/manual/transcript")]
    public class ManualTranscriptController : ControllerBase
    {
        private readonly IStringLocalizer<TranscriptCleanerMvcResource> _localizer;
        private readonly ILogger<ManualTranscriptController> _logger;
        private readonly OpenAITranscriptCorrectionService _correctionService;

        public ManualTranscriptController(
            IStringLocalizer<TranscriptCleanerMvcResource> localizer,
            ILogger<ManualTranscriptController> logger,
            OpenAITranscriptCorrectionService correctionService)
        {
            _localizer = localizer;
            _logger = logger;
            _correctionService = correctionService;
        }

        [HttpPost("correct")]
        [AllowAnonymous] // 開発時のみ
        [IgnoreAntiforgeryToken]
        public async Task<IActionResult> CorrectTranscriptManual([FromBody] ManualTranscriptCorrectionRequest request)
        {
            _logger.LogInformation("=== Correction endpoint called ===");
            
            try
            {
                _logger.LogInformation("Content-Type: {ContentType}", Request.ContentType);
                _logger.LogInformation("Content-Length: {ContentLength}", Request.ContentLength);
                _logger.LogInformation("Request Headers: {@Headers}", Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()));
                
                // モデルバインディングエラーをチェック
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage) })
                        .ToList();
                    
                    _logger.LogWarning("Model validation failed: {@Errors}", errors);
                    
                    return BadRequest(new { 
                        success = false, 
                        errorMessage = "リクエストデータの形式が正しくありません。",
                        validationErrors = errors
                    });
                }
                
                _logger.LogInformation("Request received: {RequestIsNull}", request == null ? "NULL" : "NOT NULL");
                
                if (request != null)
                {
                    _logger.LogInformation("Text Length: {TextLength}", request.Text?.Length ?? 0);
                    _logger.LogInformation("Language: {Language}", request.Language ?? "null");
                    _logger.LogInformation("CorrectionType: {CorrectionType}", request.CorrectionType ?? "null");
                    _logger.LogInformation("CustomPrompt Length: {CustomPromptLength}", request.CustomPrompt?.Length ?? 0);
                    _logger.LogInformation("WordList Length: {WordListLength}", request.WordList?.Length ?? 0);
                    _logger.LogInformation("Text preview: {TextPreview}", request.Text?.Substring(0, Math.Min(100, request.Text?.Length ?? 0)) ?? "null");
                }

                // リクエスト検証
                if (request == null)
                {
                    _logger.LogWarning("Request object is null after model binding");
                    return BadRequest(new { 
                        success = false, 
                        errorMessage = "Request data is missing or could not be parsed" 
                    });
                }

                if (string.IsNullOrWhiteSpace(request.Text))
                {
                    var errorMsg = request.Language == "ja" 
                        ? "テキストが入力されていません。" 
                        : "No text provided.";
                    
                    _logger.LogWarning("Empty text provided");
                    return BadRequest(new { 
                        success = false, 
                        errorMessage = errorMsg 
                    });
                }

                // OpenAI APIキーの確認
                var apiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY");
                _logger.LogInformation("OpenAI API key check: {HasKey}", !string.IsNullOrWhiteSpace(apiKey) ? "Found" : "Not found");
                
                if (string.IsNullOrWhiteSpace(apiKey))
                {
                    var errorMsg = request.Language == "ja" 
                        ? "OpenAI APIキーが設定されていません。環境変数 'OPENAI_API_KEY' を設定してアプリケーションを再起動してください。" 
                        : "OpenAI API key is not configured. Please set the 'OPENAI_API_KEY' environment variable and restart the application.";
                    
                    _logger.LogError("OpenAI API key not found in environment variables");
                    return BadRequest(new { 
                        success = false, 
                        errorMessage = errorMsg 
                    });
                }

                _logger.LogInformation("OpenAI API key found, proceeding with actual AI correction");

                // 実際のOpenAI API呼び出し
                var (correctedText, processingTimeMs, cost) = await _correctionService.CorrectTextAsync(
                    originalText: request.Text,
                    language: request.Language ?? "ja",
                    correctionType: request.CorrectionType ?? "comprehensive",
                    customPrompt: request.CustomPrompt,
                    wordListCsv: request.WordList
                );

                _logger.LogInformation("AI correction completed in {ProcessingTime}ms with cost ${Cost:F6}", 
                    processingTimeMs, cost ?? 0);

                return Ok(new {
                    success = true,
                    correctedText = correctedText,
                    originalLength = request.Text.Length,
                    correctedLength = correctedText.Length,
                    processingTimeMs = processingTimeMs,
                    cost = cost ?? 0m
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during manual transcript correction");
                
                var errorMessage = request?.Language == "ja" 
                    ? $"処理中にエラーが発生しました: {ex.Message}" 
                    : $"An error occurred during processing: {ex.Message}";
                    
                return StatusCode(500, new { 
                    success = false, 
                    errorMessage = errorMessage
                });
            }
        }
    }

    public class ManualTranscriptCorrectionRequest
    {
        public string Text { get; set; } = string.Empty;

        public string? Language { get; set; } = "ja";

        public string? CorrectionType { get; set; } = "comprehensive";

        public string? CustomPrompt { get; set; }

        public string? WordList { get; set; }
    }
}