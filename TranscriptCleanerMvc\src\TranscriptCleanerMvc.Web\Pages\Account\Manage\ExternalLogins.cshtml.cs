using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using TranscriptCleanerMvc.Identity;
using Volo.Abp.Identity;

namespace TranscriptCleanerMvc.Web.Pages.Account.Manage
{
    public class ExternalLoginsPageModel : PageModel
    {
        private readonly UserManager<Volo.Abp.Identity.IdentityUser> _userManager;
        private readonly SignInManager<Volo.Abp.Identity.IdentityUser> _signInManager;
        private readonly IAccountLinkService _accountLinkService;
        private readonly IProfileSyncService _profileSyncService;
        private readonly ILogger<ExternalLoginsPageModel> _logger;

        public ExternalLoginsPageModel(
            UserManager<Volo.Abp.Identity.IdentityUser> userManager,
            SignInManager<Volo.Abp.Identity.IdentityUser> signInManager,
            IAccountLinkService accountLinkService,
            IProfileSyncService profileSyncService,
            ILogger<ExternalLoginsPageModel> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _accountLinkService = accountLinkService;
            _profileSyncService = profileSyncService;
            _logger = logger;
        }

        public IList<UserLoginInfo> ExternalLogins { get; set; } = new List<UserLoginInfo>();
        public IList<AuthenticationScheme> OtherLogins { get; set; } = new List<AuthenticationScheme>();
        public bool ShowRemoveButton { get; set; }

        [BindProperty]
        public ProfileSyncSettingsDto SyncSettings { get; set; } = new();

        [TempData]
        public string? StatusMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            ExternalLogins = await _userManager.GetLoginsAsync(user);
            OtherLogins = (await _signInManager.GetExternalAuthenticationSchemesAsync())
                .Where(auth => ExternalLogins.All(ul => auth.Name != ul.LoginProvider))
                .ToList();

            ShowRemoveButton = user.PasswordHash != null || ExternalLogins.Count > 1;

            // プロフィール同期設定を取得
            try
            {
                SyncSettings = await _profileSyncService.GetProfileSyncSettingsAsync(user.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "プロフィール同期設定の取得に失敗しました。UserId: {UserId}", user.Id);
                SyncSettings = new ProfileSyncSettingsDto();
            }

            return Page();
        }

        public async Task<IActionResult> OnPostRemoveLoginAsync(string loginProvider, string providerKey)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            try
            {
                var result = await _userManager.RemoveLoginAsync(user, loginProvider, providerKey);
                if (!result.Succeeded)
                {
                    StatusMessage = "外部ログインの削除に失敗しました。";
                    return RedirectToPage();
                }

                // アカウント連携サービスでも解除
                await _accountLinkService.ForceUnlinkExternalAccountAsync(user.Id, loginProvider);

                await _signInManager.RefreshSignInAsync(user);
                StatusMessage = "外部ログインが削除されました。";
                
                _logger.LogInformation("外部ログインが削除されました。UserId: {UserId}, Provider: {Provider}", 
                    user.Id, loginProvider);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "外部ログイン削除中にエラーが発生しました。UserId: {UserId}, Provider: {Provider}", 
                    user.Id, loginProvider);
                StatusMessage = "外部ログインの削除中にエラーが発生しました。";
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostLinkLoginAsync(string provider)
        {
            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

            // Request a redirect to the external login provider to link a login for the current user
            var redirectUrl = Url.Page("./ExternalLogins", pageHandler: "LinkLoginCallback");
            var properties = _signInManager.ConfigureExternalAuthenticationProperties(provider, redirectUrl, _userManager.GetUserId(User));
            return new ChallengeResult(provider, properties);
        }

        public async Task<IActionResult> OnGetLinkLoginCallbackAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            var userId = await _userManager.GetUserIdAsync(user);
            var info = await _signInManager.GetExternalLoginInfoAsync(userId);
            if (info == null)
            {
                StatusMessage = "外部認証情報の読み込みに失敗しました。";
                return RedirectToPage();
            }

            try
            {
                // 連携の検証
                var validation = await _accountLinkService.ValidateAccountLinkAsync(
                    user.Id, info.LoginProvider, info.ProviderKey);

                if (!validation.IsValid)
                {
                    StatusMessage = $"アカウント連携に失敗しました: {string.Join(", ", validation.Errors)}";
                    return RedirectToPage();
                }

                var result = await _userManager.AddLoginAsync(user, info);
                if (!result.Succeeded)
                {
                    StatusMessage = "外部ログインの追加に失敗しました。";
                    return RedirectToPage();
                }

                // Clear the existing external cookie to ensure a clean login process
                await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

                StatusMessage = "外部ログインが追加されました。";
                
                _logger.LogInformation("外部ログインが追加されました。UserId: {UserId}, Provider: {Provider}", 
                    user.Id, info.LoginProvider);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "外部ログイン追加中にエラーが発生しました。UserId: {UserId}, Provider: {Provider}", 
                    user.Id, info?.LoginProvider);
                StatusMessage = "外部ログインの追加中にエラーが発生しました。";
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostUpdateSyncSettingsAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            try
            {
                var success = await _profileSyncService.UpdateProfileSyncSettingsAsync(user.Id, SyncSettings);
                if (success)
                {
                    StatusMessage = "同期設定が更新されました。";
                    _logger.LogInformation("プロフィール同期設定が更新されました。UserId: {UserId}", user.Id);
                }
                else
                {
                    StatusMessage = "同期設定の更新に失敗しました。";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同期設定更新中にエラーが発生しました。UserId: {UserId}", user.Id);
                StatusMessage = "同期設定の更新中にエラーが発生しました。";
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostSyncNowAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            try
            {
                var result = await _profileSyncService.SyncGoogleProfileAsync(user.Id);
                if (result.Success)
                {
                    var updatedFieldsText = result.UpdatedFields.Any() 
                        ? $"更新されたフィールド: {string.Join(", ", result.UpdatedFields)}"
                        : "更新されたフィールドはありません";
                    
                    StatusMessage = $"プロフィール同期が完了しました。{updatedFieldsText}";
                    _logger.LogInformation("手動プロフィール同期が完了しました。UserId: {UserId}, UpdatedFields: {UpdatedFields}", 
                        user.Id, string.Join(", ", result.UpdatedFields));
                }
                else
                {
                    StatusMessage = $"プロフィール同期に失敗しました: {result.ErrorMessage}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手動プロフィール同期中にエラーが発生しました。UserId: {UserId}", user.Id);
                StatusMessage = "プロフィール同期中にエラーが発生しました。";
            }

            return RedirectToPage();
        }
    }
}