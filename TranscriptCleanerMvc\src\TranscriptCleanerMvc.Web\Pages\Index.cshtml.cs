using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Users;

namespace TranscriptCleanerMvc.Web.Pages;

public class IndexModel : TranscriptCleanerMvcPageModel
{
    private readonly ICurrentUser _currentUser;

    public IndexModel(ICurrentUser currentUser)
    {
        _currentUser = currentUser;
    }

    public IActionResult OnGet()
    {
        // ログイン済みの場合はトランスクリプト訂正ページにリダイレクト
        if (_currentUser.IsAuthenticated)
        {
            return RedirectToPage("/TranscriptCorrection");
        }

        // 未ログインの場合はログインページにリダイレクト
        return RedirectToPage("/Account/Login");
    }
}
