using System;
using System.Threading.Tasks;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Media;
using TranscriptCleaner.WinUI.ViewModels;

namespace TranscriptCleaner.WinUI.Views;

public sealed partial class LoginPage : Page
{
    public LoginPageViewModel ViewModel { get; }

    public LoginPage()
    {
        this.InitializeComponent();
        ViewModel = new LoginPageViewModel();
        DataContext = ViewModel;
    }

    private async void OnLoginClicked(object sender, RoutedEventArgs e)
    {
        var username = UsernameTextBox.Text ?? string.Empty;
        var password = PasswordBox.Password ?? string.Empty;
        
        if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
        {
            await ShowMessageDialog("Error", "Please enter username and password.");
            return;
        }

        try
        {
            // Demo login process
            await Task.Delay(1000); // Simulate login processing

            if (ViewModel.IsValidLogin(username, password))
            {
                await ShowMessageDialog("Login Success", $"Welcome, {username}!");
                
                // Navigate to TranscriptPage
                Frame.Navigate(typeof(TranscriptPage));
            }
            else
            {
                await ShowMessageDialog("Login Error", "Username or password is incorrect.");
            }
        }
        catch (Exception ex)
        {
            await ShowMessageDialog("Error", $"Error during login process:\n{ex.Message}");
        }
    }

    private async Task ShowMessageDialog(string title, string message)
    {
        var dialog = new ContentDialog
        {
            Title = title,
            Content = message,
            CloseButtonText = "OK",
            XamlRoot = this.XamlRoot
        };
        _ = await dialog.ShowAsync();
    }

    private NavigationView? FindNavigationView(DependencyObject parent)
    {
        for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
        {
            var child = VisualTreeHelper.GetChild(parent, i);
            if (child is NavigationView navView)
                return navView;
            
            var result = FindNavigationView(child);
            if (result != null)
                return result;
        }
        return null;
    }

    private Frame? FindContentFrame(DependencyObject parent)
    {
        for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
        {
            var child = VisualTreeHelper.GetChild(parent, i);
            if (child is Frame frame && frame.Name == "ContentFrame")
                return frame;
            
            var result = FindContentFrame(child);
            if (result != null)
                return result;
        }
        return null;
    }
}