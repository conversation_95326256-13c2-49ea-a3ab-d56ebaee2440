using System;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// GoogleプロフィールDTO
    /// </summary>
    public class GoogleProfileDto
    {
        /// <summary>
        /// Google ID
        /// </summary>
        public string GoogleId { get; set; } = string.Empty;

        /// <summary>
        /// Googleメールアドレス
        /// </summary>
        public string GoogleEmail { get; set; } = string.Empty;

        /// <summary>
        /// 名前
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 姓
        /// </summary>
        public string Surname { get; set; } = string.Empty;

        /// <summary>
        /// プロフィール画像URL
        /// </summary>
        public string? ProfileImageUrl { get; set; }

        /// <summary>
        /// 最後の同期日時
        /// </summary>
        public DateTime? LastSyncTime { get; set; }

        /// <summary>
        /// 連携済みフラグ
        /// </summary>
        public bool IsLinked { get; set; }
    }
}