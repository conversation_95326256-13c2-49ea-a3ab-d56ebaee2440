﻿using System;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace TranscriptCleanerMvc.EntityFrameworkCore;

/* This class is needed for EF Core console commands
 * (like Add-Migration and Update-Database commands) */
public class TranscriptCleanerMvcDbContextFactory : IDesignTimeDbContextFactory<TranscriptCleanerMvcDbContext>
{
    public TranscriptCleanerMvcDbContext CreateDbContext(string[] args)
    {
        var configuration = BuildConfiguration();
        
        TranscriptCleanerMvcEfCoreEntityExtensionMappings.Configure();

        var builder = new DbContextOptionsBuilder<TranscriptCleanerMvcDbContext>()
            .UseSqlServer(configuration.GetConnectionString("Default"));
        
        return new TranscriptCleanerMvcDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "../TranscriptCleanerMvc.DbMigrator/"))
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}
