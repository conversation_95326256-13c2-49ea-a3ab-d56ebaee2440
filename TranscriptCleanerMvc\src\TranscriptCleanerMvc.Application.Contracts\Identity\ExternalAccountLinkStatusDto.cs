using System;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// 外部アカウント連携状況DTO
    /// </summary>
    public class ExternalAccountLinkStatusDto
    {
        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Google連携情報
        /// </summary>
        public GoogleLinkInfoDto? Google { get; set; }

        /// <summary>
        /// 連携済み外部アカウント数
        /// </summary>
        public int LinkedAccountsCount { get; set; }
    }

    /// <summary>
    /// Google連携情報DTO
    /// </summary>
    public class GoogleLinkInfoDto
    {
        /// <summary>
        /// 連携済みフラグ
        /// </summary>
        public bool IsLinked { get; set; }

        /// <summary>
        /// Googleメールアドレス
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 連携日時
        /// </summary>
        public DateTime? LinkedAt { get; set; }

        /// <summary>
        /// 最後の同期日時
        /// </summary>
        public DateTime? LastSyncTime { get; set; }
    }
}