using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// 外部ログインサービスインターフェース
    /// </summary>
    public interface IExternalLoginService : IApplicationService
    {
        /// <summary>
        /// Google認証コールバックを処理
        /// </summary>
        /// <param name="code">認証コード</param>
        /// <param name="state">状態パラメータ</param>
        /// <returns>認証結果</returns>
        Task<ExternalLoginResultDto> HandleGoogleCallbackAsync(string code, string state);

        /// <summary>
        /// Googleアカウントを既存ユーザーに連携
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="googleId">Google ID</param>
        /// <returns>連携成功フラグ</returns>
        Task<bool> LinkGoogleAccountAsync(Guid userId, string googleId);

        /// <summary>
        /// Googleアカウント連携を解除
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>解除成功フラグ</returns>
        Task<bool> UnlinkGoogleAccountAsync(Guid userId);

        /// <summary>
        /// Googleプロフィール情報を同期
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>同期されたプロフィール情報</returns>
        Task<GoogleProfileDto> SyncGoogleProfileAsync(Guid userId);

        /// <summary>
        /// ユーザーの外部アカウント連携状況を取得
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>連携状況</returns>
        Task<ExternalAccountLinkStatusDto> GetExternalAccountLinkStatusAsync(Guid userId);
    }
}