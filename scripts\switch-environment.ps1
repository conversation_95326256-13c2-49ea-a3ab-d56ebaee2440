# TranscriptCleaner Environment Switch Script
# Usage: ./switch-environment.ps1 [development|staging|production]

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("development", "staging", "production")]
    [string]$Environment
)

# Color output function
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

Write-ColorOutput Green "=== TranscriptCleaner Environment Switch ==="
Write-Host ""

# Project root directory
$ProjectRoot = Split-Path $PSScriptRoot -Parent
$WebProjectPath = Join-Path $ProjectRoot "TranscriptCleanerMvc\src\TranscriptCleanerMvc.Web"

# Environment configuration
switch ($Environment) {
    "development" {
        $ConfigSuffix = "Development"
        $ApiUrl = "https://localhost:44396"
        $Description = "Local Development Environment"
    }
    "staging" {
        $ConfigSuffix = "Staging"
        $ApiUrl = "https://staging.transcriptcleaner.com"
        $Description = "Staging Environment"
    }
    "production" {
        $ConfigSuffix = "Production"
        $ApiUrl = "https://api.transcriptcleaner.com"
        $Description = "Production Environment"
    }
}

Write-ColorOutput Cyan "Target Environment: $Description"
Write-ColorOutput Gray "API URL: $ApiUrl"
Write-Host ""

# Set environment variables
$env:ASPNETCORE_ENVIRONMENT = $Environment
$env:TRANSCRIPT_CLEANER_API_URL = $ApiUrl

Write-ColorOutput Green "Environment variables set:"
Write-ColorOutput Gray "   ASPNETCORE_ENVIRONMENT = $Environment"
Write-ColorOutput Gray "   TRANSCRIPT_CLEANER_API_URL = $ApiUrl"
Write-Host ""

# Update MAUI project configuration
$MauiServicePath = Join-Path $ProjectRoot "frontend-maui\TranscriptCleaner.Maui\Services\TranscriptCorrectionService.cs"
if (Test-Path $MauiServicePath) {
    try {
        $ServiceContent = Get-Content $MauiServicePath -Raw
        $UpdatedContent = $ServiceContent -replace 'private readonly string _baseUrl = ".*?"', "private readonly string _baseUrl = `"$ApiUrl`""
        $UpdatedContent | Out-File -FilePath $MauiServicePath -Encoding UTF8 -Force
        Write-ColorOutput Green "MAUI project API URL updated"
    } catch {
        Write-ColorOutput Yellow "Failed to update MAUI project: $($_.Exception.Message)"
    }
}

# Check secrets file
$SecretsPath = Join-Path $WebProjectPath "appsettings.secrets.json"
if (-not (Test-Path $SecretsPath) -and $Environment -eq "development") {
    Write-ColorOutput Yellow "OpenAI API key not configured"
    Write-ColorOutput Gray "   Run the following command to set API key:"
    Write-ColorOutput Gray "   .\scripts\setup-secrets.ps1"
    Write-Host ""
}

Write-ColorOutput Cyan "Next steps:"
Write-ColorOutput Gray "   1. Start backend: cd TranscriptCleanerMvc\src\TranscriptCleanerMvc.Web; dotnet run"
Write-ColorOutput Gray "   2. Start frontend: Run MAUI or WinUI application"
Write-Host ""

# Display current environment info
Write-ColorOutput Green "Current environment configuration:"
Write-ColorOutput Gray "   Environment: $env:ASPNETCORE_ENVIRONMENT"
Write-ColorOutput Gray "   API URL: $env:TRANSCRIPT_CLEANER_API_URL"