<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TranscriptCleanerMvc</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\TranscriptCleanerMvc.Application\TranscriptCleanerMvc.Application.csproj" />
    <ProjectReference Include="..\TranscriptCleanerMvc.Domain.Tests\TranscriptCleanerMvc.Domain.Tests.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
  </ItemGroup>

</Project>
