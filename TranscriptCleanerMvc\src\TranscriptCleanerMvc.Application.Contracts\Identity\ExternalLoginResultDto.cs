using System;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// 外部ログイン結果DTO
    /// </summary>
    public class ExternalLoginResultDto
    {
        /// <summary>
        /// 成功フラグ
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// アクセストークン
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// リフレッシュトークン
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// トークン有効期限
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// ユーザー情報
        /// </summary>
        public GoogleUserInfoDto? User { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 新規ユーザーかどうか
        /// </summary>
        public bool IsNewUser { get; set; }

        /// <summary>
        /// 成功結果を作成
        /// </summary>
        public static ExternalLoginResultDto CreateSuccess(
            string accessToken,
            GoogleUserInfoDto user,
            bool isNewUser = false,
            string? refreshToken = null,
            DateTime? expiresAt = null)
        {
            return new ExternalLoginResultDto
            {
                Success = true,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                ExpiresAt = expiresAt,
                User = user,
                IsNewUser = isNewUser
            };
        }

        /// <summary>
        /// 失敗結果を作成
        /// </summary>
        public static ExternalLoginResultDto CreateFailure(string errorMessage)
        {
            return new ExternalLoginResultDto
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }
}