using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TranscriptCleanerMvc.Entities;
using TranscriptCleanerMvc.Transcripts;
using Volo.Abp.Domain.Repositories;

namespace TranscriptCleanerMvc.Repositories;

public interface ITranscriptRepository : IRepository<Transcript, Guid>
{
    Task<List<Transcript>> GetListAsync(
        int skipCount = 0,
        int maxResultCount = 10,
        string? sorting = null,
        string? filter = null,
        TranscriptStatus? status = null,
        string? language = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        CancellationToken cancellationToken = default
    );
    
    Task<long> GetCountAsync(
        string? filter = null,
        TranscriptStatus? status = null,
        string? language = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        CancellationToken cancellationToken = default
    );
    
    Task<List<Transcript>> GetByUserIdAsync(
        Guid userId,
        int skipCount = 0,
        int maxResultCount = 10,
        CancellationToken cancellationToken = default
    );
    
    Task<List<Transcript>> GetRecentAsync(
        int count = 10,
        Guid? userId = null,
        CancellationToken cancellationToken = default
    );
    
    Task<Transcript?> GetWithHistoryAsync(
        Guid id,
        CancellationToken cancellationToken = default
    );
}