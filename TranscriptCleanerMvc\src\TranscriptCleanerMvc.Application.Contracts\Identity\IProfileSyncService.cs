using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// プロフィール同期サービスインターフェース
    /// </summary>
    public interface IProfileSyncService : IApplicationService
    {
        /// <summary>
        /// Googleプロフィールを手動同期
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>同期結果</returns>
        Task<ProfileSyncResultDto> SyncGoogleProfileAsync(Guid userId);

        /// <summary>
        /// 自動プロフィール同期を実行
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>同期結果</returns>
        Task<ProfileSyncResultDto> AutoSyncProfileAsync(Guid userId);

        /// <summary>
        /// プロフィール同期履歴を取得
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="maxCount">最大取得件数</param>
        /// <returns>同期履歴</returns>
        Task<ProfileSyncHistoryDto[]> GetProfileSyncHistoryAsync(Guid userId, int maxCount = 10);

        /// <summary>
        /// プロフィール同期設定を取得
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>同期設定</returns>
        Task<ProfileSyncSettingsDto> GetProfileSyncSettingsAsync(Guid userId);

        /// <summary>
        /// プロフィール同期設定を更新
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="settings">同期設定</param>
        /// <returns>更新成功フラグ</returns>
        Task<bool> UpdateProfileSyncSettingsAsync(Guid userId, ProfileSyncSettingsDto settings);
    }
}