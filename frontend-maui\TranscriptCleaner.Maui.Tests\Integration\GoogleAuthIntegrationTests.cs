using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using Xunit;
using TranscriptCleaner.Maui.Services;

namespace TranscriptCleaner.Maui.Tests.Integration
{
    /// <summary>
    /// MAUI アプリケーションのGoogle認証統合テスト
    /// </summary>
    public class GoogleAuthIntegrationTests : MauiTestBase
    {
        private readonly IGoogleAuthService _googleAuthService;
        private readonly IAuthStateManager _authStateManager;
        private readonly IJwtTokenService _jwtTokenService;
        private readonly IAuthSyncService _authSyncService;
        private readonly INetworkService _networkService;

        public GoogleAuthIntegrationTests()
        {
            _googleAuthService = GetRequiredService<IGoogleAuthService>();
            _authStateManager = GetRequiredService<IAuthStateManager>();
            _jwtTokenService = GetRequiredService<IJwtTokenService>();
            _authSyncService = GetRequiredService<IAuthSyncService>();
            _networkService = GetRequiredService<INetworkService>();
        }

        [Fact]
        public async Task GoogleAuth_CompleteFlow_ShouldAuthenticateSuccessfully()
        {
            // Arrange
            var isNetworkAvailable = await _networkService.IsConnectedAsync();
            isNetworkAvailable.ShouldBeTrue("Network connection required for integration test");

            // Act - Step 1: Google認証開始
            var authResult = await _googleAuthService.SignInAsync();

            // Assert - 認証成功
            authResult.ShouldNotBeNull();
            authResult.IsSuccess.ShouldBeTrue();
            authResult.AccessToken.ShouldNotBeNullOrEmpty();
            authResult.IdToken.ShouldNotBeNullOrEmpty();
            authResult.UserInfo.ShouldNotBeNull();
            authResult.UserInfo.Email.ShouldNotBeNullOrEmpty();

            // Act - Step 2: 認証状態の更新
            await _authStateManager.SetAuthenticationStateAsync(authResult);

            // Assert - 認証状態確認
            var isAuthenticated = await _authStateManager.IsAuthenticatedAsync();
            isAuthenticated.ShouldBeTrue();

            var currentUser = await _authStateManager.GetCurrentUserAsync();
            currentUser.ShouldNotBeNull();
            currentUser.Email.ShouldBe(authResult.UserInfo.Email);

            // Act - Step 3: JWTトークンの処理
            var jwtResult = await _jwtTokenService.ProcessTokenAsync(authResult.IdToken);

            // Assert - JWT処理成功
            jwtResult.ShouldNotBeNull();
            jwtResult.IsValid.ShouldBeTrue();
            jwtResult.Claims.ShouldNotBeEmpty();
            jwtResult.ExpiresAt.ShouldBeGreaterThan(DateTime.UtcNow);

            // Act - Step 4: サーバーとの同期
            var syncResult = await _authSyncService.SyncWithServerAsync();

            // Assert - 同期成功
            syncResult.ShouldNotBeNull();
            syncResult.IsSuccess.ShouldBeTrue();
            syncResult.ServerUserId.ShouldNotBe(Guid.Empty);
        }

        [Fact]
        public async Task GoogleAuth_OfflineMode_ShouldUseOfflineAuth()
        {
            // Arrange - ネットワークを無効化（モック）
            await _networkService.SetNetworkStateAsync(false);

            // 事前にオンライン認証を実行してオフラインデータを準備
            await _networkService.SetNetworkStateAsync(true);
            var onlineAuthResult = await _googleAuthService.SignInAsync();
            onlineAuthResult.IsSuccess.ShouldBeTrue();
            await _authStateManager.SetAuthenticationStateAsync(onlineAuthResult);

            // ネットワークを再度無効化
            await _networkService.SetNetworkStateAsync(false);

            // Act - オフライン認証の試行
            var offlineAuthService = GetRequiredService<IOfflineAuthService>();
            var offlineResult = await offlineAuthService.AuthenticateOfflineAsync();

            // Assert - オフライン認証成功
            offlineResult.ShouldNotBeNull();
            offlineResult.IsSuccess.ShouldBeTrue();
            offlineResult.IsOfflineMode.ShouldBeTrue();
            offlineResult.UserInfo.ShouldNotBeNull();

            // 認証状態確認
            var isAuthenticated = await _authStateManager.IsAuthenticatedAsync();
            isAuthenticated.ShouldBeTrue();
        }

        [Fact]
        public async Task GoogleAuth_TokenRefresh_ShouldUpdateTokens()
        {
            // Arrange - 初回認証
            var initialAuthResult = await _googleAuthService.SignInAsync();
            initialAuthResult.IsSuccess.ShouldBeTrue();
            await _authStateManager.SetAuthenticationStateAsync(initialAuthResult);

            // 期限切れ間近のトークンをシミュレート
            var expiredToken = CreateExpiredToken(initialAuthResult.AccessToken);
            await _jwtTokenService.StoreTokenAsync("access_token", expiredToken);

            // Act - トークンリフレッシュ
            var refreshResult = await _googleAuthService.RefreshTokenAsync();

            // Assert - リフレッシュ成功
            refreshResult.ShouldNotBeNull();
            refreshResult.IsSuccess.ShouldBeTrue();
            refreshResult.AccessToken.ShouldNotBe(initialAuthResult.AccessToken);
            refreshResult.AccessToken.ShouldNotBeNullOrEmpty();

            // 新しいトークンが有効であることを確認
            var tokenValidation = await _jwtTokenService.ValidateTokenAsync(refreshResult.AccessToken);
            tokenValidation.IsValid.ShouldBeTrue();
        }

        [Fact]
        public async Task GoogleAuth_SignOut_ShouldClearAllAuthData()
        {
            // Arrange - 認証状態を設定
            var authResult = await _googleAuthService.SignInAsync();
            authResult.IsSuccess.ShouldBeTrue();
            await _authStateManager.SetAuthenticationStateAsync(authResult);

            // 認証状態確認
            var isAuthenticatedBefore = await _authStateManager.IsAuthenticatedAsync();
            isAuthenticatedBefore.ShouldBeTrue();

            // Act - サインアウト
            var signOutResult = await _googleAuthService.SignOutAsync();

            // Assert - サインアウト成功
            signOutResult.ShouldNotBeNull();
            signOutResult.IsSuccess.ShouldBeTrue();

            // 認証状態がクリアされていることを確認
            var isAuthenticatedAfter = await _authStateManager.IsAuthenticatedAsync();
            isAuthenticatedAfter.ShouldBeFalse();

            var currentUser = await _authStateManager.GetCurrentUserAsync();
            currentUser.ShouldBeNull();

            // トークンがクリアされていることを確認
            var storedToken = await _jwtTokenService.GetStoredTokenAsync("access_token");
            storedToken.ShouldBeNullOrEmpty();
        }

        [Fact]
        public async Task GoogleAuth_NetworkReconnection_ShouldSyncPendingChanges()
        {
            // Arrange - オフライン状態で認証
            await _networkService.SetNetworkStateAsync(false);
            var offlineAuthService = GetRequiredService<IOfflineAuthService>();
            
            // オンライン認証データを事前準備
            await _networkService.SetNetworkStateAsync(true);
            var onlineAuth = await _googleAuthService.SignInAsync();
            await _authStateManager.SetAuthenticationStateAsync(onlineAuth);
            
            // オフライン状態に戻す
            await _networkService.SetNetworkStateAsync(false);
            
            // オフラインでの変更をシミュレート
            await offlineAuthService.UpdateOfflineUserDataAsync(new OfflineUserData
            {
                LastLoginTime = DateTime.UtcNow,
                PreferredLanguage = "ja",
                AppSettings = new Dictionary<string, object>
                {
                    { "theme", "dark" },
                    { "notifications", true }
                }
            });

            // Act - ネットワーク再接続
            await _networkService.SetNetworkStateAsync(true);
            var syncResult = await _authSyncService.SyncWithServerAsync();

            // Assert - 同期成功
            syncResult.ShouldNotBeNull();
            syncResult.IsSuccess.ShouldBeTrue();
            syncResult.SyncedChanges.ShouldBeGreaterThan(0);
            syncResult.ConflictsResolved.ShouldBeGreaterThanOrEqualTo(0);
        }

        [Fact]
        public async Task GoogleAuth_ErrorHandling_ShouldHandleGracefully()
        {
            // Arrange - 無効な設定でGoogle認証を試行
            var invalidGoogleAuthService = CreateInvalidGoogleAuthService();

            // Act - 認証エラーのテスト
            var authResult = await invalidGoogleAuthService.SignInAsync();

            // Assert - エラーハンドリング
            authResult.ShouldNotBeNull();
            authResult.IsSuccess.ShouldBeFalse();
            authResult.ErrorCode.ShouldNotBeNullOrEmpty();
            authResult.ErrorMessage.ShouldNotBeNullOrEmpty();
            authResult.IsUserFriendly.ShouldBeTrue();

            // 認証状態が変更されていないことを確認
            var isAuthenticated = await _authStateManager.IsAuthenticatedAsync();
            isAuthenticated.ShouldBeFalse();
        }

        [Fact]
        public async Task GoogleAuth_BiometricAuth_ShouldEnhanceSecurity()
        {
            // Arrange - 生体認証が利用可能な場合
            var biometricService = GetRequiredService<IBiometricAuthService>();
            var isBiometricAvailable = await biometricService.IsAvailableAsync();

            if (!isBiometricAvailable)
            {
                // 生体認証が利用できない環境ではスキップ
                return;
            }

            // 初回Google認証
            var googleAuthResult = await _googleAuthService.SignInAsync();
            googleAuthResult.IsSuccess.ShouldBeTrue();

            // Act - 生体認証の設定
            var biometricSetupResult = await biometricService.SetupBiometricAuthAsync(
                googleAuthResult.UserInfo.Id);

            // Assert - 生体認証設定成功
            biometricSetupResult.ShouldNotBeNull();
            biometricSetupResult.IsSuccess.ShouldBeTrue();

            // Act - 生体認証でのログイン
            var biometricAuthResult = await biometricService.AuthenticateAsync();

            // Assert - 生体認証成功
            biometricAuthResult.ShouldNotBeNull();
            biometricAuthResult.IsSuccess.ShouldBeTrue();
            biometricAuthResult.AuthMethod.ShouldBe("Biometric");
        }

        [Fact]
        public async Task GoogleAuth_MultipleAccounts_ShouldHandleAccountSwitching()
        {
            // Arrange - 複数のGoogleアカウント
            var account1Result = await _googleAuthService.SignInAsync();
            account1Result.IsSuccess.ShouldBeTrue();
            
            var account1Info = account1Result.UserInfo;
            await _authStateManager.SetAuthenticationStateAsync(account1Result);

            // サインアウト
            await _googleAuthService.SignOutAsync();

            // 別のアカウントでサインイン（テスト環境では同じアカウントをシミュレート）
            var account2Result = await _googleAuthService.SignInWithAccountAsync("different_account_hint");

            // Act - アカウント切り替え
            if (account2Result.IsSuccess)
            {
                await _authStateManager.SetAuthenticationStateAsync(account2Result);
            }

            // Assert - アカウント切り替え成功
            var currentUser = await _authStateManager.GetCurrentUserAsync();
            currentUser.ShouldNotBeNull();
            
            // アカウント履歴の確認
            var accountHistory = await _authStateManager.GetAccountHistoryAsync();
            accountHistory.ShouldNotBeEmpty();
        }

        [Fact]
        public async Task GoogleAuth_DataProtection_ShouldEncryptSensitiveData()
        {
            // Arrange
            var authResult = await _googleAuthService.SignInAsync();
            authResult.IsSuccess.ShouldBeTrue();

            // Act - 機密データの保存
            await _authStateManager.SetAuthenticationStateAsync(authResult);

            // Assert - データが暗号化されて保存されていることを確認
            var dataProtectionService = GetRequiredService<IDataProtectionService>();
            var storedData = await dataProtectionService.GetProtectedDataAsync("auth_tokens");
            
            storedData.ShouldNotBeNull();
            storedData.IsEncrypted.ShouldBeTrue();
            storedData.EncryptionMethod.ShouldNotBeNullOrEmpty();

            // 復号化テスト
            var decryptedData = await dataProtectionService.DecryptDataAsync(storedData);
            decryptedData.ShouldNotBeNull();
            decryptedData.ShouldContain("access_token");
        }

        private IGoogleAuthService CreateInvalidGoogleAuthService()
        {
            // テスト用の無効な設定を持つGoogleAuthServiceを作成
            var serviceCollection = new ServiceCollection();
            serviceCollection.AddSingleton<IGoogleAuthService, GoogleAuthService>();
            
            // 無効な設定を注入
            serviceCollection.Configure<GoogleAuthOptions>(options =>
            {
                options.ClientId = "invalid_client_id";
                options.ClientSecret = "invalid_client_secret";
                options.RedirectUri = "invalid://redirect";
            });

            var serviceProvider = serviceCollection.BuildServiceProvider();
            return serviceProvider.GetRequiredService<IGoogleAuthService>();
        }

        private string CreateExpiredToken(string originalToken)
        {
            // テスト用の期限切れトークンを作成
            // 実際の実装では、JWTライブラリを使用してトークンを操作
            return originalToken + "_expired";
        }
    }
}