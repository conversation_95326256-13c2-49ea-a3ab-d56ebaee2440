

namespace TranscriptCleaner.Maui.Services;

/// <summary>
/// Google認証サービスインターフェース
/// </summary>
public interface IGoogleAuthService
{
    /// <summary>
    /// Googleアカウントでログイン
    /// </summary>
    /// <returns>認証結果</returns>
    Task<GoogleAuthResult> LoginWithGoogleAsync();

    /// <summary>
    /// Googleアカウントが連携されているかチェック
    /// </summary>
    /// <returns>連携状態</returns>
    Task<bool> IsGoogleLinkedAsync();

    /// <summary>
    /// 現在のユーザーにGoogleアカウントを連携
    /// </summary>
    /// <returns>連携結果</returns>
    Task<bool> LinkGoogleAccountAsync();

    /// <summary>
    /// Googleアカウント連携を解除
    /// </summary>
    /// <returns>解除結果</returns>
    Task<bool> UnlinkGoogleAccountAsync();

    /// <summary>
    /// ログアウト
    /// </summary>
    /// <returns>ログアウト結果</returns>
    Task LogoutAsync();

    /// <summary>
    /// 認証状態を確認
    /// </summary>
    /// <returns>認証済みかどうか</returns>
    bool IsAuthenticated { get; }

    /// <summary>
    /// 現在のユーザー情報
    /// </summary>
    GoogleUserInfo? CurrentUser { get; }
}

/// <summary>
/// Google認証結果
/// </summary>
public class GoogleAuthResult
{
    /// <summary>
    /// 成功フラグ
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// エラーメッセージ
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// ユーザー情報
    /// </summary>
    public GoogleUserInfo? User { get; set; }

    /// <summary>
    /// 新規ユーザーフラグ
    /// </summary>
    public bool IsNewUser { get; set; }

    /// <summary>
    /// アクセストークン
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// 成功結果を作成
    /// </summary>
    public static GoogleAuthResult CreateSuccess(GoogleUserInfo user, bool isNewUser = false, string? accessToken = null)
    {
        return new GoogleAuthResult
        {
            Success = true,
            User = user,
            IsNewUser = isNewUser,
            AccessToken = accessToken
        };
    }

    /// <summary>
    /// 失敗結果を作成
    /// </summary>
    public static GoogleAuthResult CreateFailure(string errorMessage)
    {
        return new GoogleAuthResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// Googleユーザー情報
/// </summary>
public class GoogleUserInfo
{
    /// <summary>
    /// Google ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// メールアドレス
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 表示名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 名前
    /// </summary>
    public string GivenName { get; set; } = string.Empty;

    /// <summary>
    /// 姓
    /// </summary>
    public string FamilyName { get; set; } = string.Empty;

    /// <summary>
    /// プロフィール画像URL
    /// </summary>
    public string? Picture { get; set; }

    /// <summary>
    /// メールアドレス確認済みフラグ
    /// </summary>
    public bool EmailVerified { get; set; }
}