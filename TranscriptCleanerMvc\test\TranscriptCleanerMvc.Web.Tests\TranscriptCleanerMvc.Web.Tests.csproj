<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <OutputType>Exe</OutputType>
    <AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <RootNamespace>TranscriptCleanerMvc</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.72" />
    <PackageReference Include="Volo.Abp.AspNetCore.TestBase" Version="9.2.0" />
    <PackageReference Include="AngleSharp" Version="1.1.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.4" />
    <ProjectReference Include="..\TranscriptCleanerMvc.Application.Tests\TranscriptCleanerMvc.Application.Tests.csproj" />
    <ProjectReference Include="..\..\src\TranscriptCleanerMvc.Web\TranscriptCleanerMvc.Web.csproj" />
    <ProjectReference Include="..\TranscriptCleanerMvc.EntityFrameworkCore.Tests\TranscriptCleanerMvc.EntityFrameworkCore.Tests.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="xunit.runner.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <!-- https://github.com/NuGet/Home/issues/4412 -->
  <Target Name="CopyDepsFiles" AfterTargets="Build" Condition="'$(TargetFramework)'!=''">
    <ItemGroup>
      <DepsFilePaths Include="$([System.IO.Path]::ChangeExtension('%(_ResolvedProjectReferencePaths.FullPath)', '.deps.json'))" />
    </ItemGroup>

    <Copy SourceFiles="%(DepsFilePaths.FullPath)" DestinationFolder="$(OutputPath)" Condition="Exists('%(DepsFilePaths.FullPath)')" />
  </Target>

</Project>
