# 実装計画

- [x] 1. 開発環境準備と SDK 更新

  - .NET 9.0 SDK の確認・更新
  - ABP Framework パッケージの最新版への更新

  - Google Cloud Console プロジェクトの作成と設定

  - _要件: 8.1, 8.2, 8.3_

- [x] 2. データベーススキーマ拡張

  - [x] 2.1 AppUser エンティティの拡張

    - GoogleId、GoogleEmail、ProfileImageUrl 等のプロパティ追加
    - Entity Framework Core マイグレーション作成
    - _要件: 5.1, 5.2_

  - [x] 2.2 外部ログイン履歴テーブルの作成

    - ExternalLoginLogs テーブルの作成
    - 適切なインデックスの設定
    - _要件: 7.5_

- [x] 3. サーバーサイド基盤実装

  - [x] 3.1 Google 認証設定の追加

    - TranscriptCleanerMvcWebModule に Google 認証設定を追加
    - appsettings.json に Google OAuth 設定セクション追加
    - 環境変数サポートの実装
    - _要件: 1.6, 1.7, 8.1, 8.2, 8.3_

  - [x] 3.2 外部ログインサービスの実装

    - IExternalLoginService インターフェースの作成
    - Google 認証フロー処理の実装
    - ユーザー情報取得と同期機能の実装
    - _要件: 1.1, 1.2, 1.3, 5.1, 5.2_

  - [x] 3.3 外部認証コントローラーの実装

    - ExternalAuthController の作成
    - Google 認証エンドポイントの実装
    - 認証コールバック処理の実装
    - _要件: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 4. アカウント連携機能の実装

  - [x] 4.1 アカウント連携サービスの実装

    - 既存アカウントと Google アカウントの連携機能
    - 連携解除機能の実装
    - 重複チェック機能の実装
    - _要件: 4.1, 4.2, 4.3, 4.4, 4.5_

  - [x] 4.2 プロフィール同期機能の実装

    - Google からのプロフィール情報取得
    - プロフィール画像 URL 同期
    - 定期同期機能の実装
    - _要件: 5.1, 5.2, 5.4, 5.5_

- [x] 5. Web クライアント対応

  - [x] 5.1 ログインページの拡張

    - Google ログインボタンの追加
    - 認証フロー処理の実装
    - エラーハンドリングの実装
    - _要件: 1.1, 1.2, 1.3, 1.5_

  - [x] 5.2 アカウント管理ページの実装

    - 外部アカウント連携管理画面の作成
    - 連携状況表示機能
    - 連携・解除操作の実装
    - _要件: 4.1, 4.2, 4.3, 4.4_

- [x] 6. MAUI クライアント対応

  - [x] 6.1 Google 認証サービスの実装

    - IGoogleAuthService インターフェースの作成
    - プラットフォーム固有の認証実装
    - セキュアストレージ統合
    - _要件: 9.1, 9.2, 9.3, 9.6_

  - [x] 6.2 認証フロー統合

    - システムブラウザー認証の実装
    - アプリ復帰時の認証状態処理
    - ネットワークエラーハンドリング
    - _要件: 9.2, 9.4, 9.5_

  - [x] 6.3 オフライン対応の実装

    - 認証情報キャッシュ機能
    - オフライン状態での認証復元
    - ネットワーク復旧時の同期処理
    - _要件: 12.1, 12.2, 12.3, 12.4, 12.5_

- [x] 7. WinUI クライアント対応

  - [x] 7.1 WinUI Google 認証サービスの実装

    - WinUI 固有の認証サービス実装
    - WebView2 統合による認証フロー
    - Windows 固有のセキュアストレージ活用
    - _要件: 10.1, 10.2, 10.3, 10.5_

  - [x] 7.2 認証状態管理の実装

    - アプリケーション最小化/復元時の状態維持
    - ローカル認証情報管理
    - キャッシュクリア機能
    - _要件: 10.4, 10.5_

- [x] 8. クライアント・サーバー認証連携


  - [x] 8.1 JWT トークン管理の実装

    - ABP Framework JWT トークン取得
    - トークン自動更新機能
    - リフレッシュトークン処理
    - _要件: 11.2, 11.3, 11.4, 11.5_

  - [x] 8.2 認証状態同期の実装

    - 複数クライアント間の認証状態同期
    - サーバー接続確認機能
    - 一貫した認証体験の提供
    - _要件: 11.1, 11.6_

- [x] 9. 管理機能の実装




  - [x] 9.1 管理者設定画面の実装



    - Google 認証有効/無効設定
    - 管理者向け設定インターフェース
    - 設定変更の即座反映
    - _要件: 7.1, 7.2_

  - [x] 9.2 ユーザー管理機能の拡張


    - ユーザーの Google 連携状況表示
    - 管理者による強制連携解除機能
    - ユーザー通知機能
    - _要件: 7.3, 7.4_

- [x] 10. セキュリティとエラーハンドリング





  - [x] 10.1 セキュリティ機能の実装





    - OAuth 2.0/OpenID Connect 標準準拠
    - PKCE、State、Nonce 実装
    - HTTPS 強制設定
    - _要件: 6.1, 6.2_

  - [x] 10.2 エラーハンドリングの実装




    - 包括的なエラーハンドリング
    - ログ記録機能
    - ユーザーフレンドリーなエラーメッセージ
    - _要件: 1.5, 6.4, 6.5, 7.5_

  - [x] 10.3 監査ログの実装


    - 外部認証イベントのログ記録
    - セキュリティ監査機能
    - 管理者向けアラート機能
    - _要件: 6.3, 7.5_

- [x] 11. テスト実装




  - [x] 11.1 単体テストの作成





    - ExternalLoginService のテスト
    - ExternalAuthController のテスト
    - 認証フロー関連のテスト
    - _要件: 全要件のテストカバレッジ_

  - [x] 11.2 統合テストの作成




    - Google 認証フロー統合テスト
    - データベース操作テスト
    - API エンドポイントテスト
    - _要件: 全要件の統合テスト_

  - [x] 11.3 E2E テストの作成


    - Web クライアント認証フローテスト
    - MAUI クライアント認証テスト
    - WinUI クライアント認証テスト
    - _要件: 1, 9, 10 の E2E テスト_

- [x] 12. ドキュメントと設定ガイド


  - [x] 12.1 Google Cloud Console 設定ガイドの作成



    - OAuth 2.0 クライアント作成手順
    - リダイレクト URI 設定方法
    - 環境別設定ガイド
    - _要件: 8.4, 8.5_

  - [x] 12.2 開発者向けドキュメントの作成




    - 実装ガイドの作成
    - トラブルシューティングガイド
    - セキュリティベストプラクティス
    - _要件: 全要件の実装ガイド_

  - [x] 12.3 ユーザー向けドキュメントの作成


    - Google 認証使用方法ガイド
    - アカウント連携手順
    - よくある質問とその回答
    - _要件: 1, 4, 5 の使用方法_
