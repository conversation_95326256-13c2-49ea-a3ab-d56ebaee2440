﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TranscriptCleanerMvc</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\TranscriptCleanerMvc.Domain\TranscriptCleanerMvc.Domain.csproj" />
    <ProjectReference Include="..\TranscriptCleanerMvc.Application.Contracts\TranscriptCleanerMvc.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.2.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.2.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.2.0" />
    <PackageReference Include="Volo.Abp.Account.Application" Version="9.2.0" />
    <PackageReference Include="Volo.Abp.Identity.Application" Version="9.2.0" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application" Version="9.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="OpenAI" Version="2.1.0" />
  </ItemGroup>

</Project>
