using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TranscriptCleanerMvc.Entities;
using TranscriptCleanerMvc.Repositories;
using TranscriptCleanerMvc.Services;
using TranscriptCleanerMvc.Transcripts;
using TranscriptCleanerMvc.Transcripts.Dtos;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Linq;
using Volo.Abp.Users;

namespace TranscriptCleanerMvc.Transcripts;

public class TranscriptAppService : CrudAppService<
    Transcript,
    TranscriptDto,
    Guid,
    TranscriptFilterDto,
    CreateTranscriptDto,
    UpdateTranscriptDto>, ITranscriptAppService
{
    private readonly ITranscriptRepository _transcriptRepository;
    private readonly OpenAITranscriptCorrectionService _correctionService;

    public TranscriptAppService(
        IRepository<Transcript, Guid> repository,
        ITranscriptRepository transcriptRepository,
        OpenAITranscriptCorrectionService correctionService)
        : base(repository)
    {
        _transcriptRepository = transcriptRepository;
        _correctionService = correctionService;
    }

    [AllowAnonymous] // 開発時のみ - 本番では削除
    public async Task<TranscriptCorrectionResponseDto> CorrectTranscriptAsync(TranscriptCorrectionRequestDto input)
    {
        var transcript = new Transcript(
            GuidGenerator.Create(),
            input.Title ?? "Untitled Transcript",
            input.Text,
            input.Language,
            input.CorrectionType
        );

        try
        {
            transcript.Status = TranscriptStatus.Processing;
            await _transcriptRepository.InsertAsync(transcript);

            var (correctedText, processingTime, cost) = await _correctionService.CorrectTextAsync(
                input.Text,
                input.Language,
                input.CorrectionType,
                input.CustomPrompt,
                input.WordList
            );

            transcript.SetCorrectedText(correctedText, processingTime, cost);
            await _transcriptRepository.UpdateAsync(transcript);

            return new TranscriptCorrectionResponseDto
            {
                TranscriptId = transcript.Id,
                Success = true,
                CorrectedText = correctedText,
                OriginalLength = transcript.OriginalLength,
                CorrectedLength = transcript.CorrectedLength,
                ProcessingTimeMs = processingTime,
                ProcessingCost = cost
            };
        }
        catch (Exception ex)
        {
            transcript.SetError(ex.Message);
            await _transcriptRepository.UpdateAsync(transcript);

            return new TranscriptCorrectionResponseDto
            {
                TranscriptId = transcript.Id,
                Success = false,
                ErrorMessage = ex.Message,
                OriginalLength = transcript.OriginalLength,
                CorrectedText = string.Empty
            };
        }
    }

    public async Task<PagedResultDto<TranscriptDto>> GetMyTranscriptsAsync(TranscriptFilterDto input)
    {
        var currentUserId = CurrentUser.GetId();
        
        var status = !string.IsNullOrEmpty(input.Status) && Enum.TryParse<TranscriptStatus>(input.Status, out var parsedStatus) 
            ? parsedStatus 
            : (TranscriptStatus?)null;

        var transcripts = await _transcriptRepository.GetListAsync(
            skipCount: input.SkipCount,
            maxResultCount: input.MaxResultCount,
            sorting: input.Sorting,
            filter: input.Filter,
            status: status,
            language: input.Language,
            createdAfter: input.CreatedAfter,
            createdBefore: input.CreatedBefore
        );

        // Filter by current user
        transcripts = transcripts.Where(x => x.CreatorId == currentUserId).ToList();

        var totalCount = await _transcriptRepository.GetCountAsync(
            filter: input.Filter,
            status: status,
            language: input.Language,
            createdAfter: input.CreatedAfter,
            createdBefore: input.CreatedBefore
        );

        return new PagedResultDto<TranscriptDto>(
            totalCount,
            ObjectMapper.Map<List<Transcript>, IReadOnlyList<TranscriptDto>>(transcripts)
        );
    }

    public async Task<ListResultDto<TranscriptDto>> GetRecentTranscriptsAsync(int count = 10)
    {
        var currentUserId = CurrentUser.GetId();
        var transcripts = await _transcriptRepository.GetRecentAsync(count, currentUserId);
        
        return new ListResultDto<TranscriptDto>(
            ObjectMapper.Map<List<Transcript>, List<TranscriptDto>>(transcripts)
        );
    }

    public async Task<TranscriptDto> GetWithHistoryAsync(Guid id)
    {
        var transcript = await _transcriptRepository.GetWithHistoryAsync(id);
        if (transcript == null)
        {
            throw new Volo.Abp.BusinessException("Transcript not found");
        }

        return ObjectMapper.Map<Transcript, TranscriptDto>(transcript);
    }

    public async Task<TranscriptDto> ReprocessAsync(Guid id)
    {
        var transcript = await _transcriptRepository.GetAsync(id);
        
        try
        {
            transcript.Status = TranscriptStatus.Processing;
            await _transcriptRepository.UpdateAsync(transcript);

            var (correctedText, processingTime, cost) = await _correctionService.CorrectTextAsync(
                transcript.OriginalText,
                transcript.Language,
                transcript.CorrectionType
            );

            transcript.SetCorrectedText(correctedText, processingTime, cost);
            await _transcriptRepository.UpdateAsync(transcript);
        }
        catch (Exception ex)
        {
            transcript.SetError(ex.Message);
            await _transcriptRepository.UpdateAsync(transcript);
        }

        return ObjectMapper.Map<Transcript, TranscriptDto>(transcript);
    }

    protected override async Task<IQueryable<Transcript>> CreateFilteredQueryAsync(TranscriptFilterDto input)
    {
        var query = await base.CreateFilteredQueryAsync(input);
        
        return query.WhereIf(
            !input.Filter.IsNullOrWhiteSpace(),
            x => x.Title.Contains(input.Filter!) || x.OriginalText.Contains(input.Filter!)
        ).WhereIf(
            !string.IsNullOrWhiteSpace(input.Status),
            x => x.Status.ToString() == input.Status
        ).WhereIf(
            !input.Language.IsNullOrWhiteSpace(),
            x => x.Language == input.Language
        ).WhereIf(
            input.CreatedAfter.HasValue,
            x => x.CreationTime >= input.CreatedAfter
        ).WhereIf(
            input.CreatedBefore.HasValue,
            x => x.CreationTime <= input.CreatedBefore
        );
    }
}