using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Shouldly;
using Volo.Abp.Testing;
using Xunit;
using TranscriptCleanerMvc.Controllers;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Controllers
{
    public class ExternalAuthControllerTests : AbpIntegratedTest<TranscriptCleanerMvcTestModule>
    {
        private readonly Mock<IExternalLoginService> _externalLoginServiceMock;
        private readonly Mock<IAccountLinkService> _accountLinkServiceMock;
        private readonly Mock<IProfileSyncService> _profileSyncServiceMock;
        private readonly ExternalAuthController _controller;

        public ExternalAuthControllerTests()
        {
            _externalLoginServiceMock = new Mock<IExternalLoginService>();
            _accountLinkServiceMock = new Mock<IAccountLinkService>();
            _profileSyncServiceMock = new Mock<IProfileSyncService>();

            _controller = new ExternalAuthController(
                _externalLoginServiceMock.Object,
                _accountLinkServiceMock.Object,
                _profileSyncServiceMock.Object);
        }

        [Fact]
        public async Task LoginWithGoogleAsync_ValidRequest_ShouldReturnOkResult()
        {
            // Arrange
            var request = new ExternalLoginRequest
            {
                Provider = "Google",
                AccessToken = "valid-access-token",
                IpAddress = "***********",
                UserAgent = "Test User Agent"
            };

            var expectedResult = new ExternalLoginResultDto
            {
                IsSuccess = true,
                User = new GoogleUserInfoDto
                {
                    Id = "google-user-id",
                    Email = "<EMAIL>",
                    Name = "Test User"
                },
                IsNewUser = false
            };

            _externalLoginServiceMock.Setup(x => x.LoginWithGoogleAsync(It.IsAny<ExternalLoginRequest>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.LoginWithGoogleAsync(request);

            // Assert
            result.ShouldNotBeNull();
            result.IsSuccess.ShouldBeTrue();
            result.User.ShouldNotBeNull();
            result.User.Email.ShouldBe("<EMAIL>");
        }

        [Fact]
        public async Task LoginWithGoogleAsync_InvalidRequest_ShouldReturnFailureResult()
        {
            // Arrange
            var request = new ExternalLoginRequest
            {
                Provider = "Google",
                AccessToken = "invalid-access-token",
                IpAddress = "***********",
                UserAgent = "Test User Agent"
            };

            var expectedResult = new ExternalLoginResultDto
            {
                IsSuccess = false,
                ErrorMessage = "Invalid access token"
            };

            _externalLoginServiceMock.Setup(x => x.LoginWithGoogleAsync(It.IsAny<ExternalLoginRequest>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.LoginWithGoogleAsync(request);

            // Assert
            result.ShouldNotBeNull();
            result.IsSuccess.ShouldBeFalse();
            result.ErrorMessage.ShouldNotBeNullOrEmpty();
        }

        [Fact]
        public async Task LinkGoogleAccountAsync_ValidRequest_ShouldReturnSuccessResult()
        {
            // Arrange
            var request = new LinkExternalAccountRequest
            {
                Provider = "Google",
                AccessToken = "valid-access-token"
            };

            var expectedResult = new AccountLinkValidationResultDto
            {
                IsValid = true,
                IsSuccess = true
            };

            _accountLinkServiceMock.Setup(x => x.LinkExternalAccountAsync(It.IsAny<LinkExternalAccountRequest>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.LinkGoogleAccountAsync(request);

            // Assert
            result.ShouldNotBeNull();
            result.IsValid.ShouldBeTrue();
            result.IsSuccess.ShouldBeTrue();
        }

        [Fact]
        public async Task UnlinkGoogleAccountAsync_ValidProvider_ShouldReturnSuccessResult()
        {
            // Arrange
            var provider = "Google";

            _accountLinkServiceMock.Setup(x => x.UnlinkExternalAccountAsync(It.IsAny<string>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UnlinkGoogleAccountAsync(provider);

            // Assert
            result.ShouldBeTrue();
        }

        [Fact]
        public async Task GetExternalAccountStatusAsync_ShouldReturnAccountStatus()
        {
            // Arrange
            var expectedStatus = new ExternalAccountLinkStatusDto
            {
                IsLinked = true,
                Provider = "Google",
                LinkedAt = DateTime.UtcNow,
                UserInfo = new GoogleUserInfoDto
                {
                    Id = "google-user-id",
                    Email = "<EMAIL>",
                    Name = "Test User"
                }
            };

            _accountLinkServiceMock.Setup(x => x.GetExternalAccountStatusAsync())
                .ReturnsAsync(expectedStatus);

            // Act
            var result = await _controller.GetExternalAccountStatusAsync();

            // Assert
            result.ShouldNotBeNull();
            result.IsLinked.ShouldBeTrue();
            result.Provider.ShouldBe("Google");
        }

        [Fact]
        public async Task SyncProfileAsync_ShouldReturnSyncResult()
        {
            // Arrange
            var expectedResult = new ProfileSyncResultDto
            {
                IsSuccess = true,
                SyncedFields = new[] { "Name", "Email", "Picture" },
                SyncedAt = DateTime.UtcNow
            };

            _profileSyncServiceMock.Setup(x => x.SyncProfileFromGoogleAsync())
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.SyncProfileAsync();

            // Assert
            result.ShouldNotBeNull();
            result.IsSuccess.ShouldBeTrue();
            result.SyncedFields.ShouldNotBeEmpty();
        }

        [Fact]
        public async Task GetProfileSyncHistoryAsync_ShouldReturnSyncHistory()
        {
            // Arrange
            var expectedHistory = new[]
            {
                new ProfileSyncHistoryDto
                {
                    Id = Guid.NewGuid(),
                    SyncedAt = DateTime.UtcNow.AddDays(-1),
                    IsSuccess = true,
                    SyncedFields = new[] { "Name", "Email" }
                },
                new ProfileSyncHistoryDto
                {
                    Id = Guid.NewGuid(),
                    SyncedAt = DateTime.UtcNow.AddDays(-2),
                    IsSuccess = true,
                    SyncedFields = new[] { "Picture" }
                }
            };

            _profileSyncServiceMock.Setup(x => x.GetProfileSyncHistoryAsync())
                .ReturnsAsync(expectedHistory);

            // Act
            var result = await _controller.GetProfileSyncHistoryAsync();

            // Assert
            result.ShouldNotBeNull();
            result.Length.ShouldBe(2);
            result[0].IsSuccess.ShouldBeTrue();
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public async Task LoginWithGoogleAsync_InvalidAccessToken_ShouldReturnFailure(string accessToken)
        {
            // Arrange
            var request = new ExternalLoginRequest
            {
                Provider = "Google",
                AccessToken = accessToken,
                IpAddress = "***********",
                UserAgent = "Test User Agent"
            };

            var expectedResult = new ExternalLoginResultDto
            {
                IsSuccess = false,
                ErrorMessage = "Access token is required"
            };

            _externalLoginServiceMock.Setup(x => x.LoginWithGoogleAsync(It.IsAny<ExternalLoginRequest>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.LoginWithGoogleAsync(request);

            // Assert
            result.ShouldNotBeNull();
            result.IsSuccess.ShouldBeFalse();
            result.ErrorMessage.ShouldNotBeNullOrEmpty();
        }

        [Fact]
        public async Task LinkGoogleAccountAsync_AlreadyLinked_ShouldReturnValidationError()
        {
            // Arrange
            var request = new LinkExternalAccountRequest
            {
                Provider = "Google",
                AccessToken = "valid-access-token"
            };

            var expectedResult = new AccountLinkValidationResultDto
            {
                IsValid = false,
                IsSuccess = false,
                ErrorMessage = "Account is already linked"
            };

            _accountLinkServiceMock.Setup(x => x.LinkExternalAccountAsync(It.IsAny<LinkExternalAccountRequest>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.LinkGoogleAccountAsync(request);

            // Assert
            result.ShouldNotBeNull();
            result.IsValid.ShouldBeFalse();
            result.IsSuccess.ShouldBeFalse();
            result.ErrorMessage.ShouldNotBeNullOrEmpty();
        }

        [Fact]
        public async Task UnlinkGoogleAccountAsync_NotLinked_ShouldReturnFalse()
        {
            // Arrange
            var provider = "Google";

            _accountLinkServiceMock.Setup(x => x.UnlinkExternalAccountAsync(It.IsAny<string>()))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.UnlinkGoogleAccountAsync(provider);

            // Assert
            result.ShouldBeFalse();
        }

        [Fact]
        public async Task SyncProfileAsync_SyncFailure_ShouldReturnFailureResult()
        {
            // Arrange
            var expectedResult = new ProfileSyncResultDto
            {
                IsSuccess = false,
                ErrorMessage = "Failed to sync profile",
                SyncedAt = DateTime.UtcNow
            };

            _profileSyncServiceMock.Setup(x => x.SyncProfileFromGoogleAsync())
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.SyncProfileAsync();

            // Assert
            result.ShouldNotBeNull();
            result.IsSuccess.ShouldBeFalse();
            result.ErrorMessage.ShouldNotBeNullOrEmpty();
        }
    }
}