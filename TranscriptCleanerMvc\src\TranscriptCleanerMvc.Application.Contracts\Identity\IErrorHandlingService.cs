using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// エラーハンドリングサービスインターフェース
    /// </summary>
    public interface IErrorHandlingService : IApplicationService
    {
        /// <summary>
        /// 包括的なエラーハンドリング
        /// </summary>
        /// <param name="exception">例外</param>
        /// <param name="context">コンテキスト</param>
        /// <returns>エラー応答</returns>
        Task<ErrorResponse> HandleExceptionAsync(Exception exception, ErrorContext context);

        /// <summary>
        /// 認証エラーのハンドリング
        /// </summary>
        /// <param name="error">認証エラー</param>
        /// <param name="context">コンテキスト</param>
        /// <returns>エラー応答</returns>
        Task<ErrorResponse> HandleAuthenticationErrorAsync(AuthenticationError error, ErrorContext context);

        /// <summary>
        /// OAuth エラーのハンドリング
        /// </summary>
        /// <param name="error">OAuth エラー</param>
        /// <param name="context">コンテキスト</param>
        /// <returns>エラー応答</returns>
        Task<ErrorResponse> HandleOAuthErrorAsync(OAuthError error, ErrorContext context);

        /// <summary>
        /// エラーログを記録
        /// </summary>
        /// <param name="errorLog">エラーログ</param>
        /// <returns>記録処理</returns>
        Task LogErrorAsync(ErrorLog errorLog);

        /// <summary>
        /// ユーザーフレンドリーなエラーメッセージを生成
        /// </summary>
        /// <param name="exception">例外</param>
        /// <param name="userLocale">ユーザーロケール</param>
        /// <returns>ユーザーフレンドリーメッセージ</returns>
        Task<string> GenerateUserFriendlyMessageAsync(Exception exception, string userLocale = "ja");

        /// <summary>
        /// エラー統計を取得
        /// </summary>
        /// <param name="period">期間</param>
        /// <returns>エラー統計</returns>
        Task<ErrorStatistics> GetErrorStatisticsAsync(StatisticsPeriod period);

        /// <summary>
        /// エラー通知を送信
        /// </summary>
        /// <param name="error">エラー情報</param>
        /// <param name="severity">重要度</param>
        /// <returns>送信結果</returns>
        Task<bool> SendErrorNotificationAsync(ErrorInfo error, ErrorSeverity severity);
    }

    /// <summary>
    /// エラー応答
    /// </summary>
    public class ErrorResponse
    {
        /// <summary>
        /// エラーコード
        /// </summary>
        public string ErrorCode { get; set; } = string.Empty;

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// ユーザー向けメッセージ
        /// </summary>
        public string UserMessage { get; set; } = string.Empty;

        /// <summary>
        /// 詳細情報
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// トレースID
        /// </summary>
        public string? TraceId { get; set; }

        /// <summary>
        /// タイムスタンプ
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// HTTPステータスコード
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// 再試行可能フラグ
        /// </summary>
        public bool IsRetryable { get; set; }

        /// <summary>
        /// 推奨アクション
        /// </summary>
        public string[]? SuggestedActions { get; set; }
    }

    /// <summary>
    /// エラーコンテキスト
    /// </summary>
    public class ErrorContext
    {
        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// セッションID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// リクエストID
        /// </summary>
        public string? RequestId { get; set; }

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// ユーザーエージェント
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// リクエストパス
        /// </summary>
        public string? RequestPath { get; set; }

        /// <summary>
        /// HTTPメソッド
        /// </summary>
        public string? HttpMethod { get; set; }

        /// <summary>
        /// ユーザーロケール
        /// </summary>
        public string UserLocale { get; set; } = "ja";

        /// <summary>
        /// 追加データ
        /// </summary>
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    /// <summary>
    /// 認証エラー
    /// </summary>
    public class AuthenticationError
    {
        /// <summary>
        /// エラータイプ
        /// </summary>
        public AuthErrorType ErrorType { get; set; }

        /// <summary>
        /// プロバイダー名
        /// </summary>
        public string? Provider { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 内部例外
        /// </summary>
        public Exception? InnerException { get; set; }

        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 試行回数
        /// </summary>
        public int AttemptCount { get; set; }

        /// <summary>
        /// 発生日時
        /// </summary>
        public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// OAuth エラー
    /// </summary>
    public class OAuthError
    {
        /// <summary>
        /// エラーコード
        /// </summary>
        public string ErrorCode { get; set; } = string.Empty;

        /// <summary>
        /// エラー説明
        /// </summary>
        public string? ErrorDescription { get; set; }

        /// <summary>
        /// エラーURI
        /// </summary>
        public string? ErrorUri { get; set; }

        /// <summary>
        /// State
        /// </summary>
        public string? State { get; set; }

        /// <summary>
        /// クライアントID
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// リダイレクトURI
        /// </summary>
        public string? RedirectUri { get; set; }

        /// <summary>
        /// 発生日時
        /// </summary>
        public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// エラーログ
    /// </summary>
    public class ErrorLog
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// エラーレベル
        /// </summary>
        public ErrorLevel Level { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 例外詳細
        /// </summary>
        public string? ExceptionDetails { get; set; }

        /// <summary>
        /// スタックトレース
        /// </summary>
        public string? StackTrace { get; set; }

        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// セッションID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// リクエストID
        /// </summary>
        public string? RequestId { get; set; }

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// ユーザーエージェント
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// リクエストパス
        /// </summary>
        public string? RequestPath { get; set; }

        /// <summary>
        /// HTTPメソッド
        /// </summary>
        public string? HttpMethod { get; set; }

        /// <summary>
        /// 発生日時
        /// </summary>
        public DateTime OccurredAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 解決済みフラグ
        /// </summary>
        public bool IsResolved { get; set; }

        /// <summary>
        /// 解決日時
        /// </summary>
        public DateTime? ResolvedAt { get; set; }

        /// <summary>
        /// 追加データ
        /// </summary>
        public string? AdditionalData { get; set; }
    }

    /// <summary>
    /// エラー統計
    /// </summary>
    public class ErrorStatistics
    {
        /// <summary>
        /// 期間
        /// </summary>
        public StatisticsPeriod Period { get; set; }

        /// <summary>
        /// 総エラー数
        /// </summary>
        public int TotalErrors { get; set; }

        /// <summary>
        /// 認証エラー数
        /// </summary>
        public int AuthenticationErrors { get; set; }

        /// <summary>
        /// OAuth エラー数
        /// </summary>
        public int OAuthErrors { get; set; }

        /// <summary>
        /// システムエラー数
        /// </summary>
        public int SystemErrors { get; set; }

        /// <summary>
        /// 解決済みエラー数
        /// </summary>
        public int ResolvedErrors { get; set; }

        /// <summary>
        /// 未解決エラー数
        /// </summary>
        public int UnresolvedErrors { get; set; }

        /// <summary>
        /// エラー率
        /// </summary>
        public double ErrorRate { get; set; }

        /// <summary>
        /// 最頻エラー
        /// </summary>
        public ErrorFrequency[] TopErrors { get; set; } = Array.Empty<ErrorFrequency>();

        /// <summary>
        /// 統計生成日時
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// エラー情報
    /// </summary>
    public class ErrorInfo
    {
        /// <summary>
        /// エラーID
        /// </summary>
        public Guid ErrorId { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 例外タイプ
        /// </summary>
        public string? ExceptionType { get; set; }

        /// <summary>
        /// 発生場所
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 発生日時
        /// </summary>
        public DateTime OccurredAt { get; set; }
    }

    /// <summary>
    /// エラー頻度
    /// </summary>
    public class ErrorFrequency
    {
        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 発生回数
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 最終発生日時
        /// </summary>
        public DateTime LastOccurred { get; set; }
    }

    /// <summary>
    /// 認証エラータイプ
    /// </summary>
    public enum AuthErrorType
    {
        /// <summary>
        /// 無効な認証情報
        /// </summary>
        InvalidCredentials,

        /// <summary>
        /// アカウントロック
        /// </summary>
        AccountLocked,

        /// <summary>
        /// アカウント無効
        /// </summary>
        AccountDisabled,

        /// <summary>
        /// トークン期限切れ
        /// </summary>
        TokenExpired,

        /// <summary>
        /// 無効なトークン
        /// </summary>
        InvalidToken,

        /// <summary>
        /// 権限不足
        /// </summary>
        InsufficientPermissions,

        /// <summary>
        /// 外部プロバイダーエラー
        /// </summary>
        ExternalProviderError,

        /// <summary>
        /// ネットワークエラー
        /// </summary>
        NetworkError,

        /// <summary>
        /// 設定エラー
        /// </summary>
        ConfigurationError,

        /// <summary>
        /// 不明なエラー
        /// </summary>
        Unknown
    }

    /// <summary>
    /// エラーレベル
    /// </summary>
    public enum ErrorLevel
    {
        /// <summary>
        /// 情報
        /// </summary>
        Information,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// エラー
        /// </summary>
        Error,

        /// <summary>
        /// 重大
        /// </summary>
        Critical,

        /// <summary>
        /// 致命的
        /// </summary>
        Fatal
    }

    /// <summary>
    /// エラー重要度
    /// </summary>
    public enum ErrorSeverity
    {
        /// <summary>
        /// 低
        /// </summary>
        Low,

        /// <summary>
        /// 中
        /// </summary>
        Medium,

        /// <summary>
        /// 高
        /// </summary>
        High,

        /// <summary>
        /// 緊急
        /// </summary>
        Critical
    }

    /// <summary>
    /// 統計期間
    /// </summary>
    public enum StatisticsPeriod
    {
        /// <summary>
        /// 今日
        /// </summary>
        Today,

        /// <summary>
        /// 今週
        /// </summary>
        ThisWeek,

        /// <summary>
        /// 今月
        /// </summary>
        ThisMonth,

        /// <summary>
        /// 過去30日
        /// </summary>
        Last30Days,

        /// <summary>
        /// 過去90日
        /// </summary>
        Last90Days
    }
}