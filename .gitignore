# Build artifacts
bin/
obj/

# OS-specific files
.DS_Store
Thumbs.db

# Dependency directories
node_modules/

# Log files
*.log

# Archive files
*.tar.gz

# Visual Studio specific files
*.suo
*.user
*.userosscache
*.sln.docsettings

# Maui specific files
frontend-maui/TranscriptCleaner.Maui/obj/
frontend-maui/TranscriptCleaner.Maui/bin/

# ASP.NET Core specific files
TranscriptCleanerMvc/obj/
TranscriptCleanerMvc/bin/
TranscriptCleanerMvc/wwwroot/dist/
TranscriptCleanerMvc/wwwroot/css/
TranscriptCleanerMvc/wwwroot/js/
TranscriptCleanerMvc/wwwroot/lib/
TranscriptCleanerMvc/appsettings.Development.json
TranscriptCleanerMvc/appsettings.Production.json

# Secrets and sensitive configuration files
**/appsettings.secrets.json
**/appsettings.local.json
**/*.secrets.json
**/.env
**/.env.local
**/.env.production

# OpenAI API Keys and other secrets
**/secrets/
**/private/

# IDE specific files
.vs/
.vscode/
*.swp
*.swo

.claude/
