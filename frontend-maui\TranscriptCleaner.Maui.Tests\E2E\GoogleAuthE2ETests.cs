using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using Shouldly;
using Xunit;
using TranscriptCleaner.Maui.Services;
using TranscriptCleaner.Maui.ViewModels;

namespace TranscriptCleaner.Maui.Tests.E2E
{
    /// <summary>
    /// MAUI Google認証のE2Eテスト
    /// </summary>
    public class GoogleAuthE2ETests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly IGoogleAuthService _googleAuthService;
        private readonly INetworkService _networkService;
        private readonly IJwtTokenService _jwtTokenService;
        private readonly IAuthSyncService _authSyncService;

        public GoogleAuthE2ETests()
        {
            var services = new ServiceCollection();
            
            // テスト用のサービス設定
            _googleAuthService = Substitute.For<IGoogleAuthService>();
            _networkService = Substitute.For<INetworkService>();
            _jwtTokenService = Substitute.For<IJwtTokenService>();
            _authSyncService = Substitute.For<IAuthSyncService>();

            services.AddSingleton(_googleAuthService);
            services.AddSingleton(_networkService);
            services.AddSingleton(_jwtTokenService);
            services.AddSingleton(_authSyncService);
            services.AddTransient<LoginPageViewModel>();

            _serviceProvider = services.BuildServiceProvider();
        }

        [Fact]
        public async Task GoogleLogin_WithNetworkConnection_ShouldSucceed()
        {
            // Arrange
            var viewModel = _serviceProvider.GetRequiredService<LoginPageViewModel>();
            
            _networkService.IsConnected.Returns(true);
            _googleAuthService.SignInAsync().Returns(new GoogleAuthResult
            {
                IsSuccess = true,
                AccessToken = "test_access_token",
                IdToken = "test_id_token",
                UserInfo = new GoogleUserInfo
                {
                    Id = "google_user_123",
                    Email = "<EMAIL>",
                    Name = "Test User"
                }
            });
            
            _authSyncService.SyncWithServerAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(new AuthSyncResult { IsSuccess = true });

            // Act
            await viewModel.GoogleLoginCommand.ExecuteAsync(null);

            // Assert
            viewModel.IsAuthenticated.ShouldBeTrue();
            viewModel.UserName.ShouldBe("Test User");
            viewModel.ErrorMessage.ShouldBeNull();
            
            await _googleAuthService.Received(1).SignInAsync();
            await _authSyncService.Received(1).SyncWithServerAsync(Arg.Any<string>(), Arg.Any<string>());
        }

        [Fact]
        public async Task GoogleLogin_WithoutNetworkConnection_ShouldUseOfflineAuth()
        {
            // Arrange
            var viewModel = _serviceProvider.GetRequiredService<LoginPageViewModel>();
            
            _networkService.IsConnected.Returns(false);
            _googleAuthService.GetCachedTokenAsync().Returns("cached_token");
            _jwtTokenService.ValidateTokenAsync("cached_token").Returns(true);

            // Act
            await viewModel.GoogleLoginCommand.ExecuteAsync(null);

            // Assert
            viewModel.IsAuthenticated.ShouldBeTrue();
            viewModel.ErrorMessage.ShouldBeNull();
            
            await _googleAuthService.Received(1).GetCachedTokenAsync();
            await _jwtTokenService.Received(1).ValidateTokenAsync("cached_token");
        }

        [Fact]
        public async Task GoogleLogin_WithInvalidToken_ShouldShowError()
        {
            // Arrange
            var viewModel = _serviceProvider.GetRequiredService<LoginPageViewModel>();
            
            _networkService.IsConnected.Returns(true);
            _googleAuthService.SignInAsync().Returns(new GoogleAuthResult
            {
                IsSuccess = false,
                ErrorMessage = "認証に失敗しました"
            });

            // Act
            await viewModel.GoogleLoginCommand.ExecuteAsync(null);

            // Assert
            viewModel.IsAuthenticated.ShouldBeFalse();
            viewModel.ErrorMessage.ShouldBe("認証に失敗しました");
            
            await _googleAuthService.Received(1).SignInAsync();
        }

        [Fact]
        public async Task TokenRefresh_WhenTokenExpired_ShouldRefreshAutomatically()
        {
            // Arrange
            var viewModel = _serviceProvider.GetRequiredService<LoginPageViewModel>();
            
            _networkService.IsConnected.Returns(true);
            _jwtTokenService.IsTokenExpiredAsync(Arg.Any<string>()).Returns(true);
            _jwtTokenService.RefreshTokenAsync(Arg.Any<string>()).Returns("new_access_token");
            
            // 初期認証を設定
            viewModel.SetAuthenticationState(true, "Test User", "expired_token");

            // Act
            await viewModel.RefreshTokenCommand.ExecuteAsync(null);

            // Assert
            await _jwtTokenService.Received(1).RefreshTokenAsync(Arg.Any<string>());
        }

        [Fact]
        public async Task Logout_ShouldClearAllAuthenticationData()
        {
            // Arrange
            var viewModel = _serviceProvider.GetRequiredService<LoginPageViewModel>();
            
            // 認証状態を設定
            viewModel.SetAuthenticationState(true, "Test User", "test_token");
            
            _googleAuthService.SignOutAsync().Returns(Task.CompletedTask);
            _jwtTokenService.ClearTokensAsync().Returns(Task.CompletedTask);

            // Act
            await viewModel.LogoutCommand.ExecuteAsync(null);

            // Assert
            viewModel.IsAuthenticated.ShouldBeFalse();
            viewModel.UserName.ShouldBeNull();
            
            await _googleAuthService.Received(1).SignOutAsync();
            await _jwtTokenService.Received(1).ClearTokensAsync();
        }

        [Fact]
        public async Task NetworkReconnection_ShouldSyncPendingData()
        {
            // Arrange
            var viewModel = _serviceProvider.GetRequiredService<LoginPageViewModel>();
            
            // オフライン状態で認証
            _networkService.IsConnected.Returns(false);
            _googleAuthService.GetCachedTokenAsync().Returns("cached_token");
            _jwtTokenService.ValidateTokenAsync("cached_token").Returns(true);
            
            await viewModel.GoogleLoginCommand.ExecuteAsync(null);

            // ネットワーク復旧をシミュレート
            _networkService.IsConnected.Returns(true);
            _authSyncService.SyncPendingDataAsync().Returns(Task.CompletedTask);

            // Act
            await viewModel.HandleNetworkReconnectionAsync();

            // Assert
            await _authSyncService.Received(1).SyncPendingDataAsync();
        }

        [Fact]
        public async Task AppResume_ShouldValidateTokenAndRefreshIfNeeded()
        {
            // Arrange
            var viewModel = _serviceProvider.GetRequiredService<LoginPageViewModel>();
            
            // 認証状態を設定
            viewModel.SetAuthenticationState(true, "Test User", "test_token");
            
            _jwtTokenService.IsTokenExpiredAsync("test_token").Returns(true);
            _jwtTokenService.RefreshTokenAsync(Arg.Any<string>()).Returns("refreshed_token");

            // Act
            await viewModel.HandleAppResumeAsync();

            // Assert
            await _jwtTokenService.Received(1).IsTokenExpiredAsync("test_token");
            await _jwtTokenService.Received(1).RefreshTokenAsync(Arg.Any<string>());
        }

        [Fact]
        public async Task BiometricAuth_WhenEnabled_ShouldUseLocalAuthentication()
        {
            // Arrange
            var viewModel = _serviceProvider.GetRequiredService<LoginPageViewModel>();
            
            _googleAuthService.IsBiometricAuthAvailableAsync().Returns(true);
            _googleAuthService.AuthenticateWithBiometricsAsync().Returns(new GoogleAuthResult
            {
                IsSuccess = true,
                AccessToken = "biometric_token"
            });

            // Act
            await viewModel.BiometricLoginCommand.ExecuteAsync(null);

            // Assert
            viewModel.IsAuthenticated.ShouldBeTrue();
            await _googleAuthService.Received(1).AuthenticateWithBiometricsAsync();
        }

        [Fact]
        public async Task SecureStorage_ShouldProtectSensitiveData()
        {
            // Arrange
            var viewModel = _serviceProvider.GetRequiredService<LoginPageViewModel>();
            
            _googleAuthService.SignInAsync().Returns(new GoogleAuthResult
            {
                IsSuccess = true,
                AccessToken = "sensitive_token",
                RefreshToken = "sensitive_refresh_token"
            });

            // Act
            await viewModel.GoogleLoginCommand.ExecuteAsync(null);

            // Assert
            // セキュアストレージに保存されることを確認
            await _googleAuthService.Received(1).StoreTokenSecurelyAsync(Arg.Any<string>(), Arg.Any<string>());
        }

        [Fact]
        public async Task ErrorRecovery_ShouldHandleTransientErrors()
        {
            // Arrange
            var viewModel = _serviceProvider.GetRequiredService<LoginPageViewModel>();
            
            // 最初の試行は失敗
            _googleAuthService.SignInAsync().Returns(
                new GoogleAuthResult { IsSuccess = false, ErrorMessage = "Network error" },
                new GoogleAuthResult { IsSuccess = true, AccessToken = "success_token" }
            );

            // Act
            await viewModel.GoogleLoginCommand.ExecuteAsync(null);
            await viewModel.RetryLoginCommand.ExecuteAsync(null);

            // Assert
            viewModel.IsAuthenticated.ShouldBeTrue();
            await _googleAuthService.Received(2).SignInAsync();
        }

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }

    // テスト用の拡張メソッド
    public static class LoginPageViewModelExtensions
    {
        public static void SetAuthenticationState(this LoginPageViewModel viewModel, bool isAuthenticated, string userName, string token)
        {
            // リフレクションまたはテスト用メソッドを使用して内部状態を設定
            // 実際の実装では、ViewModelにテスト用のメソッドを追加することを推奨
        }

        public static async Task HandleNetworkReconnectionAsync(this LoginPageViewModel viewModel)
        {
            // ネットワーク再接続時の処理をシミュレート
            await Task.CompletedTask;
        }

        public static async Task HandleAppResumeAsync(this LoginPageViewModel viewModel)
        {
            // アプリ復帰時の処理をシミュレート
            await Task.CompletedTask;
        }
    }

    // テスト用のデータ構造
    public class GoogleAuthResult
    {
        public bool IsSuccess { get; set; }
        public string? AccessToken { get; set; }
        public string? IdToken { get; set; }
        public string? RefreshToken { get; set; }
        public GoogleUserInfo? UserInfo { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class GoogleUserInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class AuthSyncResult
    {
        public bool IsSuccess { get; set; }
        public string? ErrorMessage { get; set; }
    }
}