using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Web.Middleware
{
    /// <summary>
    /// セキュリティミドルウェア
    /// </summary>
    public class SecurityMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<SecurityMiddleware> _logger;

        public SecurityMiddleware(RequestDelegate next, ILogger<SecurityMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // セキュリティヘッダーを追加
                await AddSecurityHeadersAsync(context);

                // レート制限チェック
                if (!await CheckRateLimitAsync(context))
                {
                    context.Response.StatusCode = 429; // Too Many Requests
                    await context.Response.WriteAsync("Rate limit exceeded");
                    return;
                }

                // HTTPS強制チェック
                if (!await ValidateHttpsAsync(context))
                {
                    context.Response.StatusCode = 400; // Bad Request
                    await context.Response.WriteAsync("HTTPS required");
                    return;
                }

                // セキュリティ監査ログ
                await LogSecurityEventAsync(context);

                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "セキュリティミドルウェア処理中にエラーが発生しました");
                
                // セキュリティ関連のエラーは詳細を隠す
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync("Internal server error");
            }
        }

        /// <summary>
        /// セキュリティヘッダーを追加
        /// </summary>
        private async Task AddSecurityHeadersAsync(HttpContext context)
        {
            try
            {
                var securityService = context.RequestServices.GetService<ISecurityService>();
                if (securityService != null)
                {
                    var headers = await securityService.GenerateSecurityHeadersAsync();
                    
                    if (!string.IsNullOrEmpty(headers.ContentSecurityPolicy))
                    {
                        context.Response.Headers["Content-Security-Policy"] = headers.ContentSecurityPolicy;
                    }
                    
                    if (!string.IsNullOrEmpty(headers.XFrameOptions))
                    {
                        context.Response.Headers["X-Frame-Options"] = headers.XFrameOptions;
                    }
                    
                    if (!string.IsNullOrEmpty(headers.XContentTypeOptions))
                    {
                        context.Response.Headers["X-Content-Type-Options"] = headers.XContentTypeOptions;
                    }
                    
                    if (!string.IsNullOrEmpty(headers.ReferrerPolicy))
                    {
                        context.Response.Headers["Referrer-Policy"] = headers.ReferrerPolicy;
                    }
                    
                    if (!string.IsNullOrEmpty(headers.StrictTransportSecurity) && context.Request.IsHttps)
                    {
                        context.Response.Headers["Strict-Transport-Security"] = headers.StrictTransportSecurity;
                    }
                    
                    if (!string.IsNullOrEmpty(headers.XXssProtection))
                    {
                        context.Response.Headers["X-XSS-Protection"] = headers.XXssProtection;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "セキュリティヘッダー追加中にエラーが発生しました");
            }
        }

        /// <summary>
        /// レート制限チェック
        /// </summary>
        private async Task<bool> CheckRateLimitAsync(HttpContext context)
        {
            try
            {
                // 認証関連のエンドポイントのみチェック
                var path = context.Request.Path.Value?.ToLower();
                if (path == null || (!path.Contains("/auth/") && !path.Contains("/account/")))
                {
                    return true;
                }

                var securityService = context.RequestServices.GetService<ISecurityService>();
                if (securityService == null)
                {
                    return true;
                }

                var clientId = GetClientId(context);
                var ipAddress = GetClientIpAddress(context);

                var rateLimitResult = await securityService.CheckRateLimitAsync(clientId, ipAddress);
                
                if (!rateLimitResult.IsAllowed)
                {
                    _logger.LogWarning("レート制限に達しました。ClientId: {ClientId}, IP: {IpAddress}", clientId, ipAddress);
                    
                    // レート制限情報をヘッダーに追加
                    context.Response.Headers["X-RateLimit-Limit"] = "100";
                    context.Response.Headers["X-RateLimit-Remaining"] = rateLimitResult.RemainingRequests.ToString();
                    context.Response.Headers["X-RateLimit-Reset"] = ((DateTimeOffset)rateLimitResult.ResetTime).ToUnixTimeSeconds().ToString();
                    
                    return false;
                }

                // レート制限情報をヘッダーに追加
                context.Response.Headers["X-RateLimit-Limit"] = "100";
                context.Response.Headers["X-RateLimit-Remaining"] = rateLimitResult.RemainingRequests.ToString();
                context.Response.Headers["X-RateLimit-Reset"] = ((DateTimeOffset)rateLimitResult.ResetTime).ToUnixTimeSeconds().ToString();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "レート制限チェック中にエラーが発生しました");
                return true; // エラー時は通す
            }
        }

        /// <summary>
        /// HTTPS検証
        /// </summary>
        private async Task<bool> ValidateHttpsAsync(HttpContext context)
        {
            try
            {
                var securityService = context.RequestServices.GetService<ISecurityService>();
                if (securityService == null)
                {
                    return true;
                }

                var requestUrl = $"{context.Request.Scheme}://{context.Request.Host}{context.Request.Path}{context.Request.QueryString}";
                return await securityService.ValidateHttpsAsync(requestUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "HTTPS検証中にエラーが発生しました");
                return true; // エラー時は通す
            }
        }

        /// <summary>
        /// セキュリティイベントをログに記録
        /// </summary>
        private async Task LogSecurityEventAsync(HttpContext context)
        {
            try
            {
                var path = context.Request.Path.Value?.ToLower();
                if (path == null || (!path.Contains("/auth/") && !path.Contains("/account/")))
                {
                    return;
                }

                var securityService = context.RequestServices.GetService<ISecurityService>();
                if (securityService == null)
                {
                    return;
                }

                var auditLog = new SecurityAuditLog
                {
                    EventType = DetermineEventType(context),
                    IpAddress = GetClientIpAddress(context),
                    UserAgent = context.Request.Headers["User-Agent"].ToString(),
                    EventDetails = $"{context.Request.Method} {context.Request.Path}",
                    IsSuccess = true, // リクエスト時点では成功とする
                    RiskLevel = DetermineRiskLevel(context),
                    Timestamp = DateTime.UtcNow
                };

                await securityService.LogSecurityAuditAsync(auditLog);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "セキュリティイベントログ記録中にエラーが発生しました");
            }
        }

        /// <summary>
        /// クライアントIDを取得
        /// </summary>
        private string GetClientId(HttpContext context)
        {
            // クエリパラメータまたはフォームからクライアントIDを取得
            return context.Request.Query["client_id"].ToString() ?? 
                   context.Request.Form["client_id"].ToString() ?? 
                   "unknown";
        }

        /// <summary>
        /// クライアントIPアドレスを取得
        /// </summary>
        private string GetClientIpAddress(HttpContext context)
        {
            // プロキシ経由の場合のIPアドレス取得
            var xForwardedFor = context.Request.Headers["X-Forwarded-For"].ToString();
            if (!string.IsNullOrEmpty(xForwardedFor))
            {
                return xForwardedFor.Split(',')[0].Trim();
            }

            var xRealIp = context.Request.Headers["X-Real-IP"].ToString();
            if (!string.IsNullOrEmpty(xRealIp))
            {
                return xRealIp;
            }

            return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        }

        /// <summary>
        /// イベントタイプを判定
        /// </summary>
        private SecurityEventType DetermineEventType(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLower();
            var method = context.Request.Method.ToUpper();

            if (path?.Contains("/login") == true)
            {
                return SecurityEventType.LoginAttempt;
            }
            else if (path?.Contains("/logout") == true)
            {
                return SecurityEventType.Logout;
            }
            else if (path?.Contains("/external") == true)
            {
                return SecurityEventType.LoginAttempt;
            }
            else
            {
                return SecurityEventType.UnauthorizedAccess;
            }
        }

        /// <summary>
        /// リスクレベルを判定
        /// </summary>
        private RiskLevel DetermineRiskLevel(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLower();
            var userAgent = context.Request.Headers["User-Agent"].ToString();

            // 疑わしいユーザーエージェント
            if (string.IsNullOrEmpty(userAgent) || 
                userAgent.Contains("bot", StringComparison.OrdinalIgnoreCase) ||
                userAgent.Contains("crawler", StringComparison.OrdinalIgnoreCase))
            {
                return RiskLevel.Medium;
            }

            // 管理者関連のパス
            if (path?.Contains("/admin") == true)
            {
                return RiskLevel.High;
            }

            // 認証関連のパス
            if (path?.Contains("/auth") == true || path?.Contains("/login") == true)
            {
                return RiskLevel.Medium;
            }

            return RiskLevel.Low;
        }
    }
}