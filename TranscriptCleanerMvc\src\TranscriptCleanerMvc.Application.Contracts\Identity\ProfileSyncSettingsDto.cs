using System;
using Volo.Abp.Application.Dtos;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// プロフィール同期設定DTO
    /// </summary>
    public class ProfileSyncSettingsDto : EntityDto<Guid>
    {
        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 自動同期有効フラグ
        /// </summary>
        public bool AutoSyncEnabled { get; set; }

        /// <summary>
        /// プロフィール画像同期フラグ
        /// </summary>
        public bool SyncProfileImage { get; set; }

        /// <summary>
        /// 表示名同期フラグ
        /// </summary>
        public bool SyncDisplayName { get; set; }

        /// <summary>
        /// メールアドレス同期フラグ
        /// </summary>
        public bool SyncEmail { get; set; }

        /// <summary>
        /// 同期間隔（分）
        /// </summary>
        public int SyncIntervalMinutes { get; set; }

        /// <summary>
        /// 同期間隔（時間）- 後方互換性のため
        /// </summary>
        public int SyncIntervalHours 
        { 
            get => SyncIntervalMinutes / 60; 
            set => SyncIntervalMinutes = value * 60; 
        }

        /// <summary>
        /// 名前の同期を有効にするか
        /// </summary>
        public bool SyncName { get; set; } = true;

        /// <summary>
        /// ロケールの同期を有効にするか
        /// </summary>
        public bool SyncLocale { get; set; } = false;

        /// <summary>
        /// 最後の同期日時
        /// </summary>
        public DateTime? LastSyncTime { get; set; }

        /// <summary>
        /// 最後の自動同期日時
        /// </summary>
        public DateTime? LastAutoSyncTime { get; set; }

        /// <summary>
        /// 次回自動同期予定日時
        /// </summary>
        public DateTime? NextAutoSyncTime { get; set; }

        /// <summary>
        /// 同期プロバイダー
        /// </summary>
        public string Provider { get; set; } = string.Empty;

        /// <summary>
        /// 作成日時
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 最終更新日時
        /// </summary>
        public DateTime? LastModificationTime { get; set; }
    }

    /// <summary>
    /// プロフィール同期設定作成・更新DTO
    /// </summary>
    public class CreateUpdateProfileSyncSettingsDto
    {
        /// <summary>
        /// 自動同期有効フラグ
        /// </summary>
        public bool AutoSyncEnabled { get; set; }

        /// <summary>
        /// プロフィール画像同期フラグ
        /// </summary>
        public bool SyncProfileImage { get; set; }

        /// <summary>
        /// 表示名同期フラグ
        /// </summary>
        public bool SyncDisplayName { get; set; }

        /// <summary>
        /// メールアドレス同期フラグ
        /// </summary>
        public bool SyncEmail { get; set; }

        /// <summary>
        /// 同期間隔（分）
        /// </summary>
        public int SyncIntervalMinutes { get; set; } = 60;

        /// <summary>
        /// 同期プロバイダー
        /// </summary>
        public string Provider { get; set; } = "Google";
    }
}