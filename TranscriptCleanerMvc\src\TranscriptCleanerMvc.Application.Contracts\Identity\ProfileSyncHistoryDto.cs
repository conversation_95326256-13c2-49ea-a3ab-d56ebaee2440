using System;
using System.Collections.Generic;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// プロフィール同期履歴DTO
    /// </summary>
    public class ProfileSyncHistoryDto
    {
        /// <summary>
        /// 同期ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 同期日時
        /// </summary>
        public DateTime SyncTime { get; set; }

        /// <summary>
        /// プロバイダー名
        /// </summary>
        public string Provider { get; set; } = string.Empty;

        /// <summary>
        /// 同期タイプ（Manual, Auto, Scheduled）
        /// </summary>
        public string SyncType { get; set; } = string.Empty;

        /// <summary>
        /// 成功フラグ
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 更新されたフィールド一覧
        /// </summary>
        public List<string> UpdatedFields { get; set; } = new();

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 処理時間（ミリ秒）
        /// </summary>
        public int ProcessingTimeMs { get; set; }
    }


}