using CommunityToolkit.Mvvm.ComponentModel;

namespace TranscriptCleaner.WinUI.ViewModels;

public partial class TranscriptPageViewModel : ObservableObject
{
    [ObservableProperty]
    private string _originalText = string.Empty;

    [ObservableProperty]
    private string _correctedText = string.Empty;

    [ObservableProperty]
    private string _statusMessage = "Please select files or enter text.";

    [ObservableProperty]
    private bool _isProcessing = false;

    [ObservableProperty]
    private string _selectedTranscriptFilePath = string.Empty;

    [ObservableProperty]
    private string _selectedWordListFilePath = string.Empty;

    [ObservableProperty]
    private string _wordListContent = string.Empty;

    [ObservableProperty]
    private string _currentUserInfo = "";

    [ObservableProperty]
    private bool _isLoggedIn = false;

    [ObservableProperty]
    private string _selectedModel = "gpt-4";

    [ObservableProperty]
    private string _processingMode = "Comprehensive Correction";

    [ObservableProperty]
    private string _customPrompt = string.Empty;

    public bool CanCompare 
    { 
        get => !string.IsNullOrWhiteSpace(OriginalText) && !string.IsNullOrWhiteSpace(CorrectedText); 
    }
    
    public bool CanProcess 
    { 
        get => !IsProcessing && !string.IsNullOrWhiteSpace(OriginalText) && IsLoggedIn; 
    }
    
    public bool CanSave 
    { 
        get => !IsProcessing && !string.IsNullOrWhiteSpace(CorrectedText); 
    }

    public void UpdateStatus(string message)
    {
        StatusMessage = message;
    }

    public void SetProcessing(bool processing)
    {
        IsProcessing = processing;
        OnPropertyChanged(nameof(CanProcess));
        OnPropertyChanged(nameof(CanSave));
    }

    public void SetUserInfo(string username)
    {
        IsLoggedIn = true;
        CurrentUserInfo = $"User: {username}";
        OnPropertyChanged(nameof(CanProcess));
    }

    partial void OnOriginalTextChanged(string value)
    {
        OnPropertyChanged(nameof(CanProcess));
    }

    partial void OnCorrectedTextChanged(string value)
    {
        OnPropertyChanged(nameof(CanSave));
        OnPropertyChanged(nameof(CanCompare));
    }

    partial void OnIsProcessingChanged(bool value)
    {
        OnPropertyChanged(nameof(CanProcess));
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnIsLoggedInChanged(bool value)
    {
        OnPropertyChanged(nameof(CanProcess));
    }
}