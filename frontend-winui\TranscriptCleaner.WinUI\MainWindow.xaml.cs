using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Documents;
using Microsoft.UI.Xaml.Media;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Windows.Storage.Pickers;
using Windows.Storage;
using Windows.ApplicationModel.DataTransfer;
using WinRT.Interop;
using TranscriptCleaner.WinUI.Services;

namespace TranscriptCleaner.WinUI;

public sealed partial class MainWindow : Window
{
    private readonly TranscriptCorrectionService _transcriptService;
    private readonly AuthenticationService _authService;
    private readonly IGoogleAuthService _googleAuthService;
    private readonly IAuthStateManager _authStateManager;
    private string? _transcriptContent;
    private string? _wordListContent;

    public MainWindow()
    {
        this.InitializeComponent();
        this.Title = "TranscriptCleaner - Microsoft Teams 議事録自動訂正";
        
        // サービスを取得
        _googleAuthService = App.Services.GetRequiredService<IGoogleAuthService>();
        _authStateManager = App.Services.GetRequiredService<IAuthStateManager>();
        
        _authService = new AuthenticationService();
        _transcriptService = new TranscriptCorrectionService();
        _transcriptService.SetAuthenticationService(_authService);
        
        // 認証状態管理イベントを購読
        _authStateManager.AuthStateChanged += OnAuthStateChanged;
        
        // 初期化処理
        _ = InitializeAsync();
        
        // ウィンドウイベントを購読
        this.Activated += OnWindowActivated;
    }

    private async void OnWindowActivated(object sender, WindowActivatedEventArgs args)
    {
        if (args.WindowActivationState == WindowActivationState.Deactivated)
        {
            // ウィンドウが非アクティブになった時（最小化など）
            await _authStateManager.OnApplicationMinimizedAsync();
        }
        else if (args.WindowActivationState == WindowActivationState.CodeActivated ||
                 args.WindowActivationState == WindowActivationState.PointerActivated)
        {
            // ウィンドウがアクティブになった時（復元など）
            await _authStateManager.OnApplicationRestoredAsync();
        }
    }

    private async Task InitializeAsync()
    {
        // アプリケーション復元時の処理
        await _authStateManager.OnApplicationRestoredAsync();
        
        // 認証状態を確認
        var authStatus = await _authStateManager.GetCurrentAuthStatusAsync();
        if (authStatus == AuthenticationStatus.Authenticated)
        {
            HideLoginOverlay();
            UpdateWelcomeMessage();
        }
        else
        {
            ShowLoginOverlay();
        }
    }

    private void OnAuthStateChanged(object? sender, AuthStateChangedEventArgs e)
    {
        // UIスレッドで実行
        DispatcherQueue.TryEnqueue(() =>
        {
            switch (e.CurrentStatus)
            {
                case AuthenticationStatus.Authenticated:
                    HideLoginOverlay();
                    UpdateWelcomeMessage();
                    break;
                    
                case AuthenticationStatus.NotAuthenticated:
                case AuthenticationStatus.Error:
                    ShowLoginOverlay();
                    break;
                    
                case AuthenticationStatus.TokenExpired:
                    ShowTokenExpiredMessage();
                    break;
            }
        });
    }

    private async void ShowTokenExpiredMessage()
    {
        var dialog = new ContentDialog
        {
            Title = "認証期限切れ",
            Content = "認証の有効期限が切れました。再度ログインしてください。",
            CloseButtonText = "OK",
            XamlRoot = this.Content.XamlRoot
        };
        await dialog.ShowAsync();
        ShowLoginOverlay();
    }

    private async void OnSelectTranscriptClicked(object sender, RoutedEventArgs e)
    {
        try
        {
            var picker = new FileOpenPicker();
            picker.ViewMode = PickerViewMode.List;
            picker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;
            picker.FileTypeFilter.Add(".txt");
            picker.FileTypeFilter.Add(".docx");

            var hWnd = WindowNative.GetWindowHandle(this);
            InitializeWithWindow.Initialize(picker, hWnd);

            var file = await picker.PickSingleFileAsync();
            if (file != null)
            {
                await LoadTranscriptFile(file);
            }
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("ファイル選択エラー", ex.Message);
        }
    }

    private async void OnSelectWordListClicked(object sender, RoutedEventArgs e)
    {
        try
        {
            var picker = new FileOpenPicker();
            picker.ViewMode = PickerViewMode.List;
            picker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;
            picker.FileTypeFilter.Add(".txt");
            picker.FileTypeFilter.Add(".csv");

            var hWnd = WindowNative.GetWindowHandle(this);
            InitializeWithWindow.Initialize(picker, hWnd);

            var file = await picker.PickSingleFileAsync();
            if (file != null)
            {
                await LoadWordListFile(file);
            }
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("ファイル選択エラー", ex.Message);
        }
    }

    private async void OnTranscriptFileDrop(object sender, DragEventArgs e)
    {
        if (e.DataView.Contains(StandardDataFormats.StorageItems))
        {
            var items = await e.DataView.GetStorageItemsAsync();
            if (items.Count > 0 && items[0] is StorageFile file)
            {
                await LoadTranscriptFile(file);
            }
        }
    }

    private async void OnWordListFileDrop(object sender, DragEventArgs e)
    {
        if (e.DataView.Contains(StandardDataFormats.StorageItems))
        {
            var items = await e.DataView.GetStorageItemsAsync();
            if (items.Count > 0 && items[0] is StorageFile file)
            {
                await LoadWordListFile(file);
            }
        }
    }

    private void OnFileDragOver(object sender, DragEventArgs e)
    {
        e.AcceptedOperation = Windows.ApplicationModel.DataTransfer.DataPackageOperation.Copy;
    }

    private async Task LoadTranscriptFile(StorageFile file)
    {
        try
        {
            _transcriptContent = await FileIO.ReadTextAsync(file);
            TranscriptFileText.Text = $"Selected: {file.Name}";
            ShowOriginalText(_transcriptContent);
            UpdateProcessButtonState();
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("ファイル読み込みエラー", $"ファイルの読み込みに失敗しました：{ex.Message}");
        }
    }

    private async Task LoadWordListFile(StorageFile file)
    {
        try
        {
            _wordListContent = await FileIO.ReadTextAsync(file);
            WordListFileText.Text = $"Selected: {file.Name}";
            UpdateProcessButtonState();
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("ファイル読み込みエラー", $"ファイルの読み込みに失敗しました：{ex.Message}");
        }
    }

    private void UpdateProcessButtonState()
    {
        ProcessButton.IsEnabled = !string.IsNullOrEmpty(_transcriptContent) && !string.IsNullOrEmpty(_wordListContent);
    }

    private async void OnProcessClicked(object sender, RoutedEventArgs e)
    {
        if (!IsUserAuthenticated())
        {
            await ShowErrorDialog("認証エラー", "この機能を使用するにはログインが必要です。");
            ShowLoginOverlay();
            return;
        }

        if (!CanUserAccessFeature("transcript_correction"))
        {
            await ShowErrorDialog("アクセス拒否", "この機能を使用する権限がありません。");
            return;
        }

        if (string.IsNullOrEmpty(_transcriptContent) || string.IsNullOrEmpty(_wordListContent))
        {
            await ShowErrorDialog("エラー", "議事録ファイルと単語辞書ファイルの両方を選択してください。");
            return;
        }

        try
        {
            ProcessButton.IsEnabled = false;
            ProcessButton.Content = "Processing...";

            var correctedText = await _transcriptService.CorrectTranscriptAsync(_transcriptContent, _wordListContent);
            
            ShowCorrectedText(correctedText);
            GenerateWordLevelDiff(_transcriptContent, correctedText);

            ProcessButton.Content = "Start Correction";
            ProcessButton.IsEnabled = true;
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("Processing Error", $"Error occurred during transcript correction: {ex.Message}");
            ProcessButton.Content = "Start Correction";
            ProcessButton.IsEnabled = true;
        }
    }

    private void ShowOriginalText(string text)
    {
        OriginalTextBlock.Text = text;
    }

    private void ShowCorrectedText(string text)
    {
        CorrectedTextBlock.Text = text;
    }

    private void GenerateWordLevelDiff(string originalText, string correctedText)
    {
        try
        {
            DiffRichTextBlock.Blocks.Clear();

            var originalWords = originalText.Split(new char[] { ' ', '\n', '\r', '\t' }, StringSplitOptions.RemoveEmptyEntries);
            var correctedWords = correctedText.Split(new char[] { ' ', '\n', '\r', '\t' }, StringSplitOptions.RemoveEmptyEntries);

            var paragraph = new Paragraph();
            var changes = GetWordChanges(originalWords, correctedWords);

            foreach (var change in changes)
            {
                var run = new Run { Text = change.Text + " " };

                switch (change.Type)
                {
                    case ChangeType.Added:
                        run.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Green);
                        break;
                    case ChangeType.Deleted:
                        run.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Red);
                        break;
                    case ChangeType.Modified:
                        run.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Orange);
                        break;
                    case ChangeType.Unchanged:
                        // Default style
                        break;
                }

                paragraph.Inlines.Add(run);
            }

            DiffRichTextBlock.Blocks.Add(paragraph);
        }
        catch (Exception ex)
        {
            var errorParagraph = new Paragraph();
            errorParagraph.Inlines.Add(new Run { Text = $"Error in diff display: {ex.Message}" });
            DiffRichTextBlock.Blocks.Add(errorParagraph);
        }
    }

    private List<WordChange> GetWordChanges(string[] originalWords, string[] correctedWords)
    {
        var changes = new List<WordChange>();
        int originalIndex = 0, correctedIndex = 0;

        while (originalIndex < originalWords.Length || correctedIndex < correctedWords.Length)
        {
            if (originalIndex >= originalWords.Length)
            {
                changes.Add(new WordChange { Text = correctedWords[correctedIndex], Type = ChangeType.Added });
                correctedIndex++;
            }
            else if (correctedIndex >= correctedWords.Length)
            {
                changes.Add(new WordChange { Text = originalWords[originalIndex], Type = ChangeType.Deleted });
                originalIndex++;
            }
            else if (originalWords[originalIndex] == correctedWords[correctedIndex])
            {
                changes.Add(new WordChange { Text = originalWords[originalIndex], Type = ChangeType.Unchanged });
                originalIndex++;
                correctedIndex++;
            }
            else
            {
                changes.Add(new WordChange { Text = originalWords[originalIndex], Type = ChangeType.Deleted });
                changes.Add(new WordChange { Text = correctedWords[correctedIndex], Type = ChangeType.Added });
                originalIndex++;
                correctedIndex++;
            }
        }

        return changes;
    }

    private async Task ShowErrorDialog(string title, string message)
    {
        var dialog = new ContentDialog
        {
            Title = title,
            Content = message,
            CloseButtonText = "OK",
            XamlRoot = this.Content.XamlRoot
        };
        await dialog.ShowAsync();
    }

    private class WordChange
    {
        public string Text { get; set; } = "";
        public ChangeType Type { get; set; }
    }

    private enum ChangeType
    {
        Unchanged,
        Added,
        Deleted,
        Modified
    }

    // Authentication Methods
    private async void OnLoginClicked(object sender, RoutedEventArgs e)
    {
        var username = UsernameTextBox.Text?.Trim() ?? string.Empty;
        var password = PasswordBox.Password ?? string.Empty;

        if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
        {
            LoginStatusText.Text = "Please enter username and password.";
            LoginStatusText.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Red);
            return;
        }

        try
        {
            LoginButton.IsEnabled = false;
            LoginButton.Content = "Logging in...";
            LoginStatusText.Text = "Authenticating...";
            LoginStatusText.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Blue);

            var result = await _authService.LoginAsync(username, password);

            if (result.Success)
            {
                LoginStatusText.Text = result.Message;
                LoginStatusText.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Green);
                
                // 認証状態を更新
                await _authStateManager.UpdateAuthStatusAsync(AuthenticationStatus.Authenticated);
                
                // Hide login overlay and show main app
                await Task.Delay(1000); // Show success message briefly
                HideLoginOverlay();
                UpdateWelcomeMessage();
            }
            else
            {
                LoginStatusText.Text = result.Message;
                LoginStatusText.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Red);
            }
        }
        catch (Exception ex)
        {
            LoginStatusText.Text = $"Login error: {ex.Message}";
            LoginStatusText.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Red);
        }
        finally
        {
            LoginButton.IsEnabled = true;
            LoginButton.Content = "Login";
        }
    }

    private async void OnGoogleLoginClicked(object sender, RoutedEventArgs e)
    {
        try
        {
            // ボタンを無効化
            if (sender is Button googleButton)
            {
                googleButton.IsEnabled = false;
                googleButton.Content = "Google認証中...";
            }

            LoginStatusText.Text = "Google認証中...";
            LoginStatusText.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Blue);

            // Google認証を実行
            var result = await _googleAuthService.LoginWithGoogleAsync();

            if (result.IsSuccess)
            {
                LoginStatusText.Text = "Google認証が成功しました";
                LoginStatusText.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Green);
                
                // 認証状態を更新
                await _authStateManager.UpdateAuthStatusAsync(AuthenticationStatus.Authenticated);
                
                if (result.IsNewUser)
                {
                    var dialog = new ContentDialog
                    {
                        Title = "ようこそ",
                        Content = "新しいアカウントが作成されました。",
                        CloseButtonText = "OK",
                        XamlRoot = this.Content.XamlRoot
                    };
                    await dialog.ShowAsync();
                }
                
                // メイン画面を表示
                await Task.Delay(1000);
                HideLoginOverlay();
                UpdateWelcomeMessage();
            }
            else
            {
                LoginStatusText.Text = result.ErrorMessage ?? "Google認証に失敗しました";
                LoginStatusText.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Red);
            }
        }
        catch (Exception ex)
        {
            LoginStatusText.Text = $"Google認証エラー: {ex.Message}";
            LoginStatusText.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Red);
        }
        finally
        {
            // ボタンを有効化
            if (sender is Button googleButton)
            {
                googleButton.IsEnabled = true;
                googleButton.Content = "Googleでログイン";
            }
        }
    }

    private async void OnLogoutClicked(object sender, RoutedEventArgs e)
    {
        try
        {
            var dialog = new ContentDialog
            {
                Title = "ログアウト確認",
                Content = "ログアウトしますか？",
                PrimaryButtonText = "はい",
                SecondaryButtonText = "いいえ",
                XamlRoot = this.Content.XamlRoot
            };

            var result = await dialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                // 認証状態をログアウト中に更新
                await _authStateManager.UpdateAuthStatusAsync(AuthenticationStatus.LoggingOut);
                
                // 通常のログアウト
                await _authService.LogoutAsync();
                
                // Google認証からもログアウト
                await _googleAuthService.LogoutAsync();
                
                // キャッシュをクリア
                await _authStateManager.ClearCacheAsync();
                
                // アプリケーション状態をクリア
                _transcriptContent = null;
                _wordListContent = null;
                OriginalTextBlock.Text = string.Empty;
                CorrectedTextBlock.Text = string.Empty;
                DiffRichTextBlock.Blocks.Clear();
                TranscriptFileText.Text = "Select transcript file";
                WordListFileText.Text = "Select word dictionary file";
                UpdateProcessButtonState();
                
                // 認証状態を未認証に更新
                await _authStateManager.UpdateAuthStatusAsync(AuthenticationStatus.NotAuthenticated);
                
                // ログイン画面を表示
                ShowLoginOverlay();
            }
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("ログアウトエラー", $"ログアウト中にエラーが発生しました: {ex.Message}");
        }
    }

    private void ShowLoginOverlay()
    {
        LoginOverlay.Visibility = Visibility.Visible;
        MainAppGrid.Opacity = 0.3;
        MainAppGrid.IsHitTestVisible = false;
        
        // Clear login form
        UsernameTextBox.Text = "admin";
        PasswordBox.Password = "1q2w3E*";
        LoginStatusText.Text = string.Empty;
    }

    private void HideLoginOverlay()
    {
        LoginOverlay.Visibility = Visibility.Collapsed;
        MainAppGrid.Opacity = 1.0;
        MainAppGrid.IsHitTestVisible = true;
    }

    private void UpdateWelcomeMessage()
    {
        if (_authService.CurrentUser != null)
        {
            WelcomeText.Text = $"Welcome, {_authService.CurrentUser.DisplayName}";
        }
        else
        {
            WelcomeText.Text = "Welcome, User";
        }
    }

    private bool IsUserAuthenticated()
    {
        return _authService.IsAuthenticated;
    }

    private bool CanUserAccessFeature(string feature)
    {
        if (!IsUserAuthenticated())
            return false;

        var user = _authService.CurrentUser;
        if (user == null)
            return false;

        // Basic access control - all authenticated users can use transcript correction
        switch (feature.ToLower())
        {
            case "transcript_correction":
                return true;
            case "admin_features":
                return user.IsAdmin;
            default:
                return true;
        }
    }
}