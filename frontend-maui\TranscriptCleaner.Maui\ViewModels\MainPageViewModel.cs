using System.ComponentModel;
using System.Runtime.CompilerServices;
using TranscriptCleaner.Maui.Services;

namespace TranscriptCleaner.Maui.ViewModels
{
    public class MainPageViewModel : INotifyPropertyChanged
    {
        private readonly AuthenticationService _authService;
        private string _originalText = string.Empty;
        private string _correctedText = string.Empty;
        private string _statusMessage = "ファイルを選択するか、テキストを入力してください。";
        private bool _isProcessing = false;
        private string _selectedTranscriptFilePath = string.Empty;
        private string _selectedWordListFilePath = string.Empty;
        private string _wordListContent = string.Empty;
        private string _currentUserInfo = "";
        private bool _isLoggedIn = false;
        private string _selectedModel = "gpt-4";
        private string _processingMode = "総合訂正";
        private string _customPrompt = string.Empty;
        private bool _isCompareMode = false;

        public MainPageViewModel(AuthenticationService authService)
        {
            _authService = authService;
            UpdateLoginStatus();
        }

        public string OriginalText
        {
            get => _originalText;
            set
            {
                if (_originalText != value)
                {
                    _originalText = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CanProcess));
                }
            }
        }

        public string CorrectedText
        {
            get => _correctedText;
            set
            {
                if (_correctedText != value)
                {
                    _correctedText = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CanSave));
                    OnPropertyChanged(nameof(CanCompare));
                }
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsProcessing
        {
            get => _isProcessing;
            set
            {
                if (_isProcessing != value)
                {
                    _isProcessing = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CanProcess));
                    OnPropertyChanged(nameof(CanSave));
                }
            }
        }

        public string SelectedTranscriptFilePath
        {
            get => _selectedTranscriptFilePath;
            set
            {
                if (_selectedTranscriptFilePath != value)
                {
                    _selectedTranscriptFilePath = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SelectedWordListFilePath
        {
            get => _selectedWordListFilePath;
            set
            {
                if (_selectedWordListFilePath != value)
                {
                    _selectedWordListFilePath = value;
                    OnPropertyChanged();
                }
            }
        }

        public string WordListContent
        {
            get => _wordListContent;
            set
            {
                if (_wordListContent != value)
                {
                    _wordListContent = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CurrentUserInfo
        {
            get => _currentUserInfo;
            set
            {
                if (_currentUserInfo != value)
                {
                    _currentUserInfo = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsLoggedIn
        {
            get => _isLoggedIn;
            set
            {
                if (_isLoggedIn != value)
                {
                    _isLoggedIn = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CanProcess));
                    OnPropertyChanged(nameof(CanCompare));
                }
            }
        }

        public string SelectedModel
        {
            get => _selectedModel;
            set
            {
                if (_selectedModel != value)
                {
                    _selectedModel = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProcessingMode
        {
            get => _processingMode;
            set
            {
                if (_processingMode != value)
                {
                    _processingMode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CustomPrompt
        {
            get => _customPrompt;
            set
            {
                if (_customPrompt != value)
                {
                    _customPrompt = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsCompareMode
        {
            get => _isCompareMode;
            set
            {
                if (_isCompareMode != value)
                {
                    _isCompareMode = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool CanCompare => !string.IsNullOrWhiteSpace(OriginalText) && !string.IsNullOrWhiteSpace(CorrectedText);

        public bool CanProcess => !IsProcessing && !string.IsNullOrWhiteSpace(OriginalText) && IsLoggedIn;
        public bool CanSave => !IsProcessing && !string.IsNullOrWhiteSpace(CorrectedText);

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void UpdateStatus(string message)
        {
            StatusMessage = message;
        }

        public void SetProcessing(bool processing)
        {
            IsProcessing = processing;
        }

        public void SetLoginStatus(bool isLoggedIn, string userInfo = "")
        {
            IsLoggedIn = isLoggedIn;
            CurrentUserInfo = isLoggedIn ? userInfo : "";
        }

        public void SetUserInfo(string username)
        {
            IsLoggedIn = true;
            CurrentUserInfo = $"ユーザー: {username}";
        }

        public void UpdateLoginStatus()
        {
            if (_authService.IsAuthenticated && _authService.CurrentUser != null)
            {
                IsLoggedIn = true;
                CurrentUserInfo = $"ユーザー: {_authService.CurrentUser.DisplayName}";
            }
            else
            {
                IsLoggedIn = false;
                CurrentUserInfo = "";
            }
        }

        public async Task<bool> LogoutAsync()
        {
            var result = await _authService.LogoutAsync();
            if (result)
            {
                UpdateLoginStatus();
            }
            return result;
        }

        public bool IsAuthenticated => _authService.IsAuthenticated;
        public UserInfo? CurrentUser => _authService.CurrentUser;
    }
}