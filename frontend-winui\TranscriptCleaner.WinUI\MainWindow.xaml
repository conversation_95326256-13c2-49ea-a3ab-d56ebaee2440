<Window
    x:Class="TranscriptCleaner.WinUI.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <Grid>
        <!-- Login Overlay -->
        <Border x:Name="LoginOverlay" 
                Background="#80000000" 
                Visibility="Visible"
                Canvas.ZIndex="1000">
            <Border Background="White" 
                    CornerRadius="12" 
                    Padding="40" 
                    MaxWidth="400" 
                    MaxHeight="500"
                    HorizontalAlignment="Center" 
                    VerticalAlignment="Center">
                <StackPanel Spacing="20">
                    <TextBlock Text="TranscriptCleaner Login" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Foreground="#2c3e50"/>
                    
                    <TextBlock Text="Please login to continue" 
                               FontSize="14" 
                               HorizontalAlignment="Center"
                               Foreground="#7f8c8d"
                               Margin="0,0,0,10"/>

                    <StackPanel Spacing="15">
                        <StackPanel>
                            <TextBlock Text="Username" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox x:Name="UsernameTextBox" 
                                     PlaceholderText="Enter username"
                                     Text="admin"/>
                        </StackPanel>

                        <StackPanel>
                            <TextBlock Text="Password" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <PasswordBox x:Name="PasswordBox" 
                                         PlaceholderText="Enter password"
                                         Password="1q2w3E*"/>
                        </StackPanel>
                    </StackPanel>

                    <Button x:Name="LoginButton" 
                            Content="Login" 
                            Background="#3498db" 
                            Foreground="White"
                            FontSize="16"
                            FontWeight="Bold"
                            Height="45"
                            HorizontalAlignment="Stretch"
                            Click="OnLoginClicked"
                            CornerRadius="6"/>

                    <TextBlock x:Name="LoginStatusText" 
                               Text="" 
                               FontSize="12" 
                               HorizontalAlignment="Center"
                               TextWrapping="Wrap"
                               Margin="0,10,0,0"/>

                    <!-- Demo Credentials Info -->
                    <Border Background="#f8f9fa" 
                            Padding="15" 
                            CornerRadius="6"
                            BorderBrush="#dee2e6"
                            BorderThickness="1">
                        <StackPanel Spacing="8">
                            <TextBlock Text="Demo Credentials:" 
                                       FontWeight="SemiBold" 
                                       FontSize="12"
                                       Foreground="#495057"/>
                            <TextBlock Text="admin / 1q2w3E*" 
                                       FontSize="11" 
                                       FontFamily="Consolas"
                                       Foreground="#6c757d"/>
                            <TextBlock Text="testuser / test123" 
                                       FontSize="11" 
                                       FontFamily="Consolas"
                                       Foreground="#6c757d"/>
                            <TextBlock Text="demo / demo123" 
                                       FontSize="11" 
                                       FontFamily="Consolas"
                                       Foreground="#6c757d"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>
        </Border>

        <!-- Main Application Grid -->
        <Grid x:Name="MainAppGrid" Opacity="0.3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

        <!-- Left Sidebar: File Selection Panel -->
        <Border Grid.Column="0" Background="#f8f9fa" BorderBrush="#dee2e6" BorderThickness="0,0,1,0">
            <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                <StackPanel Spacing="20">
                    
                    <!-- Header -->
                    <TextBlock Text="Transcript Cleaner" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               Foreground="#495057"
                               Margin="0,0,0,10"/>

                    <!-- Transcript File Selection -->
                    <StackPanel Spacing="10">
                        <TextBlock Text="📄 Transcript File" 
                                   FontSize="16" 
                                   FontWeight="SemiBold"
                                   Foreground="#495057"/>
                        
                        <Border x:Name="TranscriptFileArea"
                                Background="White"
                                BorderBrush="#dee2e6"
                                BorderThickness="2"
                                CornerRadius="8"
                                Padding="15"
                                MinHeight="100"
                                AllowDrop="True"
                                Drop="OnTranscriptFileDrop"
                                DragOver="OnFileDragOver">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="8">
                                <TextBlock x:Name="TranscriptFileText" 
                                           Text="Select transcript file"
                                           HorizontalAlignment="Center"
                                           TextWrapping="Wrap"
                                           FontSize="12"
                                           Foreground="#6c757d"/>
                                <Button x:Name="SelectTranscriptButton" 
                                        Content="Select File" 
                                        Click="OnSelectTranscriptClicked"
                                        Background="#007bff"
                                        Foreground="White"
                                        CornerRadius="4"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>

                    <!-- Word Dictionary File Selection -->
                    <StackPanel Spacing="10">
                        <TextBlock Text="📝 Word Dictionary" 
                                   FontSize="16" 
                                   FontWeight="SemiBold"
                                   Foreground="#495057"/>
                        
                        <Border x:Name="WordListFileArea"
                                Background="White"
                                BorderBrush="#dee2e6"
                                BorderThickness="2"
                                CornerRadius="8"
                                Padding="15"
                                MinHeight="100"
                                AllowDrop="True"
                                Drop="OnWordListFileDrop"
                                DragOver="OnFileDragOver">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="8">
                                <TextBlock x:Name="WordListFileText" 
                                           Text="Select word dictionary file"
                                           HorizontalAlignment="Center"
                                           TextWrapping="Wrap"
                                           FontSize="12"
                                           Foreground="#6c757d"/>
                                <Button x:Name="SelectWordListButton" 
                                        Content="Select File" 
                                        Click="OnSelectWordListClicked"
                                        Background="#28a745"
                                        Foreground="White"
                                        CornerRadius="4"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>

                    <!-- Processing Settings -->
                    <StackPanel Spacing="10">
                        <TextBlock Text="⚙️ Settings" 
                                   FontSize="16" 
                                   FontWeight="SemiBold"
                                   Foreground="#495057"/>
                        
                        <ComboBox x:Name="ModelComboBox" 
                                  Header="AI Model"
                                  SelectedIndex="0"
                                  HorizontalAlignment="Stretch">
                            <ComboBoxItem Content="GPT-4"/>
                            <ComboBoxItem Content="GPT-3.5 Turbo"/>
                        </ComboBox>
                        
                        <ComboBox x:Name="ModeComboBox" 
                                  Header="Correction Mode"
                                  SelectedIndex="0"
                                  HorizontalAlignment="Stretch">
                            <ComboBoxItem Content="Comprehensive"/>
                            <ComboBoxItem Content="Basic"/>
                            <ComboBoxItem Content="Custom"/>
                        </ComboBox>
                    </StackPanel>

                    <!-- Process Button -->
                    <Button x:Name="ProcessButton" 
                            Content="Start Correction" 
                            Background="#ffc107" 
                            Foreground="Black"
                            FontSize="16"
                            FontWeight="Bold"
                            Height="50"
                            Click="OnProcessClicked"
                            IsEnabled="False"
                            CornerRadius="6"
                            HorizontalAlignment="Stretch"/>

                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Right Content Area: Results Display -->
        <Grid Grid.Column="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Spacing="20" Margin="0,0,0,20">
                <TextBlock Text="Transcript Correction Results" 
                           FontSize="24" 
                           FontWeight="Bold" 
                           VerticalAlignment="Center"/>
                <StackPanel Orientation="Horizontal" Spacing="10" VerticalAlignment="Center">
                    <TextBlock x:Name="WelcomeText" 
                               Text="Welcome, User" 
                               FontSize="14"
                               Foreground="#6c757d"
                               VerticalAlignment="Center"/>
                    <Button x:Name="LogoutButton" 
                            Content="Logout" 
                            Background="#dc3545" 
                            Foreground="White"
                            VerticalAlignment="Center"
                            CornerRadius="4"
                            Click="OnLogoutClicked"/>
                </StackPanel>
            </StackPanel>

            <!-- Results Display Grid -->
            <Grid Grid.Row="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Original Text Display -->
                <Border Grid.Row="0" 
                        Background="White"
                        BorderBrush="#dee2e6"
                        BorderThickness="1"
                        CornerRadius="8"
                        Margin="0,0,0,10">
                    <StackPanel>
                        <Border Background="#e9ecef" Padding="15" CornerRadius="8,8,0,0">
                            <TextBlock Text="Original" 
                                       FontWeight="Bold" 
                                       FontSize="16"
                                       Foreground="#495057"/>
                        </Border>
                        <ScrollViewer Height="150" Padding="15">
                            <TextBlock x:Name="OriginalTextBlock" 
                                       TextWrapping="Wrap"
                                       FontSize="14"
                                       LineHeight="20"/>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Corrected Text Display -->
                <Border Grid.Row="1" 
                        Background="White"
                        BorderBrush="#dee2e6"
                        BorderThickness="1"
                        CornerRadius="8"
                        Margin="0,5,0,10">
                    <StackPanel>
                        <Border Background="#d4edda" Padding="15" CornerRadius="8,8,0,0">
                            <TextBlock Text="Corrected" 
                                       FontWeight="Bold" 
                                       FontSize="16"
                                       Foreground="#155724"/>
                        </Border>
                        <ScrollViewer Height="150" Padding="15">
                            <TextBlock x:Name="CorrectedTextBlock" 
                                       TextWrapping="Wrap"
                                       FontSize="14"
                                       LineHeight="20"/>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Diff Display -->
                <Border Grid.Row="2" 
                        Background="White"
                        BorderBrush="#dee2e6"
                        BorderThickness="1"
                        CornerRadius="8"
                        Margin="0,5,0,0">
                    <StackPanel>
                        <StackPanel Background="#f8f9fa" Padding="15" CornerRadius="8,8,0,0">
                            <TextBlock Text="Diff Display" FontWeight="Bold" FontSize="16" Margin="0,0,0,8"/>
                            <StackPanel Orientation="Horizontal" Spacing="20">
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <Border Background="#ffecb3" Width="12" Height="12" CornerRadius="2"/>
                                    <TextBlock Text="Changed" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <Border Background="#ffcdd2" Width="12" Height="12" CornerRadius="2"/>
                                    <TextBlock Text="Deleted" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <Border Background="#c8e6c9" Width="12" Height="12" CornerRadius="2"/>
                                    <TextBlock Text="Added" FontSize="12"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                        
                        <ScrollViewer Height="150" Padding="15">
                            <RichTextBlock x:Name="DiffRichTextBlock" 
                                           FontSize="14"
                                           LineHeight="20"
                                           IsTextSelectionEnabled="True"/>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

            </Grid>
        </Grid>
        
        </Grid> <!-- MainAppGrid -->

    </Grid>
    
</Window>