using TranscriptCleanerMvc.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace TranscriptCleanerMvc.Permissions;

public class TranscriptCleanerMvcPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(TranscriptCleanerMvcPermissions.GroupName);

        //Define your own permissions here. Example:
        //myGroup.AddPermission(TranscriptCleanerMvcPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<TranscriptCleanerMvcResource>(name);
    }
}
