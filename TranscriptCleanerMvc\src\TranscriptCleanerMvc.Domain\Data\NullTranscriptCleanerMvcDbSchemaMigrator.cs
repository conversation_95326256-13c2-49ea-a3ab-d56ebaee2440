﻿using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TranscriptCleanerMvc.Data;

/* This is used if database provider does't define
 * ITranscriptCleanerMvcDbSchemaMigrator implementation.
 */
public class NullTranscriptCleanerMvcDbSchemaMigrator : ITranscriptCleanerMvcDbSchemaMigrator, ITransientDependency
{
    public Task MigrateAsync()
    {
        return Task.CompletedTask;
    }
}
