<Page
    x:Class="TranscriptCleaner.WinUI.Views.TranscriptPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Spacing="20" Margin="0,0,0,20">
            <TextBlock Text="Transcript Correction System" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       VerticalAlignment="Center"/>
            <Button x:Name="LogoutButton" 
                    Content="Logout" 
                    Background="#dc3545" 
                    Foreground="White"
                    Click="OnLogoutClicked"
                    VerticalAlignment="Center"/>
        </StackPanel>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Left Side: File Selection -->
            <StackPanel Grid.Column="0" Margin="0,0,10,0" Spacing="10">
                
                <!-- Transcript File Selection Area -->
                <Border x:Name="TranscriptFileArea"
                        Background="#f8f9fa"
                        BorderBrush="#dee2e6"
                        BorderThickness="2"
                        CornerRadius="10"
                        Padding="20"
                        MinHeight="120"
                        AllowDrop="True"
                        Drop="OnTranscriptFileDrop"
                        DragOver="OnFileDragOver">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="10">
                        <TextBlock Text="📄" FontSize="36" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="TranscriptFileText" 
                                   Text="Select transcript file"
                                   HorizontalAlignment="Center"
                                   TextWrapping="Wrap"
                                   FontSize="14"/>
                        <Button x:Name="SelectTranscriptButton" 
                                Content="Select File" 
                                Click="OnSelectTranscriptClicked"
                                Background="#007bff"
                                Foreground="White"/>
                    </StackPanel>
                </Border>

                <!-- Word List File Selection Area -->
                <Border x:Name="WordListFileArea"
                        Background="#f8f9fa"
                        BorderBrush="#dee2e6"
                        BorderThickness="2"
                        CornerRadius="10"
                        Padding="20"
                        MinHeight="120"
                        AllowDrop="True"
                        Drop="OnWordListFileDrop"
                        DragOver="OnFileDragOver">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="10">
                        <TextBlock Text="📝" FontSize="36" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="WordListFileText" 
                                   Text="Select word dictionary file"
                                   HorizontalAlignment="Center"
                                   TextWrapping="Wrap"
                                   FontSize="14"/>
                        <Button x:Name="SelectWordListButton" 
                                Content="Select File" 
                                Click="OnSelectWordListClicked"
                                Background="#28a745"
                                Foreground="White"/>
                    </StackPanel>
                </Border>

                <!-- Word List Display Area -->
                <Border x:Name="WordListDisplayArea"
                        Background="White"
                        BorderBrush="#dee2e6"
                        BorderThickness="1"
                        CornerRadius="5"
                        Padding="10"
                        Visibility="Collapsed"
                        MinHeight="200">
                    <StackPanel Spacing="10">
                        <!-- Header with controls -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" 
                                       Text="Word List" 
                                       FontWeight="Bold" 
                                       FontSize="16"
                                       VerticalAlignment="Center"/>
                            <Button Grid.Column="1" 
                                    x:Name="AddWordButton"
                                    Content="Add"
                                    Background="#007bff"
                                    Foreground="White"
                                    FontSize="12"
                                    Padding="8,4"
                                    Margin="0,0,5,0"
                                    Click="OnAddWordClicked"/>
                            <Button Grid.Column="2" 
                                    x:Name="SaveWordListButton"
                                    Content="Save"
                                    Background="#28a745"
                                    Foreground="White"
                                    FontSize="12"
                                    Padding="8,4"
                                    Click="OnSaveWordListClicked"/>
                        </Grid>
                        
                        <!-- Word List Header -->
                        <Grid Background="#f8f9fa" Padding="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Incorrect" FontWeight="Bold" Margin="5,0"/>
                            <TextBlock Grid.Column="1" Text="Correct" FontWeight="Bold" Margin="5,0"/>
                            <TextBlock Grid.Column="2" Text="Action" FontWeight="Bold" Margin="5,0"/>
                        </Grid>
                        
                        <!-- Word List Items -->
                        <ScrollViewer MaxHeight="150">
                            <ListView x:Name="WordListView" 
                                      SelectionMode="None"
                                      IsItemClickEnabled="False">
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Padding="0,2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="60"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBox Grid.Column="0" 
                                                     Text="{Binding Incorrect, Mode=TwoWay}" 
                                                     BorderThickness="0"
                                                     Background="Transparent"
                                                     Margin="2"/>
                                            <TextBox Grid.Column="1" 
                                                     Text="{Binding Correct, Mode=TwoWay}" 
                                                     BorderThickness="0"
                                                     Background="Transparent"
                                                     Margin="2"/>
                                            <Button Grid.Column="2" 
                                                    Content="❌"
                                                    FontSize="10"
                                                    Background="Transparent"
                                                    Foreground="#dc3545"
                                                    Width="30"
                                                    Height="25"
                                                    Click="OnRemoveWordClicked"
                                                    Tag="{Binding}"/>
                                        </Grid>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                            </ListView>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Process Button -->
                <Button x:Name="ProcessButton" 
                        Content="Start Correction" 
                        Background="#ffc107" 
                        Foreground="Black"
                        FontSize="16"
                        FontWeight="Bold"
                        Height="50"
                        Click="OnProcessClicked"
                        IsEnabled="False"/>

            </StackPanel>

            <!-- Right Side: Text Display Areas -->
            <Grid Grid.Column="1" Margin="10,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Original Text Display Area -->
                <Border Grid.Row="0" 
                        Background="White"
                        BorderBrush="#dee2e6"
                        BorderThickness="1"
                        CornerRadius="5"
                        Margin="0,0,0,5">
                    <StackPanel>
                        <Border Background="#e9ecef" Padding="10">
                            <TextBlock Text="Original" 
                                       FontWeight="Bold" 
                                       Foreground="#495057"/>
                        </Border>
                        <ScrollViewer x:Name="OriginalTextScrollViewer" 
                                      Height="150" 
                                      Padding="10">
                            <TextBlock x:Name="OriginalTextBlock" 
                                       TextWrapping="Wrap"
                                       FontSize="14"/>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Corrected Text Display Area -->
                <Border Grid.Row="1" 
                        Background="White"
                        BorderBrush="#dee2e6"
                        BorderThickness="1"
                        CornerRadius="5"
                        Margin="0,5,0,5">
                    <StackPanel>
                        <Border Background="#d4edda" Padding="10">
                            <TextBlock Text="Corrected" 
                                       FontWeight="Bold" 
                                       Foreground="#155724"/>
                        </Border>
                        <ScrollViewer x:Name="CorrectedTextScrollViewer" 
                                      Height="150" 
                                      Padding="10">
                            <TextBlock x:Name="CorrectedTextBlock" 
                                       TextWrapping="Wrap"
                                       FontSize="14"/>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Comparison Display Area -->
                <Border Grid.Row="2" 
                        Background="White"
                        BorderBrush="#dee2e6"
                        BorderThickness="1"
                        CornerRadius="5"
                        Margin="0,5,0,0">
                    <StackPanel>
                        <!-- Legend -->
                        <StackPanel Background="#f8f9fa" Padding="10">
                            <TextBlock Text="Diff Display" FontWeight="Bold" Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal" Spacing="15">
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <Border Background="#ffecb3" Width="15" Height="15"/>
                                    <TextBlock Text="Changed" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <Border Background="#ffcdd2" Width="15" Height="15"/>
                                    <TextBlock Text="Deleted" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <Border Background="#c8e6c9" Width="15" Height="15"/>
                                    <TextBlock Text="Added" FontSize="12"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                        
                        <!-- Diff Display -->
                        <ScrollViewer x:Name="DiffScrollViewer" 
                                      Height="150" 
                                      Padding="10">
                            <RichTextBlock x:Name="DiffRichTextBlock" 
                                           FontSize="14"
                                           IsTextSelectionEnabled="True"/>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

            </Grid>
        </Grid>
    </Grid>
</Page>