using System;
using System.Threading.Tasks;
using Shouldly;
using Xunit;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Identity
{
    public class SecurityServiceTests : TranscriptCleanerMvcApplicationTestBase<TranscriptCleanerMvcApplicationTestModule>
    {
        private readonly ISecurityService _securityService;

        public SecurityServiceTests()
        {
            _securityService = GetRequiredService<ISecurityService>();
        }

        [Fact]
        public async Task ValidateHttpsAsync_HttpsUrl_ShouldReturnTrue()
        {
            // Arrange
            var httpsUrl = "https://example.com/callback";

            // Act
            var result = await _securityService.ValidateHttpsAsync(httpsUrl);

            // Assert
            result.ShouldBeTrue();
        }

        [Fact]
        public async Task ValidateStateAsync_MatchingStates_ShouldReturnTrue()
        {
            // Arrange
            var expectedState = "test_state_123";
            var actualState = "test_state_123";

            // Act
            var result = await _securityService.ValidateStateAsync(expectedState, actualState);

            // Assert
            result.ShouldBeTrue();
        }
    }
}