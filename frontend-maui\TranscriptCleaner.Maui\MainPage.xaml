﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="TranscriptCleaner.Maui.MainPage"
             Title="TranscriptCleaner"
             BackgroundColor="#f8f9fa">

    <ContentPage.Resources>
        <Shadow x:Key="CommonShadow"
                Brush="Black"
                Offset="0,2"
                Radius="4"
                Opacity="0.1" />
    </ContentPage.Resources>

    <Grid RowDefinitions="Auto,*">
        
        <!-- ヘッダー -->
        <Border Grid.Row="0" 
                BackgroundColor="White" 
                Stroke="Transparent"
                StrokeThickness="0"
                Padding="20,15"
                Shadow="{StaticResource CommonShadow}">
            <Grid ColumnDefinitions="*,Auto">
                <StackLayout Grid.Column="0" Orientation="Horizontal" Spacing="15">
                    <Label Text="📝" FontSize="24" VerticalOptions="Center" />
                    <StackLayout Spacing="2">
                        <Label Text="TranscriptCleaner" 
                               FontSize="20" 
                               FontAttributes="Bold" 
                               TextColor="#4472c4" />
                        <Label Text="{Binding CurrentUserInfo}" 
                               FontSize="12" 
                               TextColor="#6c757d" />
                    </StackLayout>
                </StackLayout>
                <Button Grid.Column="1"
                        x:Name="LogoutBtn" 
                        Text="ログアウト" 
                        BackgroundColor="#6c757d" 
                        TextColor="White"
                        WidthRequest="90"
                        HeightRequest="35"
                        CornerRadius="17"
                        FontSize="12"
                        Clicked="OnLogoutClicked" />
            </Grid>
        </Border>
        
        <!-- メインコンテンツ -->
        <Grid Grid.Row="1" ColumnDefinitions="300,*" BackgroundColor="#f8f9fa">

            <!-- サイドバー -->
            <Border Grid.Column="0" 
                    BackgroundColor="White" 
                    Stroke="#dee2e6"
                    StrokeThickness="1"
                    Padding="20">
                <ScrollView>
                    <StackLayout Spacing="20">
                        
                        <!-- AI モデル選択 -->
                        <StackLayout Spacing="8">
                            <Label Text="AI モデル選択" 
                                   FontSize="16" 
                                   FontAttributes="Bold" 
                                   TextColor="#495057" />
                            <Picker x:Name="ModelPicker"
                                    BackgroundColor="#fafbfc"
                                    SelectedItem="{Binding SelectedModel}">
                                <Picker.Items>
                                    <x:String>gpt-4</x:String>
                                    <x:String>gpt-4-turbo</x:String>
                                    <x:String>gpt-3.5-turbo</x:String>
                                </Picker.Items>
                            </Picker>
                        </StackLayout>
                        
                        <!-- 処理モード選択 -->
                        <StackLayout Spacing="8">
                            <Label Text="処理モード" 
                                   FontSize="16" 
                                   FontAttributes="Bold" 
                                   TextColor="#495057" />
                            <Picker x:Name="ModePicker"
                                    BackgroundColor="#fafbfc"
                                    SelectedItem="{Binding ProcessingMode}">
                                <Picker.Items>
                                    <x:String>誤字脱字修正</x:String>
                                    <x:String>文法訂正</x:String>
                                    <x:String>総合訂正</x:String>
                                </Picker.Items>
                            </Picker>
                        </StackLayout>
                        
                        <!-- カスタムプロンプト -->
                        <StackLayout Spacing="8">
                            <Label Text="カスタムプロンプト" 
                                   FontSize="16" 
                                   FontAttributes="Bold" 
                                   TextColor="#495057" />
                            <Border Stroke="#dee2e6" 
                                    BackgroundColor="#fafbfc"
                                    StrokeThickness="1"
                                    StrokeShape="RoundRectangle 8"
                                    Padding="0">
                                <Editor x:Name="CustomPromptEditor"
                                        Placeholder="追加の指示があれば入力してください..."
                                        BackgroundColor="Transparent"
                                        HeightRequest="80"
                                        Text="{Binding CustomPrompt}" />
                            </Border>
                        </StackLayout>

                        <!-- ファイル選択セクション -->
                        <StackLayout Spacing="15">
                            <Label Text="📄 ファイル選択" 
                                   FontSize="16" 
                                   FontAttributes="Bold" 
                                   TextColor="#495057" />
                            
                            <!-- 議事録ファイル選択 -->
                            <StackLayout Spacing="8">
                                <Label Text="議事録ファイル" 
                                       FontSize="14" 
                                       TextColor="#6c757d" />
                                <Border Stroke="#dee2e6" 
                                        BackgroundColor="#fafbfc"
                                        StrokeThickness="2"
                                        StrokeShape="RoundRectangle 8"
                                        Padding="10"
                                        HeightRequest="60">
                                    <Border.GestureRecognizers>
                                        <TapGestureRecognizer Tapped="OnTranscriptFileAreaTapped" />
                                    </Border.GestureRecognizers>
                                    <Grid ColumnDefinitions="*,Auto">
                                        <Label Grid.Column="0"
                                               x:Name="TranscriptFileLabel"
                                               Text="📄 ここをタップしてファイル選択 (またはボタンをクリック)"
                                               FontSize="12"
                                               TextColor="#6c757d"
                                               VerticalOptions="Center" />
                                        <Button Grid.Column="1"
                                                x:Name="SelectTranscriptFileBtn" 
                                                Text="参照" 
                                                BackgroundColor="#4472c4" 
                                                TextColor="White"
                                                WidthRequest="60"
                                                HeightRequest="35"
                                                CornerRadius="17"
                                                FontSize="12"
                                                Clicked="OnSelectTranscriptFileClicked" />
                                    </Grid>
                                </Border>
                            </StackLayout>
                            
                            <!-- 誤字脱字リストファイル選択 -->
                            <StackLayout Spacing="8">
                                <Label Text="誤字脱字リスト（CSV）" 
                                       FontSize="14" 
                                       TextColor="#6c757d" />
                                <Border Stroke="#dee2e6" 
                                        BackgroundColor="#fafbfc"
                                        StrokeThickness="2"
                                        StrokeShape="RoundRectangle 8"
                                        Padding="10"
                                        HeightRequest="60">
                                    <Border.GestureRecognizers>
                                        <TapGestureRecognizer Tapped="OnWordListFileAreaTapped" />
                                    </Border.GestureRecognizers>
                                    <Grid ColumnDefinitions="*,Auto">
                                        <Label Grid.Column="0"
                                               x:Name="WordListFileLabel"
                                               Text="📊 ここをタップしてCSVファイル選択 (またはボタンをクリック)"
                                               FontSize="12"
                                               TextColor="#6c757d"
                                               VerticalOptions="Center" />
                                        <Button Grid.Column="1"
                                                x:Name="SelectWordListFileBtn" 
                                                Text="参照" 
                                                BackgroundColor="#28a745" 
                                                TextColor="White"
                                                WidthRequest="60"
                                                HeightRequest="35"
                                                CornerRadius="17"
                                                FontSize="12"
                                                Clicked="OnSelectWordListFileClicked" />
                                    </Grid>
                                </Border>
                            </StackLayout>
                        </StackLayout>

                        <!-- 誤字脱字一覧表示エリア -->
                        <StackLayout x:Name="WordListDisplaySection" Spacing="8" IsVisible="False">
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <Label Grid.Column="0"
                                       Text="📝 誤字脱字一覧" 
                                       FontSize="16" 
                                       FontAttributes="Bold" 
                                       TextColor="#495057"
                                       VerticalOptions="Center" />
                                <Button Grid.Column="1"
                                        x:Name="AddWordBtn"
                                        Text="追加"
                                        BackgroundColor="#007bff"
                                        TextColor="White"
                                        WidthRequest="50"
                                        HeightRequest="30"
                                        CornerRadius="15"
                                        FontSize="10"
                                        Margin="5,0"
                                        Clicked="OnAddWordClicked" />
                                <Button Grid.Column="2"
                                        x:Name="SaveWordListBtn"
                                        Text="保存"
                                        BackgroundColor="#28a745"
                                        TextColor="White"
                                        WidthRequest="50"
                                        HeightRequest="30"
                                        CornerRadius="15"
                                        FontSize="10"
                                        Clicked="OnSaveWordListClicked" />
                            </Grid>
                            
                            <Border Stroke="#dee2e6" 
                                    BackgroundColor="White"
                                    StrokeThickness="1"
                                    StrokeShape="RoundRectangle 8"
                                    Padding="5">
                                <StackLayout Spacing="2">
                                    <!-- ヘッダー -->
                                    <Grid ColumnDefinitions="*,*,60" BackgroundColor="#f8f9fa" Padding="8,5">
                                        <Label Grid.Column="0" Text="誤字" FontSize="12" FontAttributes="Bold" VerticalOptions="Center" />
                                        <Label Grid.Column="1" Text="正字" FontSize="12" FontAttributes="Bold" VerticalOptions="Center" />
                                        <Label Grid.Column="2" Text="操作" FontSize="12" FontAttributes="Bold" VerticalOptions="Center" HorizontalOptions="Center" />
                                    </Grid>
                                    
                                    <!-- アイテムリスト -->
                                    <CollectionView x:Name="WordListCollectionView" 
                                                    HeightRequest="120"
                                                    BackgroundColor="White">
                                        <CollectionView.ItemTemplate>
                                            <DataTemplate>
                                                <Grid ColumnDefinitions="*,*,60" Padding="8,4" BackgroundColor="White">
                                                    <Entry Grid.Column="0" 
                                                           Text="{Binding Incorrect}" 
                                                           BackgroundColor="Transparent"
                                                           FontSize="12"
                                                           Margin="2,0" />
                                                    <Entry Grid.Column="1" 
                                                           Text="{Binding Correct}" 
                                                           BackgroundColor="Transparent"
                                                           FontSize="12"
                                                           Margin="2,0" />
                                                    <Button Grid.Column="2" 
                                                            Text="❌"
                                                            BackgroundColor="Transparent"
                                                            TextColor="#dc3545"
                                                            WidthRequest="30"
                                                            HeightRequest="25"
                                                            FontSize="10"
                                                            CommandParameter="{Binding}"
                                                            Clicked="OnRemoveWordClicked" />
                                                </Grid>
                                            </DataTemplate>
                                        </CollectionView.ItemTemplate>
                                    </CollectionView>
                                </StackLayout>
                            </Border>
                        </StackLayout>
                        
                        <!-- 処理実行ボタン -->
                        <Button x:Name="ProcessBtn" 
                                Text="🤖 AI訂正実行" 
                                BackgroundColor="#4472c4" 
                                TextColor="White"
                                FontSize="16"
                                FontAttributes="Bold"
                                HeightRequest="50"
                                CornerRadius="25"
                                Clicked="OnProcessClicked"
                                IsEnabled="{Binding CanProcess}" />
                        
                    </StackLayout>
                </ScrollView>
            </Border>

            <!-- メインコンテンツエリア -->
            <Border Grid.Column="1" 
                    BackgroundColor="White" 
                    Stroke="Transparent"
                    StrokeThickness="0"
                    Padding="20">
                <Grid RowDefinitions="Auto,*,Auto">
                    
                    <!-- 結果表示ヘッダー -->
                    <Grid Grid.Row="0" ColumnDefinitions="*,Auto,Auto" Margin="0,0,0,15">
                        <Label Grid.Column="0"
                               Text="議事録表示・訂正エリア" 
                               FontSize="20" 
                               FontAttributes="Bold" 
                               TextColor="#495057"
                               VerticalOptions="Center" />
                        <Button Grid.Column="1"
                                x:Name="CompareBtn" 
                                Text="📊 差分表示" 
                                BackgroundColor="#17a2b8" 
                                TextColor="White"
                                WidthRequest="100"
                                HeightRequest="35"
                                CornerRadius="17"
                                FontSize="12"
                                Margin="0,0,10,0"
                                Clicked="OnCompareClicked"
                                IsEnabled="{Binding CanCompare}" />
                        <Button Grid.Column="2"
                                x:Name="SaveBtn" 
                                Text="💾 保存" 
                                BackgroundColor="#28a745" 
                                TextColor="White"
                                WidthRequest="80"
                                HeightRequest="35"
                                CornerRadius="17"
                                FontSize="12"
                                Clicked="OnSaveClicked"
                                IsEnabled="{Binding CanSave}" />
                    </Grid>
                    
                    <!-- メインコンテンツエリア - 上下分割 -->
                    <Grid Grid.Row="1" x:Name="MainContentGrid" RowDefinitions="*,*">
                        
                        <!-- 上半分: 議事録表示エリア -->
                        <Grid Grid.Row="0" x:Name="TextDisplayGrid" ColumnDefinitions="*,5,*" Margin="0,0,0,5">
                            
                            <!-- 元テキスト表示 -->
                            <Border Grid.Column="0" 
                                    x:Name="OriginalTextBorder"
                                    BackgroundColor="#fff8dc" 
                                    Stroke="#ffd700" 
                                    StrokeThickness="2"
                                    StrokeShape="RoundRectangle 8"
                                    Padding="10"
                                    IsVisible="False">
                                <StackLayout Spacing="8">
                                    <Label Text="📝 元のテキスト" 
                                           FontSize="14" 
                                           FontAttributes="Bold" 
                                           TextColor="#b8860b" />
                                    <ScrollView>
                                        <Editor x:Name="OriginalTextEditor" 
                                                BackgroundColor="White"
                                                FontSize="12"
                                                IsReadOnly="True"
                                                HeightRequest="200"
                                                Text="{Binding OriginalText}" />
                                    </ScrollView>
                                </StackLayout>
                            </Border>
                            
                            <!-- 訂正後テキスト表示 -->
                            <Border Grid.Column="2" 
                                    x:Name="CorrectedTextBorder"
                                    BackgroundColor="#f0fff0" 
                                    Stroke="#32cd32" 
                                    StrokeThickness="2"
                                    StrokeShape="RoundRectangle 8"
                                    Padding="10"
                                    IsVisible="False">
                                <StackLayout Spacing="8">
                                    <Label Text="✨ 訂正後テキスト" 
                                           FontSize="14" 
                                           FontAttributes="Bold" 
                                           TextColor="#228b22" />
                                    <ScrollView>
                                        <Editor x:Name="CorrectedTextEditor" 
                                                BackgroundColor="White"
                                                FontSize="12"
                                                IsReadOnly="True"
                                                HeightRequest="200"
                                                Text="{Binding CorrectedText}" />
                                    </ScrollView>
                                </StackLayout>
                            </Border>
                            
                            <!-- 初期メッセージ表示 -->
                            <Border Grid.Column="0" Grid.ColumnSpan="3"
                                    x:Name="InitialMessageBorder"
                                    BackgroundColor="#fafbfc" 
                                    Stroke="#dee2e6" 
                                    StrokeThickness="1"
                                    StrokeShape="RoundRectangle 8"
                                    Padding="20">
                                <StackLayout HorizontalOptions="Center" VerticalOptions="Center" Spacing="15">
                                    <Label Text="📄" FontSize="48" HorizontalOptions="Center" />
                                    <Label Text="議事録ファイルを選択してください" 
                                           FontSize="16" 
                                           HorizontalOptions="Center"
                                           TextColor="#6c757d" />
                                    <Label Text="ファイル選択後、AI訂正を実行すると比較表示されます" 
                                           FontSize="12" 
                                           HorizontalOptions="Center"
                                           TextColor="#adb5bd" />
                                </StackLayout>
                            </Border>
                            
                        </Grid>
                        
                        <!-- 下半分: 差分表示エリア -->
                        <Border Grid.Row="1" 
                                x:Name="DiffDisplayBorder"
                                BackgroundColor="#f8f9fa" 
                                Stroke="#ced4da" 
                                StrokeThickness="1"
                                StrokeShape="RoundRectangle 8"
                                Padding="15"
                                Margin="0,5,0,0"
                                IsVisible="False">
                            <StackLayout Spacing="10">
                                <Grid ColumnDefinitions="*,Auto">
                                    <Label Grid.Column="0"
                                           Text="📊 単語レベル差分表示" 
                                           FontSize="16" 
                                           FontAttributes="Bold" 
                                           TextColor="#495057" />
                                    <StackLayout Grid.Column="1" Orientation="Horizontal" Spacing="15">
                                        <!-- 凡例 -->
                                        <StackLayout Orientation="Horizontal" Spacing="5">
                                            <BoxView Color="#ffcccb" WidthRequest="15" HeightRequest="15" />
                                            <Label Text="削除" FontSize="12" TextColor="#dc3545" />
                                        </StackLayout>
                                        <StackLayout Orientation="Horizontal" Spacing="5">
                                            <BoxView Color="#90ee90" WidthRequest="15" HeightRequest="15" />
                                            <Label Text="追加" FontSize="12" TextColor="#28a745" />
                                        </StackLayout>
                                        <StackLayout Orientation="Horizontal" Spacing="5">
                                            <BoxView Color="#ffffe0" WidthRequest="15" HeightRequest="15" />
                                            <Label Text="変更" FontSize="12" TextColor="#ffc107" />
                                        </StackLayout>
                                    </StackLayout>
                                </Grid>
                                <ScrollView>
                                    <Label x:Name="DiffResultLabel" 
                                           FontSize="12"
                                           LineBreakMode="WordWrap"
                                           Text="差分を表示するには、議事録ファイルを選択してAI訂正を実行してください。" />
                                </ScrollView>
                            </StackLayout>
                        </Border>
                        
                    </Grid>

                    <!-- ステータス表示 -->
                    <Border Grid.Row="2" 
                            BackgroundColor="#e9ecef" 
                            Stroke="#ced4da" 
                            StrokeThickness="1"
                            StrokeShape="RoundRectangle 5"
                            Padding="15"
                            Margin="0,15,0,0">
                        <StackLayout Orientation="Horizontal" Spacing="10">
                            <ActivityIndicator x:Name="LoadingIndicator" 
                                               IsVisible="{Binding IsProcessing}" 
                                               IsRunning="{Binding IsProcessing}"
                                               Color="#4472c4" />
                            <Label x:Name="StatusLabel" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14" 
                                   TextColor="#6c757d" 
                                   VerticalOptions="Center" />
                        </StackLayout>
                    </Border>
                    
                </Grid>
            </Border>

        </Grid>
    </Grid>

</ContentPage>