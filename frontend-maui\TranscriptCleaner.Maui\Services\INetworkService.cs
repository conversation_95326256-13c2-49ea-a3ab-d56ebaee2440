

namespace TranscriptCleaner.Maui.Services;

/// <summary>
/// ネットワークサービスインターフェース
/// </summary>
public interface INetworkService
{
    /// <summary>
    /// ネットワーク接続状態
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// ネットワーク状態変更イベント
    /// </summary>
    event EventHandler<NetworkStatusChangedEventArgs> NetworkStatusChanged;

    /// <summary>
    /// ネットワーク接続を確認
    /// </summary>
    /// <returns>接続状態</returns>
    Task<bool> CheckConnectivityAsync();

    /// <summary>
    /// サーバー接続を確認
    /// </summary>
    /// <returns>サーバー接続状態</returns>
    Task<bool> CheckServerConnectivityAsync();
}

/// <summary>
/// ネットワーク状態変更イベント引数
/// </summary>
public class NetworkStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 接続状態
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// 接続タイプ
    /// </summary>
    public string ConnectionType { get; set; } = string.Empty;

    public NetworkStatusChangedEventArgs(bool isConnected, string connectionType = "")
    {
        IsConnected = isConnected;
        ConnectionType = connectionType;
    }
}