# TranscriptCleaner Google認証 ユーザーガイド

TranscriptCleanerでGoogle認証を使用して、より便利で安全にアプリケーションをご利用いただけます。このガイドでは、Google認証の使用方法とアカウント管理について説明します。

## 目次

1. [Google認証とは](#google認証とは)
2. [初回ログイン](#初回ログイン)
3. [アカウント連携](#アカウント連携)
4. [プロフィール管理](#プロフィール管理)
5. [セキュリティ設定](#セキュリティ設定)
6. [よくある質問](#よくある質問)
7. [トラブルシューティング](#トラブルシューティング)

## Google認証とは

Google認証を使用することで、以下のメリットがあります：

- **簡単ログイン**: Googleアカウントでワンクリックログイン
- **安全性**: Googleの高度なセキュリティ機能を活用
- **プロフィール同期**: Googleアカウントの情報を自動同期
- **パスワード不要**: 複雑なパスワードを覚える必要がありません

## 初回ログイン

### Webブラウザでのログイン

1. **ログインページにアクセス**
   - TranscriptCleanerのログインページを開きます
   - 「Google でログイン」ボタンをクリックします

   ![Google Login Button](images/google-login-button.png)

2. **Google認証画面**
   - Googleの認証画面が表示されます
   - 使用したいGoogleアカウントを選択します
   - パスワードを入力します（必要に応じて）

   ![Google Auth Screen](images/google-auth-screen.png)

3. **権限の確認**
   - TranscriptCleanerがアクセスする情報の確認画面が表示されます
   - 「許可」をクリックして続行します

   ![Permission Screen](images/permission-screen.png)

4. **ログイン完了**
   - 自動的にTranscriptCleanerのダッシュボードにリダイレクトされます
   - 右上にGoogleアカウントのプロフィール画像が表示されます

### モバイルアプリでのログイン

#### Android/iOS (MAUI)

1. **アプリを起動**
   - TranscriptCleanerアプリを開きます
   - 「Google でログイン」ボタンをタップします

2. **ブラウザ認証**
   - システムブラウザが開きます
   - Googleアカウントでログインします
   - 認証完了後、自動的にアプリに戻ります

3. **生体認証の設定（オプション）**
   - 初回ログイン後、生体認証（指紋・顔認証）の設定が可能です
   - 次回からより簡単にログインできます

#### Windows (WinUI)

1. **アプリを起動**
   - TranscriptCleanerアプリを開きます
   - 「Google でログイン」ボタンをクリックします

2. **アプリ内認証**
   - アプリ内でGoogle認証画面が表示されます
   - Googleアカウントでログインします

3. **Windows Hello連携（オプション）**
   - Windows Helloが利用可能な場合、設定できます
   - 次回からWindows Helloでログインできます

## アカウント連携

既存のTranscriptCleanerアカウントをお持ちの場合、Googleアカウントと連携できます。

### 既存アカウントとの連携

1. **アカウント管理ページにアクセス**
   - ログイン後、右上のプロフィールアイコンをクリック
   - 「アカウント設定」を選択
   - 「外部アカウント連携」タブをクリック

   ![Account Settings](images/account-settings.png)

2. **Google連携の設定**
   - 「Google アカウントを連携」ボタンをクリック
   - Google認証を完了します
   - 連携状況が「連携済み」と表示されます

   ![Link Google Account](images/link-google-account.png)

3. **連携の確認**
   - 連携後、以下の情報が同期されます：
     - プロフィール画像
     - 表示名（設定により）
     - メールアドレス

### 連携の解除

1. **アカウント管理ページで解除**
   - 「外部アカウント連携」タブを開く
   - Google連携の「解除」ボタンをクリック
   - 確認ダイアログで「解除する」をクリック

2. **解除後の注意点**
   - Googleアカウントでのログインができなくなります
   - 従来のメール・パスワードでのログインが必要になります
   - プロフィール画像は削除されます

## プロフィール管理

### プロフィール情報の同期

Google認証を使用すると、以下の情報が自動的に同期されます：

- **プロフィール画像**: Googleアカウントの写真
- **表示名**: Googleアカウントの名前
- **メールアドレス**: Googleアカウントのメールアドレス

### 同期設定の変更

1. **プロフィール設定ページ**
   - 「アカウント設定」→「プロフィール」タブ
   - 「Google同期設定」セクション

2. **同期項目の選択**
   - ☑ プロフィール画像を同期
   - ☑ 表示名を同期
   - ☐ メールアドレスを同期（変更不可）

3. **手動同期**
   - 「今すぐ同期」ボタンで最新情報を取得
   - 通常は自動的に同期されます

### プロフィール画像の管理

- **自動更新**: Googleアカウントの画像変更時に自動更新
- **手動アップロード**: 独自の画像をアップロード可能
- **削除**: プロフィール画像を削除してデフォルトに戻す

## セキュリティ設定

### 二段階認証

Google認証を使用する場合、Googleアカウントの二段階認証設定が適用されます：

1. **Googleアカウントで二段階認証を有効化**
   - [Google アカウント設定](https://myaccount.google.com/security)にアクセス
   - 「2段階認証プロセス」を有効にします

2. **TranscriptCleanerでの効果**
   - ログイン時に追加の認証が求められます
   - より高いセキュリティレベルを実現

### セッション管理

- **自動ログアウト**: 一定時間操作がない場合、自動的にログアウト
- **デバイス管理**: 複数デバイスでの同時ログインが可能
- **セッション履歴**: ログイン履歴の確認が可能

### アクセス権限の管理

1. **権限の確認**
   - 「アカウント設定」→「セキュリティ」タブ
   - 「アプリの権限」セクション

2. **権限の取り消し**
   - 必要に応じてGoogleアカウントからアクセス権限を取り消し可能
   - [Google アカウント設定](https://myaccount.google.com/permissions)で管理

## よくある質問

### Q1: Google認証は無料で使用できますか？

**A**: はい、Google認証機能は無料でご利用いただけます。Googleアカウントをお持ちであれば、追加費用は発生しません。

### Q2: 複数のGoogleアカウントを使用できますか？

**A**: 一つのTranscriptCleanerアカウントには、一つのGoogleアカウントのみ連携できます。別のGoogleアカウントを使用したい場合は、現在の連携を解除してから新しいアカウントで連携してください。

### Q3: Googleアカウントを削除した場合はどうなりますか？

**A**: Googleアカウントが削除された場合、Google認証でのログインができなくなります。事前にメール・パスワードでのログイン方法を設定しておくことをお勧めします。

### Q4: 会社のGoogleアカウントを使用しても大丈夫ですか？

**A**: 技術的には可能ですが、会社のポリシーを確認してください。また、退職時にアクセスできなくなる可能性があるため、個人のアカウントの使用をお勧めします。

### Q5: プロフィール情報の同期を停止できますか？

**A**: はい、アカウント設定でプロフィール画像と表示名の同期を個別に無効にできます。ただし、メールアドレスの同期は無効にできません。

### Q6: オフラインでもログインできますか？

**A**: モバイルアプリ（MAUI）とデスクトップアプリ（WinUI）では、一度ログインした後はオフラインでも認証情報が保持され、ログインできます。ただし、Webブラウザ版ではインターネット接続が必要です。

### Q7: 生体認証は安全ですか？

**A**: はい、生体認証情報はデバイス内でのみ処理され、外部に送信されることはありません。また、生体認証はローカル認証のみに使用され、サーバー認証には使用されません。

## トラブルシューティング

### ログインできない場合

#### 症状1: 「Google でログイン」ボタンが表示されない

**解決方法**:
1. ブラウザのキャッシュをクリアしてください
2. 別のブラウザで試してください
3. JavaScript が有効になっているか確認してください
4. 管理者にGoogle認証が有効になっているか確認してください

#### 症状2: Google認証画面でエラーが発生する

**解決方法**:
1. Googleアカウントのパスワードが正しいか確認してください
2. Googleアカウントがロックされていないか確認してください
3. 二段階認証が設定されている場合、認証コードを正しく入力してください
4. しばらく時間をおいてから再試行してください

#### 症状3: 認証後にエラーページが表示される

**解決方法**:
1. ブラウザの戻るボタンでログインページに戻り、再試行してください
2. 別のブラウザで試してください
3. 問題が続く場合は、サポートにお問い合わせください

### アカウント連携の問題

#### 症状1: 既存アカウントと連携できない

**解決方法**:
1. 既存アカウントでログインしていることを確認してください
2. Googleアカウントが他のTranscriptCleanerアカウントと連携していないか確認してください
3. メールアドレスが一致しているか確認してください

#### 症状2: 連携後にプロフィール情報が更新されない

**解決方法**:
1. アカウント設定で「今すぐ同期」ボタンをクリックしてください
2. ブラウザを更新してください
3. 同期設定が有効になっているか確認してください

### モバイルアプリの問題

#### 症状1: アプリでGoogle認証が開始されない

**解決方法**:
1. アプリを最新版に更新してください
2. デバイスのインターネット接続を確認してください
3. アプリの権限設定を確認してください
4. アプリを再起動してください

#### 症状2: 認証後にアプリに戻らない

**解決方法**:
1. ブラウザからアプリに手動で戻ってください
2. アプリを再起動してください
3. デバイスを再起動してください

### デスクトップアプリの問題

#### 症状1: Windows Helloが使用できない

**解決方法**:
1. Windows Helloが設定されているか確認してください
2. アプリの権限設定を確認してください
3. Windowsを最新版に更新してください

#### 症状2: 認証情報が保存されない

**解決方法**:
1. アプリを管理者権限で実行してください
2. ウイルス対策ソフトがブロックしていないか確認してください
3. Windowsの資格情報マネージャーを確認してください

## サポート

### お問い合わせ前の確認事項

問題が発生した場合、以下の情報をご準備ください：

1. **使用環境**
   - OS（Windows、macOS、Android、iOS）
   - ブラウザ（Chrome、Firefox、Safari、Edge）
   - アプリのバージョン

2. **エラー情報**
   - エラーメッセージの内容
   - エラーが発生した手順
   - スクリーンショット（可能であれば）

3. **アカウント情報**
   - 使用しているメールアドレス
   - 連携しているGoogleアカウント
   - 最後に正常に動作した日時

### サポート連絡先

- **メール**: <EMAIL>
- **サポートページ**: https://support.transcriptcleaner.com
- **FAQ**: https://faq.transcriptcleaner.com

### 緊急時の対応

アカウントにアクセスできない緊急事態の場合：

1. **パスワードリセット**: メール・パスワードでのログインを試してください
2. **管理者連絡**: 組織のアカウントの場合、管理者にお問い合わせください
3. **サポート連絡**: 上記の方法で解決しない場合、サポートにご連絡ください

---

このガイドは定期的に更新されます。最新の情報については、アプリ内のヘルプページまたはサポートサイトをご確認ください。

Google認証を活用して、より便利で安全なTranscriptCleanerをお楽しみください！