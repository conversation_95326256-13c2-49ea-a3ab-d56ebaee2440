

namespace TranscriptCleaner.Maui.Services
{
    /// <summary>
    /// オフライン認証サービスインターフェース
    /// </summary>
    public interface IOfflineAuthService
    {
        /// <summary>
        /// オフライン認証情報を保存
        /// </summary>
        /// <param name="authData">認証データ</param>
        /// <returns>保存処理</returns>
        Task SaveOfflineAuthAsync(OfflineAuthData authData);

        /// <summary>
        /// オフライン認証情報を取得
        /// </summary>
        /// <returns>認証データ</returns>
        Task<OfflineAuthData?> GetOfflineAuthAsync();

        /// <summary>
        /// オフライン認証情報をクリア
        /// </summary>
        /// <returns>クリア処理</returns>
        Task ClearOfflineAuthAsync();

        /// <summary>
        /// オフライン認証の有効性を検証
        /// </summary>
        /// <returns>有効性</returns>
        Task<bool> ValidateOfflineAuthAsync();

        /// <summary>
        /// オンライン復帰時の同期処理
        /// </summary>
        /// <returns>同期処理</returns>
        Task<bool> SyncOnNetworkRestoreAsync();

        /// <summary>
        /// オフラインデータの同期
        /// </summary>
        /// <returns>同期結果</returns>
        Task<SyncResult> SyncOfflineDataAsync();

        /// <summary>
        /// ネットワーク状態の変更を監視
        /// </summary>
        /// <returns>監視処理</returns>
        Task StartNetworkMonitoringAsync();

        /// <summary>
        /// ネットワーク監視を停止
        /// </summary>
        /// <returns>停止処理</returns>
        Task StopNetworkMonitoringAsync();
    }

    /// <summary>
    /// オフライン認証データ
    /// </summary>
    public class OfflineAuthData
    {
        /// <summary>
        /// ユーザーID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// アクセストークン
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// リフレッシュトークン
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// ユーザー情報
        /// </summary>
        public GoogleUserInfo? UserInfo { get; set; }

        /// <summary>
        /// 有効期限
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// 作成日時
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 最終更新日時
        /// </summary>
        public DateTime LastUpdatedAt { get; set; }

        /// <summary>
        /// オフラインモードフラグ
        /// </summary>
        public bool IsOfflineMode { get; set; }

        /// <summary>
        /// 有効性チェック
        /// </summary>
        public bool IsValid => DateTime.UtcNow < ExpiresAt;
    }

    /// <summary>
    /// 同期結果
    /// </summary>
    public class SyncResult
    {
        /// <summary>
        /// 成功フラグ
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 同期されたアイテム数
        /// </summary>
        public int SyncedItems { get; set; }

        /// <summary>
        /// 競合数
        /// </summary>
        public int Conflicts { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 同期時刻
        /// </summary>
        public DateTime SyncTime { get; set; }

        /// <summary>
        /// 成功結果を作成
        /// </summary>
        public static SyncResult Success(int syncedItems, int conflicts = 0)
        {
            return new SyncResult
            {
                IsSuccess = true,
                SyncedItems = syncedItems,
                Conflicts = conflicts,
                SyncTime = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 失敗結果を作成
        /// </summary>
        public static SyncResult Failure(string errorMessage)
        {
            return new SyncResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                SyncTime = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// 認証結果
    /// </summary>
    public class AuthResult
    {
        /// <summary>
        /// 成功フラグ
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// アクセストークン
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// ユーザー情報
        /// </summary>
        public GoogleUserInfo? UserInfo { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 認証タイプ
        /// </summary>
        public AuthType AuthType { get; set; }

        /// <summary>
        /// 成功結果を作成
        /// </summary>
        public static AuthResult Success(string accessToken, GoogleUserInfo userInfo, AuthType authType = AuthType.Online)
        {
            return new AuthResult
            {
                IsSuccess = true,
                AccessToken = accessToken,
                UserInfo = userInfo,
                AuthType = authType
            };
        }

        /// <summary>
        /// 失敗結果を作成
        /// </summary>
        public static AuthResult Failure(string errorMessage)
        {
            return new AuthResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 認証タイプ
    /// </summary>
    public enum AuthType
    {
        /// <summary>
        /// オンライン認証
        /// </summary>
        Online,

        /// <summary>
        /// オフライン認証
        /// </summary>
        Offline,

        /// <summary>
        /// キャッシュ認証
        /// </summary>
        Cached
    }

    /// <summary>
    /// ネットワーク状態
    /// </summary>
    public enum NetworkStatus
    {
        /// <summary>
        /// オンライン
        /// </summary>
        Online,

        /// <summary>
        /// オフライン
        /// </summary>
        Offline,

        /// <summary>
        /// 不明
        /// </summary>
        Unknown
    }
}