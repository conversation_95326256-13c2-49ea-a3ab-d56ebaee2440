using System;

namespace TranscriptCleanerMvc.Transcripts.Dtos;

public class TranscriptCorrectionResponseDto
{
    public Guid TranscriptId { get; set; }
    
    public bool Success { get; set; }
    
    public string CorrectedText { get; set; } = string.Empty;
    
    public string? ErrorMessage { get; set; }
    
    public int OriginalLength { get; set; }
    
    public int CorrectedLength { get; set; }
    
    public int ProcessingTimeMs { get; set; }
    
    public decimal? ProcessingCost { get; set; }
}