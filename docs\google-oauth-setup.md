# Google OAuth 2.0 設定ガイド

TranscriptCleanerMvcアプリケーションでGoogle認証を使用するためのGoogle Cloud Console設定手順です。

## 前提条件

- Googleアカウント
- Google Cloud Consoleへのアクセス権限
- TranscriptCleanerMvcアプリケーションが https://localhost:44396 で動作していること

## 1. Google Cloud Consoleプロジェクトの作成

### 1.1 Google Cloud Consoleにアクセス

1. [Google Cloud Console](https://console.cloud.google.com/) にアクセス
2. Googleアカウントでログイン

### 1.2 新しいプロジェクトの作成

1. 画面上部の「プロジェクトを選択」をクリック
2. 「新しいプロジェクト」をクリック
3. プロジェクト情報を入力：
   - **プロジェクト名**: `TranscriptCleaner-OAuth`
   - **組織**: （任意）
4. 「作成」をクリック

## 2. OAuth 2.0 認証情報の設定

### 2.1 OAuth同意画面の設定

1. 左側メニューから「APIとサービス」→「OAuth同意画面」を選択
2. ユーザータイプを選択：
   - **外部**: 一般ユーザー向け（推奨）
   - **内部**: Google Workspace組織内のみ
3. 「作成」をクリック

### 2.2 OAuth同意画面の詳細設定

#### アプリ情報
- **アプリ名**: `TranscriptCleaner`
- **ユーザーサポートメール**: あなたのメールアドレス
- **アプリのロゴ**: （任意）

#### アプリドメイン
- **アプリのホームページ**: `https://localhost:44396`
- **プライバシーポリシーのリンク**: `https://localhost:44396/privacy`
- **利用規約のリンク**: `https://localhost:44396/terms`

#### 承認済みドメイン
- `localhost` を追加

#### 開発者の連絡先情報
- あなたのメールアドレスを入力

「保存して次へ」をクリック

### 2.3 スコープの設定

1. 「スコープを追加または削除」をクリック
2. 以下のスコープを選択：
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
   - `openid`
3. 「更新」をクリック
4. 「保存して次へ」をクリック

### 2.4 テストユーザーの追加（開発時のみ）

1. 「テストユーザーを追加」をクリック
2. テスト用のGoogleアカウントのメールアドレスを追加
3. 「保存して次へ」をクリック

## 3. OAuth 2.0 クライアントIDの作成

### 3.1 認証情報の作成

1. 左側メニューから「APIとサービス」→「認証情報」を選択
2. 「認証情報を作成」→「OAuth 2.0 クライアントID」をクリック

### 3.2 クライアントIDの設定

#### アプリケーションの種類
- **ウェブアプリケーション** を選択

#### 名前
- `TranscriptCleaner-Web-Client`

#### 承認済みのJavaScript生成元
以下のURIを追加：
```
https://localhost:44396
```

#### 承認済みのリダイレクトURI
以下のURIを追加：
```
https://localhost:44396/signin-google
```

### 3.3 クライアント情報の取得

1. 「作成」をクリック
2. 表示されるダイアログから以下の情報をコピー：
   - **クライアントID**: `242510620260-xxxxxxxxxxxxxxxxx.apps.googleusercontent.com`
   - **クライアントシークレット**: `GOCSPX-xxxxxxxxxxxxxxxxxxxxxxxx`

⚠️ **重要**: この情報は安全に保管してください。

## 4. 環境変数の設定

### 4.1 開発環境での設定

以下の環境変数を設定：

```powershell
# PowerShellの場合
$env:GOOGLE_OAUTH_CLIENT_ID="242510620260-xxxxxxxxxxxxxxxxx.apps.googleusercontent.com"
$env:GOOGLE_OAUTH_CLIENT_SECRET="GOCSPX-xxxxxxxxxxxxxxxxxxxxxxxx"
```

```bash
# Bashの場合
export GOOGLE_OAUTH_CLIENT_ID="242510620260-xxxxxxxxxxxxxxxxx.apps.googleusercontent.com"
export GOOGLE_OAUTH_CLIENT_SECRET="GOCSPX-xxxxxxxxxxxxxxxxxxxxxxxx"
```

### 4.2 appsettings.json での設定（代替方法）

`TranscriptCleanerMvc/src/TranscriptCleanerMvc.Web/appsettings.json` に追加：

```json
{
  "Authentication": {
    "Google": {
      "ClientId": "242510620260-xxxxxxxxxxxxxxxxx.apps.googleusercontent.com",
      "ClientSecret": "GOCSPX-xxxxxxxxxxxxxxxxxxxxxxxx",
      "Enabled": true,
      "Scopes": ["openid", "profile", "email"],
      "CallbackPath": "/signin-google",
      "SaveTokens": true
    }
  }
}
```

⚠️ **セキュリティ注意**: 本番環境では環境変数を使用し、appsettings.jsonにシークレットを直接記述しないでください。

## 5. 環境別設定

### 5.1 開発環境
- **ドメイン**: `localhost:44396`
- **プロトコル**: HTTPS必須
- **リダイレクトURI**: `https://localhost:44396/signin-google`

### 5.2 ステージング環境
- **ドメイン**: `staging.yourdomain.com`
- **リダイレクトURI**: `https://staging.yourdomain.com/signin-google`

### 5.3 本番環境
- **ドメイン**: `yourdomain.com`
- **リダイレクトURI**: `https://yourdomain.com/signin-google`

## 6. トラブルシューティング

### 6.1 よくあるエラー

#### `redirect_uri_mismatch`
- Google Cloud Consoleで設定したリダイレクトURIと実際のURIが一致しない
- 解決方法: リダイレクトURIの設定を確認

#### `invalid_client`
- クライアントIDまたはクライアントシークレットが間違っている
- 解決方法: 認証情報を再確認

#### `access_denied`
- ユーザーが認証を拒否した、またはアプリが未承認
- 解決方法: OAuth同意画面の設定を確認

### 6.2 デバッグ方法

1. ブラウザの開発者ツールでネットワークタブを確認
2. アプリケーションログを確認
3. Google Cloud Consoleの「APIとサービス」→「認証情報」で使用状況を確認

## 7. セキュリティベストプラクティス

### 7.1 クライアントシークレットの管理
- 環境変数またはセキュアな設定管理システムを使用
- ソースコードにハードコードしない
- 定期的にローテーション

### 7.2 スコープの最小化
- 必要最小限のスコープのみを要求
- 不要な権限は要求しない

### 7.3 HTTPS の強制
- すべての認証フローでHTTPS を使用
- 開発環境でも自己署名証明書を使用

## 8. 参考リンク

- [Google OAuth 2.0 ドキュメント](https://developers.google.com/identity/protocols/oauth2)
- [Google Cloud Console](https://console.cloud.google.com/)
- [ASP.NET Core Google認証](https://docs.microsoft.com/en-us/aspnet/core/security/authentication/social/google-logins)
- [ABP Framework 外部認証](https://docs.abp.io/en/abp/latest/Authentication/External-Login-Providers)