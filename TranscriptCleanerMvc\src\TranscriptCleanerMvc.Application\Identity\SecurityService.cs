using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// セキュリティサービス実装
    /// </summary>
    public class SecurityService : ApplicationService, ISecurityService
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IRepository<ExternalLoginLog, Guid> _externalLoginLogRepository;
        private readonly ILogger<SecurityService> _logger;

        // レート制限設定
        private const int DEFAULT_RATE_LIMIT = 100; // 1時間あたりのリクエスト数
        private const int RATE_LIMIT_WINDOW_SECONDS = 3600; // 1時間

        public SecurityService(
            IConfiguration configuration,
            IMemoryCache memoryCache,
            IRepository<ExternalLoginLog, Guid> externalLoginLogRepository,
            ILogger<SecurityService> logger)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _externalLoginLogRepository = externalLoginLogRepository;
            _logger = logger;
        }

        /// <summary>
        /// OAuth 2.0/OpenID Connect標準準拠チェック
        /// </summary>
        public async Task<OAuthValidationResult> ValidateOAuthRequestAsync(OAuthValidationRequest request)
        {
            try
            {
                _logger.LogInformation("OAuth認証リクエストを検証します。ClientId: {ClientId}", request.ClientId);

                var details = new List<ValidationDetail>();

                // クライアントID検証
                if (string.IsNullOrEmpty(request.ClientId))
                {
                    details.Add(new ValidationDetail
                    {
                        Item = "ClientId",
                        IsValid = false,
                        Message = "クライアントIDが必要です"
                    });
                }
                else
                {
                    details.Add(new ValidationDetail
                    {
                        Item = "ClientId",
                        IsValid = true,
                        Message = "クライアントIDは有効です"
                    });
                }

                // リダイレクトURI検証
                if (string.IsNullOrEmpty(request.RedirectUri))
                {
                    details.Add(new ValidationDetail
                    {
                        Item = "RedirectUri",
                        IsValid = false,
                        Message = "リダイレクトURIが必要です"
                    });
                }
                else if (!Uri.TryCreate(request.RedirectUri, UriKind.Absolute, out var redirectUri))
                {
                    details.Add(new ValidationDetail
                    {
                        Item = "RedirectUri",
                        IsValid = false,
                        Message = "リダイレクトURIの形式が無効です"
                    });
                }
                else
                {
                    details.Add(new ValidationDetail
                    {
                        Item = "RedirectUri",
                        IsValid = true,
                        Message = "リダイレクトURIは有効です"
                    });
                }

                // レスポンスタイプ検証
                if (request.ResponseType != "code")
                {
                    details.Add(new ValidationDetail
                    {
                        Item = "ResponseType",
                        IsValid = false,
                        Message = "サポートされていないレスポンスタイプです"
                    });
                }
                else
                {
                    details.Add(new ValidationDetail
                    {
                        Item = "ResponseType",
                        IsValid = true,
                        Message = "レスポンスタイプは有効です"
                    });
                }

                // HTTPS検証
                var isHttps = await ValidateHttpsAsync(request.RequestUrl);
                details.Add(new ValidationDetail
                {
                    Item = "HTTPS",
                    IsValid = isHttps,
                    Message = isHttps ? "HTTPS接続です" : "HTTPS接続が必要です"
                });

                // PKCE検証（推奨）
                if (!string.IsNullOrEmpty(request.CodeChallenge))
                {
                    var isPkceValid = !string.IsNullOrEmpty(request.CodeChallengeMethod) &&
                                     (request.CodeChallengeMethod == "S256" || request.CodeChallengeMethod == "plain");
                    details.Add(new ValidationDetail
                    {
                        Item = "PKCE",
                        IsValid = isPkceValid,
                        Message = isPkceValid ? "PKCE設定は有効です" : "PKCE設定が無効です"
                    });
                }

                // State検証（推奨）
                if (!string.IsNullOrEmpty(request.State))
                {
                    details.Add(new ValidationDetail
                    {
                        Item = "State",
                        IsValid = true,
                        Message = "Stateパラメータが設定されています"
                    });
                }

                // 全体的な検証結果
                var isValid = details.All(d => d.IsValid);

                if (isValid)
                {
                    _logger.LogInformation("OAuth認証リクエストの検証が成功しました。ClientId: {ClientId}", request.ClientId);
                    var result = OAuthValidationResult.Success();
                    result.Details = details.ToArray();
                    return result;
                }
                else
                {
                    _logger.LogWarning("OAuth認証リクエストの検証に失敗しました。ClientId: {ClientId}", request.ClientId);
                    var result = OAuthValidationResult.Failure("invalid_request", "リクエストパラメータが無効です");
                    result.Details = details.ToArray();
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "OAuth認証リクエスト検証中にエラーが発生しました");
                return OAuthValidationResult.Failure("server_error", "サーバーエラーが発生しました");
            }
        }

        /// <summary>
        /// PKCE検証
        /// </summary>
        public async Task<bool> ValidatePkceAsync(string codeVerifier, string codeChallenge, string codeChallengeMethod)
        {
            try
            {
                if (string.IsNullOrEmpty(codeVerifier) || string.IsNullOrEmpty(codeChallenge))
                {
                    return false;
                }

                string computedChallenge;
                
                if (codeChallengeMethod == "S256")
                {
                    using (var sha256 = SHA256.Create())
                    {
                        var challengeBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(codeVerifier));
                        computedChallenge = Convert.ToBase64String(challengeBytes)
                            .TrimEnd('=')
                            .Replace('+', '-')
                            .Replace('/', '_');
                    }
                }
                else if (codeChallengeMethod == "plain")
                {
                    computedChallenge = codeVerifier;
                }
                else
                {
                    return false;
                }

                var isValid = computedChallenge == codeChallenge;
                _logger.LogInformation("PKCE検証結果: {IsValid}", isValid);
                
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PKCE検証中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// State検証
        /// </summary>
        public async Task<bool> ValidateStateAsync(string expectedState, string actualState)
        {
            try
            {
                if (string.IsNullOrEmpty(expectedState) || string.IsNullOrEmpty(actualState))
                {
                    return false;
                }

                var isValid = expectedState == actualState;
                _logger.LogInformation("State検証結果: {IsValid}", isValid);
                
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "State検証中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// Nonce検証
        /// </summary>
        public async Task<bool> ValidateNonceAsync(string expectedNonce, string actualNonce)
        {
            try
            {
                if (string.IsNullOrEmpty(expectedNonce) || string.IsNullOrEmpty(actualNonce))
                {
                    return false;
                }

                var isValid = expectedNonce == actualNonce;
                _logger.LogInformation("Nonce検証結果: {IsValid}", isValid);
                
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Nonce検証中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// HTTPS強制設定チェック
        /// </summary>
        public async Task<bool> ValidateHttpsAsync(string requestUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(requestUrl))
                {
                    return false;
                }

                if (!Uri.TryCreate(requestUrl, UriKind.Absolute, out var uri))
                {
                    return false;
                }

                var isHttps = uri.Scheme.Equals("https", StringComparison.OrdinalIgnoreCase);
                
                // 開発環境では HTTP を許可する場合の設定
                var allowHttp = _configuration.GetValue<bool>("Authentication:AllowHttpInDevelopment", false);
                var isDevelopment = _configuration.GetValue<string>("Environment") == "Development";
                
                if (!isHttps && isDevelopment && allowHttp)
                {
                    _logger.LogWarning("開発環境でHTTP接続が許可されています: {Url}", requestUrl);
                    return true;
                }

                return isHttps;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "HTTPS検証中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// セキュリティヘッダーを生成
        /// </summary>
        public async Task<SecurityHeaders> GenerateSecurityHeadersAsync()
        {
            try
            {
                return new SecurityHeaders
                {
                    ContentSecurityPolicy = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none';",
                    XFrameOptions = "DENY",
                    XContentTypeOptions = "nosniff",
                    ReferrerPolicy = "strict-origin-when-cross-origin",
                    StrictTransportSecurity = "max-age=31536000; includeSubDomains",
                    XXssProtection = "1; mode=block"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "セキュリティヘッダー生成中にエラーが発生しました");
                return new SecurityHeaders();
            }
        }

        /// <summary>
        /// CSRFトークンを生成
        /// </summary>
        public async Task<string> GenerateCsrfTokenAsync()
        {
            try
            {
                var bytes = new byte[32];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(bytes);
                }
                
                var token = Convert.ToBase64String(bytes);
                
                // トークンをキャッシュに保存（有効期限付き）
                var cacheKey = $"csrf_token_{token}";
                _memoryCache.Set(cacheKey, true, TimeSpan.FromHours(1));
                
                return token;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CSRFトークン生成中にエラーが発生しました");
                throw;
            }
        }

        /// <summary>
        /// CSRFトークンを検証
        /// </summary>
        public async Task<bool> ValidateCsrfTokenAsync(string token)
        {
            try
            {
                if (string.IsNullOrEmpty(token))
                {
                    return false;
                }

                var cacheKey = $"csrf_token_{token}";
                var isValid = _memoryCache.TryGetValue(cacheKey, out _);
                
                if (isValid)
                {
                    // 使用済みトークンを削除
                    _memoryCache.Remove(cacheKey);
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CSRFトークン検証中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// レート制限チェック
        /// </summary>
        public async Task<RateLimitResult> CheckRateLimitAsync(string clientId, string ipAddress)
        {
            try
            {
                var key = $"rate_limit_{clientId}_{ipAddress}";
                var now = DateTime.UtcNow;
                var windowStart = now.AddSeconds(-RATE_LIMIT_WINDOW_SECONDS);

                if (_memoryCache.TryGetValue(key, out List<DateTime> requests))
                {
                    // 古いリクエストを削除
                    requests.RemoveAll(r => r < windowStart);
                }
                else
                {
                    requests = new List<DateTime>();
                }

                var currentCount = requests.Count;
                var limit = _configuration.GetValue<int>("Security:RateLimit", DEFAULT_RATE_LIMIT);

                if (currentCount >= limit)
                {
                    return new RateLimitResult
                    {
                        IsAllowed = false,
                        RemainingRequests = 0,
                        ResetTime = requests.Min().AddSeconds(RATE_LIMIT_WINDOW_SECONDS),
                        WindowSeconds = RATE_LIMIT_WINDOW_SECONDS,
                        Reason = "レート制限に達しました"
                    };
                }

                // 新しいリクエストを記録
                requests.Add(now);
                _memoryCache.Set(key, requests, TimeSpan.FromSeconds(RATE_LIMIT_WINDOW_SECONDS));

                return new RateLimitResult
                {
                    IsAllowed = true,
                    RemainingRequests = limit - requests.Count,
                    ResetTime = now.AddSeconds(RATE_LIMIT_WINDOW_SECONDS),
                    WindowSeconds = RATE_LIMIT_WINDOW_SECONDS
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "レート制限チェック中にエラーが発生しました");
                // エラー時は制限しない
                return new RateLimitResult
                {
                    IsAllowed = true,
                    RemainingRequests = DEFAULT_RATE_LIMIT,
                    ResetTime = DateTime.UtcNow.AddSeconds(RATE_LIMIT_WINDOW_SECONDS),
                    WindowSeconds = RATE_LIMIT_WINDOW_SECONDS
                };
            }
        }

        /// <summary>
        /// セキュリティ監査ログを記録
        /// </summary>
        public async Task LogSecurityAuditAsync(SecurityAuditLog auditLog)
        {
            try
            {
                _logger.LogInformation("セキュリティ監査ログを記録します。EventType: {EventType}, UserId: {UserId}", 
                    auditLog.EventType, auditLog.UserId);

                // TODO: 実際の監査ログテーブルに保存
                // 現在はログ出力のみ
                var logLevel = auditLog.RiskLevel switch
                {
                    RiskLevel.Critical => LogLevel.Critical,
                    RiskLevel.High => LogLevel.Error,
                    RiskLevel.Medium => LogLevel.Warning,
                    RiskLevel.Low => LogLevel.Information,
                    _ => LogLevel.Information
                };

                _logger.Log(logLevel, "セキュリティ監査: {EventType} | ユーザー: {UserId} | IP: {IpAddress} | 成功: {IsSuccess} | 詳細: {Details} | リスク: {RiskLevel}", 
                    auditLog.EventType, auditLog.UserId, auditLog.IpAddress, auditLog.IsSuccess, auditLog.EventDetails, auditLog.RiskLevel);

                // 高リスクイベントの場合は追加処理
                if (auditLog.RiskLevel >= RiskLevel.High)
                {
                    await HandleHighRiskEventAsync(auditLog);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "セキュリティ監査ログ記録中にエラーが発生しました");
                // 監査ログの記録失敗は処理を阻害しない
            }
        }

        /// <summary>
        /// 高リスクイベントの処理
        /// </summary>
        private async Task HandleHighRiskEventAsync(SecurityAuditLog auditLog)
        {
            try
            {
                _logger.LogWarning("高リスクセキュリティイベントが発生しました: {EventType}", auditLog.EventType);

                // TODO: 高リスクイベントの処理実装
                // - 管理者への通知
                // - 自動ブロック
                // - 追加監視の開始
                // など
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "高リスクイベント処理中にエラーが発生しました");
            }
        }
    }
}