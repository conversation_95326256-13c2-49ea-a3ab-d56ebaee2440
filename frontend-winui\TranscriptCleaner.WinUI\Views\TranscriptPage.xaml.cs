using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Documents;
using Microsoft.UI.Xaml.Media;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Windows.Storage.Pickers;
using Windows.Storage;
using Windows.ApplicationModel.DataTransfer;
using WinRT.Interop;
using TranscriptCleaner.WinUI.Services;

namespace TranscriptCleaner.WinUI.Views;

public class WordListItem : INotifyPropertyChanged
{
    private string _incorrect = string.Empty;
    private string _correct = string.Empty;

    public string Incorrect
    {
        get => _incorrect;
        set
        {
            if (_incorrect != value)
            {
                _incorrect = value;
                OnPropertyChanged();
            }
        }
    }

    public string Correct
    {
        get => _correct;
        set
        {
            if (_correct != value)
            {
                _correct = value;
                OnPropertyChanged();
            }
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public sealed partial class TranscriptPage : Page
{
    private readonly TranscriptCorrectionService _transcriptService;
    private string? _transcriptContent;
    private string? _wordListContent;
    private readonly ObservableCollection<WordListItem> _wordListItems;

    public TranscriptPage()
    {
        this.InitializeComponent();
        _transcriptService = new TranscriptCorrectionService();
        _wordListItems = new ObservableCollection<WordListItem>();
        WordListView.ItemsSource = _wordListItems;
    }

    private async void OnSelectTranscriptClicked(object sender, RoutedEventArgs e)
    {
        try
        {
            var picker = new FileOpenPicker();
            picker.ViewMode = PickerViewMode.List;
            picker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;
            picker.FileTypeFilter.Add(".txt");

            var window = App.MainWindow;
            var hWnd = WindowNative.GetWindowHandle(window);
            InitializeWithWindow.Initialize(picker, hWnd);

            var file = await picker.PickSingleFileAsync();
            if (file != null)
            {
                await LoadTranscriptFile(file);
            }
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("ファイル選択エラー", ex.Message);
        }
    }

    private async void OnSelectWordListClicked(object sender, RoutedEventArgs e)
    {
        try
        {
            var picker = new FileOpenPicker();
            picker.ViewMode = PickerViewMode.List;
            picker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;
            picker.FileTypeFilter.Add(".csv");

            var window = App.MainWindow;
            var hWnd = WindowNative.GetWindowHandle(window);
            InitializeWithWindow.Initialize(picker, hWnd);

            var file = await picker.PickSingleFileAsync();
            if (file != null)
            {
                await LoadWordListFile(file);
            }
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("ファイル選択エラー", ex.Message);
        }
    }

    private async void OnTranscriptFileDrop(object sender, DragEventArgs e)
    {
        if (e.DataView.Contains(StandardDataFormats.StorageItems))
        {
            var items = await e.DataView.GetStorageItemsAsync();
            if (items.Count > 0 && items[0] is StorageFile file)
            {
                await LoadTranscriptFile(file);
            }
        }
    }

    private async void OnWordListFileDrop(object sender, DragEventArgs e)
    {
        if (e.DataView.Contains(StandardDataFormats.StorageItems))
        {
            var items = await e.DataView.GetStorageItemsAsync();
            if (items.Count > 0 && items[0] is StorageFile file)
            {
                await LoadWordListFile(file);
            }
        }
    }

    private void OnFileDragOver(object sender, DragEventArgs e)
    {
        e.AcceptedOperation = Windows.ApplicationModel.DataTransfer.DataPackageOperation.Copy;
    }

    private async Task LoadTranscriptFile(StorageFile file)
    {
        try
        {
            _transcriptContent = await FileIO.ReadTextAsync(file);
            TranscriptFileText.Text = $"選択されたファイル: {file.Name}";
            ShowOriginalText(_transcriptContent);
            UpdateProcessButtonState();
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("ファイル読み込みエラー", $"ファイルの読み込みに失敗しました：{ex.Message}");
        }
    }

    private async Task LoadWordListFile(StorageFile file)
    {
        try
        {
            _wordListContent = await FileIO.ReadTextAsync(file);
            WordListFileText.Text = $"選択されたファイル: {file.Name}";
            ParseAndDisplayWordList(_wordListContent);
            UpdateProcessButtonState();
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("ファイル読み込みエラー", $"ファイルの読み込みに失敗しました：{ex.Message}");
        }
    }

    private void ParseAndDisplayWordList(string csvContent)
    {
        _wordListItems.Clear();
        
        if (string.IsNullOrWhiteSpace(csvContent))
            return;

        var lines = csvContent.Split(new char[] { '\n' }, StringSplitOptions.RemoveEmptyEntries);
        
        foreach (var line in lines)
        {
            var trimmedLine = line.Trim();
            if (string.IsNullOrEmpty(trimmedLine))
                continue;

            var columns = ParseCsvLine(trimmedLine);
            if (columns.Count >= 2)
            {
                _wordListItems.Add(new WordListItem
                {
                    Incorrect = columns[0],
                    Correct = columns[1]
                });
            }
        }

        WordListDisplayArea.Visibility = _wordListItems.Count > 0 ? Visibility.Visible : Visibility.Collapsed;
    }

    private List<string> ParseCsvLine(string line)
    {
        var result = new List<string>();
        var current = string.Empty;
        var inQuotes = false;

        for (int i = 0; i < line.Length; i++)
        {
            var c = line[i];

            if (c == '"')
            {
                inQuotes = !inQuotes;
            }
            else if (c == ',' && !inQuotes)
            {
                result.Add(current.Trim().Trim('\"'));
                current = string.Empty;
            }
            else
            {
                current += c;
            }
        }

        if (!string.IsNullOrEmpty(current))
        {
            result.Add(current.Trim().Trim('\"'));
        }

        return result;
    }

    private void UpdateProcessButtonState()
    {
        ProcessButton.IsEnabled = !string.IsNullOrEmpty(_transcriptContent) && !string.IsNullOrEmpty(_wordListContent);
    }

    private async void OnProcessClicked(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrEmpty(_transcriptContent) || string.IsNullOrEmpty(_wordListContent))
        {
            await ShowErrorDialog("エラー", "議事録ファイルと単語辞書ファイルの両方を選択してください。");
            return;
        }

        try
        {
            ProcessButton.IsEnabled = false;
            ProcessButton.Content = "Processing...";

            var correctedText = await _transcriptService.CorrectTranscriptAsync(_transcriptContent, _wordListContent);
            
            ShowCorrectedText(correctedText);
            GenerateWordLevelDiff(_transcriptContent, correctedText);

            ProcessButton.Content = "Start Correction";
            ProcessButton.IsEnabled = true;
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("Processing Error", $"Error occurred during transcript correction: {ex.Message}");
            ProcessButton.Content = "Start Correction";
            ProcessButton.IsEnabled = true;
        }
    }

    private void ShowOriginalText(string text)
    {
        OriginalTextBlock.Text = text;
    }

    private void ShowCorrectedText(string text)
    {
        CorrectedTextBlock.Text = text;
    }

    private void GenerateWordLevelDiff(string originalText, string correctedText)
    {
        try
        {
            DiffRichTextBlock.Blocks.Clear();

            var originalWords = originalText.Split(new char[] { ' ', '\n', '\r', '\t' }, StringSplitOptions.RemoveEmptyEntries);
            var correctedWords = correctedText.Split(new char[] { ' ', '\n', '\r', '\t' }, StringSplitOptions.RemoveEmptyEntries);

            var paragraph = new Paragraph();
            var changes = GetWordChanges(originalWords, correctedWords);

            foreach (var change in changes)
            {
                var run = new Run { Text = change.Text + " " };

                switch (change.Type)
                {
                    case ChangeType.Added:
                        run.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Green);
                        break;
                    case ChangeType.Deleted:
                        run.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Red);
                        break;
                    case ChangeType.Modified:
                        run.Foreground = new SolidColorBrush(Microsoft.UI.Colors.Orange);
                        break;
                    case ChangeType.Unchanged:
                        // Default style
                        break;
                }

                paragraph.Inlines.Add(run);
            }

            DiffRichTextBlock.Blocks.Add(paragraph);
        }
        catch (Exception ex)
        {
            var errorParagraph = new Paragraph();
            errorParagraph.Inlines.Add(new Run { Text = $"Error in diff display: {ex.Message}" });
            DiffRichTextBlock.Blocks.Add(errorParagraph);
        }
    }

    private List<WordChange> GetWordChanges(string[] originalWords, string[] correctedWords)
    {
        var changes = new List<WordChange>();
        int originalIndex = 0, correctedIndex = 0;

        while (originalIndex < originalWords.Length || correctedIndex < correctedWords.Length)
        {
            if (originalIndex >= originalWords.Length)
            {
                // 残りは全て追加
                changes.Add(new WordChange { Text = correctedWords[correctedIndex], Type = ChangeType.Added });
                correctedIndex++;
            }
            else if (correctedIndex >= correctedWords.Length)
            {
                // 残りは全て削除
                changes.Add(new WordChange { Text = originalWords[originalIndex], Type = ChangeType.Deleted });
                originalIndex++;
            }
            else if (originalWords[originalIndex] == correctedWords[correctedIndex])
            {
                // 同じ単語
                changes.Add(new WordChange { Text = originalWords[originalIndex], Type = ChangeType.Unchanged });
                originalIndex++;
                correctedIndex++;
            }
            else
            {
                // 異なる単語 - 単純に変更として扱う
                changes.Add(new WordChange { Text = originalWords[originalIndex], Type = ChangeType.Deleted });
                changes.Add(new WordChange { Text = correctedWords[correctedIndex], Type = ChangeType.Added });
                originalIndex++;
                correctedIndex++;
            }
        }

        return changes;
    }

    private async void OnLogoutClicked(object sender, RoutedEventArgs e)
    {
        try
        {
            var dialog = new ContentDialog
            {
                Title = "ログアウト確認",
                Content = "ログアウトしますか？",
                PrimaryButtonText = "はい",
                SecondaryButtonText = "いいえ",
                XamlRoot = this.XamlRoot
            };

            var result = await dialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                // ログイン画面に戻る
                Frame.Navigate(typeof(LoginPage));
            }
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("エラー", $"ログアウト処理でエラーが発生しました：{ex.Message}");
        }
    }

    private void OnAddWordClicked(object sender, RoutedEventArgs e)
    {
        _wordListItems.Add(new WordListItem { Incorrect = "", Correct = "" });
        WordListDisplayArea.Visibility = Visibility.Visible;
        UpdateWordListCsv();
    }

    private void OnRemoveWordClicked(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is WordListItem item)
        {
            _wordListItems.Remove(item);
            UpdateWordListCsv();
            
            if (_wordListItems.Count == 0)
            {
                WordListDisplayArea.Visibility = Visibility.Collapsed;
            }
        }
    }

    private async void OnSaveWordListClicked(object sender, RoutedEventArgs e)
    {
        try
        {
            var savePicker = new FileSavePicker();
            savePicker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;
            savePicker.FileTypeChoices.Add("CSV Files", new List<string>() { ".csv" });
            savePicker.SuggestedFileName = $"wordlist_{DateTime.Now:yyyyMMdd}";

            var window = App.MainWindow;
            var hWnd = WindowNative.GetWindowHandle(window);
            InitializeWithWindow.Initialize(savePicker, hWnd);

            var file = await savePicker.PickSaveFileAsync();
            if (file != null)
            {
                UpdateWordListCsv();
                await FileIO.WriteTextAsync(file, _wordListContent ?? "");
                
                var dialog = new ContentDialog
                {
                    Title = "保存完了",
                    Content = "誤字脱字一覧を保存しました。",
                    CloseButtonText = "OK",
                    XamlRoot = this.XamlRoot
                };
                await dialog.ShowAsync();
            }
        }
        catch (Exception ex)
        {
            await ShowErrorDialog("保存エラー", $"ファイルの保存に失敗しました：{ex.Message}");
        }
    }

    private void UpdateWordListCsv()
    {
        var csvLines = _wordListItems
            .Where(item => !string.IsNullOrWhiteSpace(item.Incorrect) && !string.IsNullOrWhiteSpace(item.Correct))
            .Select(item => $@"""{item.Incorrect.Replace(@"""", @"""""")}"",""{item.Correct.Replace(@"""", @"""""")}""");

        _wordListContent = string.Join(Environment.NewLine, csvLines);
    }

    private async Task ShowErrorDialog(string title, string message)
    {
        var dialog = new ContentDialog
        {
            Title = title,
            Content = message,
            CloseButtonText = "OK",
            XamlRoot = this.XamlRoot
        };
        await dialog.ShowAsync();
    }

    private class WordChange
    {
        public string Text { get; set; } = "";
        public ChangeType Type { get; set; }
    }

    private enum ChangeType
    {
        Unchanged,
        Added,
        Deleted,
        Modified
    }
}