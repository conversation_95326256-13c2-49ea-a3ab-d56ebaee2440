using System;
using System.Collections.Generic;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// プロフィール同期結果DTO
    /// </summary>
    public class ProfileSyncResultDto
    {
        /// <summary>
        /// 同期成功フラグ
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 同期日時
        /// </summary>
        public DateTime SyncTime { get; set; }

        /// <summary>
        /// 更新されたフィールド一覧
        /// </summary>
        public List<string> UpdatedFields { get; set; } = new();

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 同期前のプロフィール情報
        /// </summary>
        public ProfileSnapshotDto? BeforeProfile { get; set; }

        /// <summary>
        /// 同期後のプロフィール情報
        /// </summary>
        public ProfileSnapshotDto? AfterProfile { get; set; }

        /// <summary>
        /// 同期タイプ（Manual, Auto）
        /// </summary>
        public string SyncType { get; set; } = "Manual";

        /// <summary>
        /// 成功結果を作成
        /// </summary>
        public static ProfileSyncResultDto CreateSuccess(
            List<string> updatedFields,
            ProfileSnapshotDto? beforeProfile = null,
            ProfileSnapshotDto? afterProfile = null,
            string syncType = "Manual")
        {
            return new ProfileSyncResultDto
            {
                Success = true,
                SyncTime = DateTime.UtcNow,
                UpdatedFields = updatedFields,
                BeforeProfile = beforeProfile,
                AfterProfile = afterProfile,
                SyncType = syncType
            };
        }

        /// <summary>
        /// 失敗結果を作成
        /// </summary>
        public static ProfileSyncResultDto CreateFailure(string errorMessage, string syncType = "Manual")
        {
            return new ProfileSyncResultDto
            {
                Success = false,
                SyncTime = DateTime.UtcNow,
                ErrorMessage = errorMessage,
                SyncType = syncType
            };
        }
    }

    /// <summary>
    /// プロフィールスナップショットDTO
    /// </summary>
    public class ProfileSnapshotDto
    {
        /// <summary>
        /// 名前
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 姓
        /// </summary>
        public string? Surname { get; set; }

        /// <summary>
        /// メールアドレス
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// プロフィール画像URL
        /// </summary>
        public string? ProfileImageUrl { get; set; }

        /// <summary>
        /// ロケール
        /// </summary>
        public string? Locale { get; set; }
    }
}