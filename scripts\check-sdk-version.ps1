# .NET SDK バージョン確認スクリプト
# Google認証機能実装のための環境確認

Write-Host "=== .NET SDK バージョン確認 ===" -ForegroundColor Green

# 現在のSDKバージョンを確認
$currentVersion = dotnet --version
Write-Host "現在の .NET SDK バージョン: $currentVersion" -ForegroundColor Yellow

# インストール済みSDKの一覧
Write-Host "`n=== インストール済み SDK 一覧 ===" -ForegroundColor Green
dotnet --list-sdks

# 必要な最小バージョンをチェック
$requiredVersion = "9.0.0"
$current = [Version]$currentVersion
$required = [Version]$requiredVersion

if ($current -ge $required) {
    Write-Host "`n✅ .NET 9.0以上が確認されました。Google認証実装の準備完了です。" -ForegroundColor Green
} else {
    Write-Host "`n❌ .NET 9.0以上が必要です。以下のURLからダウンロードしてください:" -ForegroundColor Red
    Write-Host "https://dotnet.microsoft.com/download/dotnet/9.0" -ForegroundColor Blue
    exit 1
}

# プロジェクトのターゲットフレームワーク確認
Write-Host "`n=== プロジェクトのターゲットフレームワーク確認 ===" -ForegroundColor Green

$webProject = "TranscriptCleanerMvc/src/TranscriptCleanerMvc.Web/TranscriptCleanerMvc.Web.csproj"
if (Test-Path $webProject) {
    $content = Get-Content $webProject -Raw
    if ($content -match '<TargetFramework>(.*?)</TargetFramework>') {
        $targetFramework = $matches[1]
        Write-Host "Web プロジェクト: $targetFramework" -ForegroundColor Yellow
        
        if ($targetFramework -eq "net9.0") {
            Write-Host "✅ 既に .NET 9.0 対応済みです" -ForegroundColor Green
        } else {
            Write-Host "⚠️  .NET 9.0 への更新が推奨されます" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "⚠️  Web プロジェクトファイルが見つかりません: $webProject" -ForegroundColor Yellow
}

Write-Host "`n=== 環境確認完了 ===" -ForegroundColor Green