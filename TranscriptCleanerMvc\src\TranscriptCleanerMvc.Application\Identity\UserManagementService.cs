using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// ユーザー管理サービス実装
    /// </summary>
    [Authorize(Roles = "admin")]
    public class UserManagementService : ApplicationService, IUserManagementService
    {
        private readonly IIdentityUserRepository _userRepository;
        private readonly IIdentityRoleRepository _roleRepository;
        private readonly IRepository<ExternalLoginLog, Guid> _externalLoginLogRepository;
        private readonly IdentityUserManager _userManager;
        private readonly ILogger<UserManagementService> _logger;

        public UserManagementService(
            IIdentityUserRepository userRepository,
            IIdentityRoleRepository roleRepository,
            IRepository<ExternalLoginLog, Guid> externalLoginLogRepository,
            IdentityUserManager userManager,
            ILogger<UserManagementService> logger)
        {
            _userRepository = userRepository;
            _roleRepository = roleRepository;
            _externalLoginLogRepository = externalLoginLogRepository;
            _userManager = userManager;
            _logger = logger;
        }

        /// <summary>
        /// ユーザー一覧を取得（Google連携状況付き）
        /// </summary>
        public async Task<PagedResultDto<UserWithExternalLinksDto>> GetUsersWithExternalLinksAsync(GetUsersWithExternalLinksInput input)
        {
            try
            {
                _logger.LogInformation("ユーザー一覧を取得します。Filter: {Filter}", input.Filter);

                // IIdentityUserRepositoryにはGetQueryableAsyncがないため、直接検索を使用
                var allUsers = await _userRepository.GetListAsync();
                var filteredUsers = allUsers.AsQueryable();

                // フィルター適用
                if (!string.IsNullOrEmpty(input.Filter))
                {
                    filteredUsers = filteredUsers.Where(u => 
                        u.UserName.Contains(input.Filter) ||
                        u.Email.Contains(input.Filter) ||
                        (u.Name != null && u.Name.Contains(input.Filter)) ||
                        (u.Surname != null && u.Surname.Contains(input.Filter)));
                }

                if (input.IsActive.HasValue)
                {
                    filteredUsers = filteredUsers.Where(u => u.IsActive == input.IsActive.Value);
                }

                if (input.HasGoogleLink.HasValue)
                {
                    if (input.HasGoogleLink.Value)
                    {
                        filteredUsers = filteredUsers.Where(u => u.Logins.Any(l => l.LoginProvider == "Google"));
                    }
                    else
                    {
                        filteredUsers = filteredUsers.Where(u => !u.Logins.Any(l => l.LoginProvider == "Google"));
                    }
                }

                if (input.CreatedAfter.HasValue)
                {
                    filteredUsers = filteredUsers.Where(u => u.CreationTime >= input.CreatedAfter.Value);
                }

                if (input.CreatedBefore.HasValue)
                {
                    filteredUsers = filteredUsers.Where(u => u.CreationTime <= input.CreatedBefore.Value);
                }

                // ソート
                if (!string.IsNullOrEmpty(input.Sorting))
                {
                    // TODO: ソート実装
                }
                else
                {
                    filteredUsers = filteredUsers.OrderByDescending(u => u.CreationTime);
                }

                // ページング
                var totalCount = filteredUsers.Count();
                var users = filteredUsers
                    .Skip(input.SkipCount)
                    .Take(input.MaxResultCount)
                    .ToList();

                // DTOに変換
                var userDtos = new List<UserWithExternalLinksDto>();
                foreach (var user in users)
                {
                    var userDto = new UserWithExternalLinksDto
                    {
                        Id = user.Id,
                        UserName = user.UserName,
                        Email = user.Email,
                        Name = user.Name,
                        Surname = user.Surname,
                        PhoneNumber = user.PhoneNumber,
                        IsActive = user.IsActive,
                        EmailConfirmed = user.EmailConfirmed,
                        PhoneNumberConfirmed = user.PhoneNumberConfirmed,
                        TwoFactorEnabled = user.TwoFactorEnabled,
                        LockoutEnd = user.LockoutEnd,
                        LockoutEnabled = user.LockoutEnabled,
                        AccessFailedCount = user.AccessFailedCount,
                        CreationTime = user.CreationTime
                    };

                    // Google連携情報を取得
                    var googleLogin = user.Logins.FirstOrDefault(l => l.LoginProvider == "Google");
                    if (googleLogin != null)
                    {
                        userDto.GoogleLink = new ExternalLinkInfo
                        {
                            Provider = googleLogin.LoginProvider,
                            ProviderKey = googleLogin.ProviderKey,
                            ProviderDisplayName = googleLogin.ProviderDisplayName,
                            LinkedAt = user.CreationTime, // TODO: 実際の連携日時を取得
                            LastUsedAt = await GetLastLoginTimeAsync(user.Id, "Google")
                        };
                    }

                    // ロール情報を取得
                    var roles = await _userManager.GetRolesAsync(user);
                    userDto.Roles = roles.ToArray();

                    // 最終ログイン時刻を取得
                    userDto.LastLoginTime = await GetLastLoginTimeAsync(user.Id);

                    userDtos.Add(userDto);
                }

                return new PagedResultDto<UserWithExternalLinksDto>(totalCount, userDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ユーザー一覧取得中にエラーが発生しました");
                throw new UserFriendlyException("ユーザー一覧の取得に失敗しました");
            }
        }

        /// <summary>
        /// ユーザーのGoogle連携状況を取得
        /// </summary>
        public async Task<UserExternalLinkStatusDto> GetUserExternalLinkStatusAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("ユーザーの外部連携状況を取得します。UserId: {UserId}", userId);

                var user = await _userRepository.GetAsync(userId);
                
                var externalLinks = user.Logins.Select(login => new ExternalLinkInfo
                {
                    Provider = login.LoginProvider,
                    ProviderKey = login.ProviderKey,
                    ProviderDisplayName = login.ProviderDisplayName,
                    LinkedAt = user.CreationTime, // TODO: 実際の連携日時を取得
                    LastUsedAt = GetLastLoginTimeAsync(userId, login.LoginProvider).Result
                }).ToArray();

                return new UserExternalLinkStatusDto
                {
                    UserId = user.Id,
                    UserName = user.UserName,
                    Email = user.Email,
                    ExternalLinks = externalLinks,
                    LastUpdated = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ユーザー外部連携状況取得中にエラーが発生しました。UserId: {UserId}", userId);
                throw new UserFriendlyException("外部連携状況の取得に失敗しました");
            }
        }

        /// <summary>
        /// 管理者による強制連携解除
        /// </summary>
        public async Task<bool> ForceUnlinkExternalAccountAsync(Guid userId, string provider)
        {
            try
            {
                _logger.LogInformation("管理者による強制連携解除を実行します。UserId: {UserId}, Provider: {Provider}", userId, provider);

                var user = await _userRepository.GetAsync(userId);
                var login = user.Logins.FirstOrDefault(l => l.LoginProvider == provider);
                
                if (login == null)
                {
                    _logger.LogWarning("指定されたプロバイダーの連携が見つかりません。UserId: {UserId}, Provider: {Provider}", userId, provider);
                    return false;
                }

                // 連携を解除
                user.RemoveLogin(login.LoginProvider, login.ProviderKey);
                await _userRepository.UpdateAsync(user);

                // ログに記録
                await LogExternalLoginAsync(userId, provider, false, "管理者による強制連携解除");

                _logger.LogInformation("管理者による強制連携解除が完了しました。UserId: {UserId}, Provider: {Provider}", userId, provider);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "強制連携解除中にエラーが発生しました。UserId: {UserId}, Provider: {Provider}", userId, provider);
                throw new UserFriendlyException("連携解除に失敗しました");
            }
        }

        /// <summary>
        /// ユーザーに通知を送信
        /// </summary>
        public async Task<bool> SendUserNotificationAsync(SendUserNotificationInput input)
        {
            try
            {
                _logger.LogInformation("ユーザーに通知を送信します。UserId: {UserId}, Title: {Title}", input.UserId, input.Title);

                // TODO: 実際の通知送信実装
                // 現在はログ出力のみ
                _logger.LogInformation("通知送信: UserId={UserId}, Title={Title}, Message={Message}, Type={Type}", 
                    input.UserId, input.Title, input.Message, input.Type);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ユーザー通知送信中にエラーが発生しました。UserId: {UserId}", input.UserId);
                return false;
            }
        }

        /// <summary>
        /// 複数ユーザーに一括通知を送信
        /// </summary>
        public async Task<BulkNotificationResultDto> SendBulkNotificationAsync(SendBulkNotificationInput input)
        {
            try
            {
                _logger.LogInformation("一括通知を送信します。対象ユーザー数: {Count}", input.UserIds.Length);

                int successCount = 0;
                int failureCount = 0;
                var errors = new List<string>();

                foreach (var userId in input.UserIds)
                {
                    try
                    {
                        var notificationInput = new SendUserNotificationInput
                        {
                            UserId = userId,
                            Title = input.Title,
                            Message = input.Message,
                            Type = input.Type,
                            IsUrgent = input.IsUrgent,
                            ExpiresAt = input.ExpiresAt
                        };

                        var success = await SendUserNotificationAsync(notificationInput);
                        if (success)
                        {
                            successCount++;
                        }
                        else
                        {
                            failureCount++;
                            errors.Add($"ユーザー {userId} への通知送信に失敗しました");
                        }
                    }
                    catch (Exception ex)
                    {
                        failureCount++;
                        errors.Add($"ユーザー {userId}: {ex.Message}");
                    }
                }

                return new BulkNotificationResultDto
                {
                    SuccessCount = successCount,
                    FailureCount = failureCount,
                    TotalCount = input.UserIds.Length,
                    Errors = errors.ToArray(),
                    SentAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "一括通知送信中にエラーが発生しました");
                throw new UserFriendlyException("一括通知の送信に失敗しました");
            }
        }

        /// <summary>
        /// ユーザーのログイン履歴を取得
        /// </summary>
        public async Task<PagedResultDto<UserLoginHistoryDto>> GetUserLoginHistoryAsync(Guid userId, GetUserLoginHistoryInput input)
        {
            try
            {
                _logger.LogInformation("ユーザーのログイン履歴を取得します。UserId: {UserId}", userId);

                var queryable = await _externalLoginLogRepository.GetQueryableAsync();
                queryable = queryable.Where(log => log.UserId == userId);

                // フィルター適用
                if (!string.IsNullOrEmpty(input.Provider))
                {
                    queryable = queryable.Where(log => log.Provider == input.Provider);
                }

                if (input.IsSuccess.HasValue)
                {
                    queryable = queryable.Where(log => log.Success == input.IsSuccess.Value);
                }

                if (input.StartDate.HasValue)
                {
                    queryable = queryable.Where(log => log.CreationTime >= input.StartDate.Value);
                }

                if (input.EndDate.HasValue)
                {
                    queryable = queryable.Where(log => log.CreationTime <= input.EndDate.Value);
                }

                // ソート
                queryable = queryable.OrderByDescending(log => log.CreationTime);

                // ページング
                var totalCount = queryable.Count();
                var logs = queryable
                    .Skip(input.SkipCount)
                    .Take(input.MaxResultCount)
                    .ToList();

                var logDtos = logs.Select(log => new UserLoginHistoryDto
                {
                    Id = log.Id,
                    UserId = log.UserId,
                    Provider = log.Provider,
                    ProviderKey = log.ProviderKey,
                    IsSuccess = log.Success,
                    ErrorMessage = log.ErrorMessage,
                    IpAddress = log.IpAddress,
                    UserAgent = log.UserAgent,
                    LoginTime = log.CreationTime,
                    SessionDuration = null // TODO: セッション継続時間の計算
                }).ToList();

                return new PagedResultDto<UserLoginHistoryDto>(totalCount, logDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ユーザーログイン履歴取得中にエラーが発生しました。UserId: {UserId}", userId);
                throw new UserFriendlyException("ログイン履歴の取得に失敗しました");
            }
        }

        /// <summary>
        /// ユーザーアカウントを無効化
        /// </summary>
        public async Task<bool> DisableUserAccountAsync(Guid userId, string reason)
        {
            try
            {
                _logger.LogInformation("ユーザーアカウントを無効化します。UserId: {UserId}, Reason: {Reason}", userId, reason);

                var user = await _userRepository.GetAsync(userId);
                user.SetIsActive(false);
                await _userRepository.UpdateAsync(user);

                // ログに記録
                _logger.LogInformation("ユーザーアカウントが無効化されました。UserId: {UserId}, Reason: {Reason}, AdminUserId: {AdminUserId}", 
                    userId, reason, CurrentUser.Id);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ユーザーアカウント無効化中にエラーが発生しました。UserId: {UserId}", userId);
                throw new UserFriendlyException("アカウントの無効化に失敗しました");
            }
        }

        /// <summary>
        /// ユーザーアカウントを有効化
        /// </summary>
        public async Task<bool> EnableUserAccountAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("ユーザーアカウントを有効化します。UserId: {UserId}", userId);

                var user = await _userRepository.GetAsync(userId);
                user.SetIsActive(true);
                await _userRepository.UpdateAsync(user);

                // ログに記録
                _logger.LogInformation("ユーザーアカウントが有効化されました。UserId: {UserId}, AdminUserId: {AdminUserId}", 
                    userId, CurrentUser.Id);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ユーザーアカウント有効化中にエラーが発生しました。UserId: {UserId}", userId);
                throw new UserFriendlyException("アカウントの有効化に失敗しました");
            }
        }

        /// <summary>
        /// 最終ログイン時刻を取得
        /// </summary>
        private async Task<DateTime?> GetLastLoginTimeAsync(Guid userId, string? provider = null)
        {
            try
            {
                var queryable = await _externalLoginLogRepository.GetQueryableAsync();
                queryable = queryable.Where(log => log.UserId == userId && log.Success);

                if (!string.IsNullOrEmpty(provider))
                {
                    queryable = queryable.Where(log => log.Provider == provider);
                }

                var lastLogin = queryable
                    .OrderByDescending(log => log.CreationTime)
                    .FirstOrDefault();

                return lastLogin?.CreationTime;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "最終ログイン時刻取得中にエラーが発生しました。UserId: {UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// 外部ログインをログに記録
        /// </summary>
        private async Task LogExternalLoginAsync(Guid userId, string provider, bool isSuccess, string? errorMessage = null)
        {
            try
            {
                var log = isSuccess 
                    ? ExternalLoginLog.CreateSuccessLog(userId, provider, "admin_action", "127.0.0.1", "Admin Action")
                    : ExternalLoginLog.CreateFailureLog(userId, provider, "admin_action", errorMessage ?? "Unknown error", "127.0.0.1", "Admin Action");

                await _externalLoginLogRepository.InsertAsync(log);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "外部ログイン記録中にエラーが発生しました");
                // ログ記録の失敗は処理を阻害しない
            }
        }
    }
}