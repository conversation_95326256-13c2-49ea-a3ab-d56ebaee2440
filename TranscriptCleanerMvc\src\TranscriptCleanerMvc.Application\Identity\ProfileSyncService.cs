using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// プロフィール同期サービス実装
    /// </summary>
    public class ProfileSyncService : ApplicationService, IProfileSyncService
    {
        private readonly IRepository<AppUser, Guid> _appUserRepository;
        private readonly IRepository<ExternalLoginLog, Guid> _externalLoginLogRepository;

        public ProfileSyncService(
            IRepository<AppUser, Guid> appUserRepository,
            IRepository<ExternalLoginLog, Guid> externalLoginLogRepository)
        {
            _appUserRepository = appUserRepository;
            _externalLoginLogRepository = externalLoginLogRepository;
        }

        /// <summary>
        /// Googleプロフィールを手動同期
        /// </summary>
        public async Task<ProfileSyncResultDto> SyncGoogleProfileAsync(Guid userId)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var user = await _appUserRepository.GetAsync(userId);
                
                if (!user.IsGoogleLinked)
                {
                    return ProfileSyncResultDto.CreateFailure("Googleアカウントが連携されていません");
                }

                // 同期前のプロフィール情報を保存
                var beforeProfile = new ProfileSnapshotDto
                {
                    Name = user.Name,
                    Surname = user.Surname,
                    Email = user.Email,
                    ProfileImageUrl = user.ProfileImageUrl
                };

                // TODO: 実際のGoogle APIからプロフィール情報を取得
                // 現在はモック実装
                var updatedFields = new List<string>();
                var mockGoogleProfile = GetMockGoogleProfile();

                // 名前の更新
                if (user.Name != mockGoogleProfile.GivenName)
                {
                    user.Name = mockGoogleProfile.GivenName;
                    updatedFields.Add("Name");
                }

                // 姓の更新
                if (user.Surname != mockGoogleProfile.FamilyName)
                {
                    user.Surname = mockGoogleProfile.FamilyName;
                    updatedFields.Add("Surname");
                }

                // プロフィール画像の更新
                if (user.ProfileImageUrl != mockGoogleProfile.Picture)
                {
                    user.ProfileImageUrl = mockGoogleProfile.Picture;
                    updatedFields.Add("ProfileImageUrl");
                }

                // 同期日時を更新
                user.LastGoogleSync = DateTime.UtcNow;
                await _appUserRepository.UpdateAsync(user);

                // 同期後のプロフィール情報
                var afterProfile = new ProfileSnapshotDto
                {
                    Name = user.Name,
                    Surname = user.Surname,
                    Email = user.Email,
                    ProfileImageUrl = user.ProfileImageUrl
                };

                stopwatch.Stop();
                
                Logger.LogInformation("Googleプロフィール手動同期が完了しました。UserId: {UserId}, 更新フィールド: {UpdatedFields}, 処理時間: {ProcessingTime}ms", 
                    userId, string.Join(", ", updatedFields), stopwatch.ElapsedMilliseconds);

                return ProfileSyncResultDto.CreateSuccess(updatedFields, beforeProfile, afterProfile, "Manual");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Logger.LogError(ex, "Googleプロフィール手動同期中にエラーが発生しました。UserId: {UserId}, 処理時間: {ProcessingTime}ms", 
                    userId, stopwatch.ElapsedMilliseconds);
                
                return ProfileSyncResultDto.CreateFailure($"同期中にエラーが発生しました: {ex.Message}", "Manual");
            }
        }

        /// <summary>
        /// 自動プロフィール同期を実行
        /// </summary>
        public async Task<ProfileSyncResultDto> AutoSyncProfileAsync(Guid userId)
        {
            try
            {
                var user = await _appUserRepository.GetAsync(userId);
                
                if (!user.IsGoogleLinked)
                {
                    return ProfileSyncResultDto.CreateFailure("Googleアカウントが連携されていません", "Auto");
                }

                // 自動同期の間隔チェック（24時間以内の場合はスキップ）
                if (user.LastGoogleSync.HasValue && 
                    DateTime.UtcNow - user.LastGoogleSync.Value < TimeSpan.FromHours(24))
                {
                    return ProfileSyncResultDto.CreateFailure("前回の同期から24時間経過していません", "Auto");
                }

                // 手動同期と同じ処理を実行
                var result = await SyncGoogleProfileAsync(userId);
                result.SyncType = "Auto";

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "自動プロフィール同期中にエラーが発生しました。UserId: {UserId}", userId);
                return ProfileSyncResultDto.CreateFailure($"自動同期中にエラーが発生しました: {ex.Message}", "Auto");
            }
        }

        /// <summary>
        /// プロフィール同期履歴を取得
        /// </summary>
        public async Task<ProfileSyncHistoryDto[]> GetProfileSyncHistoryAsync(Guid userId, int maxCount = 10)
        {
            try
            {
                // TODO: 実際の同期履歴テーブルから取得
                // 現在はモック実装
                var mockHistory = new List<ProfileSyncHistoryDto>();
                
                for (int i = 0; i < Math.Min(maxCount, 5); i++)
                {
                    mockHistory.Add(new ProfileSyncHistoryDto
                    {
                        Id = Guid.NewGuid(),
                        SyncTime = DateTime.UtcNow.AddDays(-i),
                        Provider = "Google",
                        SyncType = i == 0 ? "Manual" : "Auto",
                        Success = i != 2, // 3番目を失敗にする
                        UpdatedFields = i != 2 ? new List<string> { "Name", "ProfileImageUrl" } : new List<string>(),
                        ErrorMessage = i == 2 ? "API接続エラー" : null,
                        ProcessingTimeMs = 1500 + (i * 100)
                    });
                }

                return mockHistory.ToArray();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "プロフィール同期履歴取得中にエラーが発生しました。UserId: {UserId}", userId);
                return Array.Empty<ProfileSyncHistoryDto>();
            }
        }

        /// <summary>
        /// プロフィール同期設定を取得
        /// </summary>
        public async Task<ProfileSyncSettingsDto> GetProfileSyncSettingsAsync(Guid userId)
        {
            try
            {
                var user = await _appUserRepository.GetAsync(userId);
                
                // TODO: 実際の設定テーブルから取得
                // 現在はデフォルト設定を返す
                return new ProfileSyncSettingsDto
                {
                    AutoSyncEnabled = true,
                    SyncIntervalHours = 24,
                    SyncName = true,
                    SyncProfileImage = true,
                    SyncLocale = false,
                    LastAutoSyncTime = user.LastGoogleSync,
                    NextAutoSyncTime = user.LastGoogleSync?.AddHours(24)
                };
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "プロフィール同期設定取得中にエラーが発生しました。UserId: {UserId}", userId);
                
                // エラー時はデフォルト設定を返す
                return new ProfileSyncSettingsDto();
            }
        }

        /// <summary>
        /// プロフィール同期設定を更新
        /// </summary>
        public async Task<bool> UpdateProfileSyncSettingsAsync(Guid userId, ProfileSyncSettingsDto settings)
        {
            try
            {
                // TODO: 実際の設定テーブルに保存
                // 現在はログ出力のみ
                Logger.LogInformation("プロフィール同期設定が更新されました。UserId: {UserId}, AutoSync: {AutoSync}, Interval: {Interval}時間", 
                    userId, settings.AutoSyncEnabled, settings.SyncIntervalHours);
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "プロフィール同期設定更新中にエラーが発生しました。UserId: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// モックGoogleプロフィール情報を取得
        /// </summary>
        private GoogleUserInfoDto GetMockGoogleProfile()
        {
            return new GoogleUserInfoDto
            {
                Id = "mock_google_id",
                Email = "<EMAIL>",
                Name = "Updated User",
                GivenName = "Updated",
                FamilyName = "User",
                Picture = "https://example.com/updated-avatar.jpg",
                EmailVerified = true,
                Locale = "ja"
            };
        }
    }
}