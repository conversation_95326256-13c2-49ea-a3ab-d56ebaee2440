using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TranscriptCleanerMvc.Entities;
using TranscriptCleanerMvc.EntityFrameworkCore;
using TranscriptCleanerMvc.Repositories;
using TranscriptCleanerMvc.Transcripts;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TranscriptCleanerMvc.EntityFrameworkCore.Repositories;

public class TranscriptRepository : EfCoreRepository<TranscriptCleanerMvcDbContext, Transcript, Guid>, ITranscriptRepository
{
    public TranscriptRepository(IDbContextProvider<TranscriptCleanerMvcDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<Transcript>> GetListAsync(
        int skipCount = 0,
        int maxResultCount = 10,
        string? sorting = null,
        string? filter = null,
        TranscriptStatus? status = null,
        string? language = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.AsQueryable();

        query = ApplyFilter(query, filter, status, language, createdAfter, createdBefore);

        if (!string.IsNullOrEmpty(sorting))
        {
            query = query.OrderBy(sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
    }

    public async Task<long> GetCountAsync(
        string? filter = null,
        TranscriptStatus? status = null,
        string? language = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.AsQueryable();

        query = ApplyFilter(query, filter, status, language, createdAfter, createdBefore);

        return await query.CountAsync(cancellationToken);
    }

    public async Task<List<Transcript>> GetByUserIdAsync(
        Guid userId,
        int skipCount = 0,
        int maxResultCount = 10,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        
        return await dbSet
            .Where(x => x.CreatorId == userId)
            .OrderByDescending(x => x.CreationTime)
            .Skip(skipCount)
            .Take(maxResultCount)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Transcript>> GetRecentAsync(
        int count = 10,
        Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.AsQueryable();

        if (userId.HasValue)
        {
            query = query.Where(x => x.CreatorId == userId);
        }

        return await query
            .OrderByDescending(x => x.CreationTime)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    public async Task<Transcript?> GetWithHistoryAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var dbContext = await GetDbContextAsync();
        
        return await dbContext.Transcripts
            .Include(x => x.CorrectionHistories)
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    private IQueryable<Transcript> ApplyFilter(
        IQueryable<Transcript> query,
        string? filter = null,
        TranscriptStatus? status = null,
        string? language = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null)
    {
        if (!string.IsNullOrEmpty(filter))
        {
            query = query.Where(x => x.Title.Contains(filter) || x.OriginalText.Contains(filter));
        }

        if (status.HasValue)
        {
            query = query.Where(x => x.Status == status);
        }

        if (!string.IsNullOrEmpty(language))
        {
            query = query.Where(x => x.Language == language);
        }

        if (createdAfter.HasValue)
        {
            query = query.Where(x => x.CreationTime >= createdAfter);
        }

        if (createdBefore.HasValue)
        {
            query = query.Where(x => x.CreationTime <= createdBefore);
        }

        return query;
    }
}