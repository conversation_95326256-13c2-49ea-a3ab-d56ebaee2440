using System;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// 外部アカウント連携DTO
    /// </summary>
    public class ExternalAccountLinkDto
    {
        /// <summary>
        /// プロバイダー名
        /// </summary>
        public string Provider { get; set; } = string.Empty;

        /// <summary>
        /// プロバイダーキー
        /// </summary>
        public string ProviderKey { get; set; } = string.Empty;

        /// <summary>
        /// プロバイダー表示名
        /// </summary>
        public string ProviderDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 連携済みフラグ
        /// </summary>
        public bool IsLinked { get; set; }

        /// <summary>
        /// 連携日時
        /// </summary>
        public DateTime? LinkedAt { get; set; }

        /// <summary>
        /// 連携されたメールアドレス
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 最後の同期日時
        /// </summary>
        public DateTime? LastSyncTime { get; set; }

        /// <summary>
        /// 連携状態（Active, Inactive, Error等）
        /// </summary>
        public string Status { get; set; } = "Active";
    }
}