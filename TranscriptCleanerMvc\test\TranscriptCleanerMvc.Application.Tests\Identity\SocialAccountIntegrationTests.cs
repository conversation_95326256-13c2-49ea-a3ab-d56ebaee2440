using System;
using System.Threading.Tasks;
using Shouldly;
using Xunit;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Identity
{
    public class SocialAccountIntegrationTests : TranscriptCleanerMvcApplicationTestBase<TranscriptCleanerMvcApplicationTestModule>
    {
        private readonly IExternalLoginService _externalLoginService;

        public SocialAccountIntegrationTests()
        {
            _externalLoginService = GetRequiredService<IExternalLoginService>();
        }

        [Fact]
        public async Task HandleGoogleCallback_ValidCode_ShouldReturnResult()
        {
            // Arrange
            var code = "integration_test_code";
            var state = "integration_test_state";

            // Act
            var result = await _externalLoginService.HandleGoogleCallbackAsync(code, state);

            // Assert
            result.ShouldNotBeNull();
        }
    }
}