﻿using Volo.Abp.Ui.Branding;
using Volo.Abp.DependencyInjection;
using Microsoft.Extensions.Localization;
using TranscriptCleanerMvc.Localization;

namespace TranscriptCleanerMvc.Web;

[Dependency(ReplaceServices = true)]
public class TranscriptCleanerMvcBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<TranscriptCleanerMvcResource> _localizer;

    public TranscriptCleanerMvcBrandingProvider(IStringLocalizer<TranscriptCleanerMvcResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
    
    public override string LogoUrl => null; // ロゴを表示しない
    
    public override string LogoReverseUrl => null; // リバースロゴを表示しない
}
