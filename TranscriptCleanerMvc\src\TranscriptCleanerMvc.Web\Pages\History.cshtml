@page
@model TranscriptCleanerMvc.Web.Pages.HistoryPageModel
@using Microsoft.AspNetCore.Mvc.Localization
@using TranscriptCleanerMvc.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IHtmlLocalizer<TranscriptCleanerMvcResource> L
@inject IPageLayout PageLayout
@{
    ViewBag.PageTitle = "訂正履歴";
    PageLayout.Content.MenuItemName = "CorrectionHistory";
}

@functions {
    public string GetCorrectionTypeText(string correctionType)
    {
        return correctionType switch
        {
            "comprehensive" => "総合訂正",
            "grammar" => "文法訂正", 
            "formatting" => "フォーマット整理",
            _ => correctionType
        };
    }
}

@section styles {
    <link href="~/css/history.css" rel="stylesheet" />
}

<div class="history-container">
    <div class="content-header">
        <h2><i class="fas fa-history"></i> 訂正履歴</h2>
        <p>過去の訂正履歴を確認し、結果をダウンロードできます</p>
    </div>

    <div class="filter-section">
        <div class="row">
            <div class="col-md-3">
                <label for="dateFilter">期間</label>
                <select id="dateFilter" class="form-select">
                    <option value="all">すべて</option>
                    <option value="today">今日</option>
                    <option value="week">今週</option>
                    <option value="month">今月</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="statusFilter">ステータス</label>
                <select id="statusFilter" class="form-select">
                    <option value="all">すべて</option>
                    <option value="success">成功</option>
                    <option value="failed">失敗</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="searchText">検索</label>
                <input type="text" id="searchText" class="form-control" placeholder="キーワードで検索...">
            </div>
            <div class="col-md-2">
                <label>&nbsp;</label>
                <button type="button" class="btn btn-primary w-100" onclick="searchHistory()">
                    <i class="fas fa-search"></i> 検索
                </button>
            </div>
        </div>
    </div>

    <div class="history-table-container">
        <table class="table history-table">
            <thead>
                <tr>
                    <th>処理日時</th>
                    <th>ファイル名</th>
                    <th>処理モード</th>
                    <th>ステータス</th>
                    <th>処理時間</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="historyTableBody">
                @foreach (var transcript in Model.Transcripts)
                {
                    <tr>
                        <td>@transcript.CreationTime.ToString("yyyy-MM-dd HH:mm:ss")</td>
                        <td>@transcript.Title</td>
                        <td>@GetCorrectionTypeText(transcript.CorrectionType)</td>
                        <td>
                            @if (transcript.Status == "Completed")
                            {
                                <span class="status-badge success">@L["Success"]</span>
                            }
                            else if (transcript.Status == "Failed")
                            {
                                <span class="status-badge failed">@L["Failed"]</span>
                            }
                            else if (transcript.Status == "Processing")
                            {
                                <span class="status-badge processing">@L["Processing"]</span>
                            }
                            else
                            {
                                <span class="status-badge pending">@L["Pending"]</span>
                            }
                        </td>
                        <td>
                            @if (transcript.ProcessingTimeMs > 0)
                            {
                                @($"{transcript.ProcessingTimeMs / 1000.0:F1}秒")
                            }
                            else
                            {
                                <span>-</span>
                            }
                        </td>
                        <td>
                            @if (transcript.Status == "Completed")
                            {
                                <button class="btn btn-sm btn-outline-primary" onclick="viewResult('@transcript.Id')">
                                    <i class="fas fa-eye"></i> @L["View"]
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="downloadResult('@transcript.Id')">
                                    <i class="fas fa-download"></i> @L["Download"]
                                </button>
                            }
                            else if (transcript.Status == "Failed")
                            {
                                <button class="btn btn-sm btn-outline-danger" onclick="viewError('@transcript.Id')">
                                    <i class="fas fa-exclamation-triangle"></i> @L["Error"]
                                </button>
                            }
                            else if (transcript.Status == "Processing")
                            {
                                <button class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="fas fa-spinner fa-spin"></i> @L["Processing"]
                                </button>
                            }
                        </td>
                    </tr>
                }
                @if (!Model.Transcripts.Any())
                {
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            @L["NoTranscriptsFound"]
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    <div class="pagination-container">
        <nav>
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <span class="page-link">前へ</span>
                </li>
                <li class="page-item active">
                    <span class="page-link">1</span>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">2</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">3</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">次へ</a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<!-- 結果表示モーダル -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">訂正結果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="result-comparison">
                    <div class="row">
                        <div class="col-6">
                            <h6>元のテキスト</h6>
                            <div class="text-content" id="originalContent"></div>
                        </div>
                        <div class="col-6">
                            <h6>訂正後のテキスト</h6>
                            <div class="text-content" id="correctedContent"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="downloadFromModal()">
                    <i class="fas fa-download"></i> ダウンロード
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">閉じる</button>
            </div>
        </div>
    </div>
</div>

@section scripts {
    <script type="text/javascript">
        function searchHistory() {
            // 検索機能の実装
            console.log('検索実行');
        }
        
        async function viewResult(id) {
            try {
                const response = await fetch(`/api/transcript/${id}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch transcript data');
                }
                
                const data = await response.json();
                document.getElementById('originalContent').textContent = data.originalText || '';
                document.getElementById('correctedContent').textContent = data.correctedText || '';
                new bootstrap.Modal(document.getElementById('resultModal')).show();
            } catch (error) {
                console.error('Error fetching transcript:', error);
                alert('データの取得に失敗しました。');
            }
        }
        
        async function downloadResult(id) {
            try {
                const response = await fetch(`/api/transcript/${id}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch transcript data');
                }
                
                const data = await response.json();
                const correctedText = data.correctedText || 'No corrected text available';
                const blob = new Blob([correctedText], { type: 'text/plain; charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${data.title || 'corrected_transcript'}_${id}.txt`;
                a.click();
                URL.revokeObjectURL(url);
            } catch (error) {
                console.error('Error downloading transcript:', error);
                alert('ダウンロードに失敗しました。');
            }
        }
        
        async function viewError(id) {
            try {
                const response = await fetch(`/api/transcript/${id}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch transcript data');
                }
                
                const data = await response.json();
                const errorMessage = data.errorMessage || 'エラーの詳細情報が取得できませんでした。';
                alert(`エラー詳細: ${errorMessage}`);
            } catch (error) {
                console.error('Error fetching error details:', error);
                alert('エラー詳細の取得に失敗しました。');
            }
        }
        
        function downloadFromModal() {
            const correctedText = document.getElementById('correctedContent').textContent;
            const blob = new Blob([correctedText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `corrected_transcript_${new Date().toISOString().slice(0,10)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
}
