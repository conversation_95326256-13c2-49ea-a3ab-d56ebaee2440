/**
 * 管理者設定画面のJavaScript
 */

// ページ読み込み時の初期化
document.addEventListener('DOMContentLoaded', function() {
    // 初期データを読み込み
    loadGoogleAuthSettings();
    loadSystemSettings();
    loadStatistics();
    loadHistory();

    // フォームイベントを設定
    setupFormEvents();
});

/**
 * フォームイベントを設定
 */
function setupFormEvents() {
    // Google認証設定フォーム
    document.getElementById('googleAuthForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveGoogleAuthSettings();
    });

    // システム設定フォーム
    document.getElementById('systemSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSystemSettings();
    });
}

/**
 * Google認証設定を読み込み
 */
async function loadGoogleAuthSettings() {
    try {
        const response = await fetch('/api/admin/settings/google-auth');
        if (response.ok) {
            const settings = await response.json();
            
            document.getElementById('googleAuthEnabled').checked = settings.isEnabled;
            document.getElementById('googleClientId').value = settings.clientId || '';
            document.getElementById('googleClientSecret').placeholder = settings.clientSecretMasked ? 
                `現在の値: ${settings.clientSecretMasked}` : 'クライアントシークレットを入力';
            document.getElementById('googleRedirectUri').value = settings.redirectUri || '';
            document.getElementById('googleScopes').value = settings.scopes ? settings.scopes.join(',') : '';
            document.getElementById('googleAutoCreateAccount').checked = settings.autoCreateAccount;
            document.getElementById('googleDefaultRoles').value = settings.defaultRoles ? settings.defaultRoles.join(',') : '';
        } else {
            showError('Google認証設定の読み込みに失敗しました');
        }
    } catch (error) {
        console.error('Google認証設定読み込みエラー:', error);
        showError('Google認証設定の読み込み中にエラーが発生しました');
    }
}

/**
 * Google認証設定を保存
 */
async function saveGoogleAuthSettings() {
    try {
        const settings = {
            isEnabled: document.getElementById('googleAuthEnabled').checked,
            clientId: document.getElementById('googleClientId').value,
            clientSecret: document.getElementById('googleClientSecret').value,
            redirectUri: document.getElementById('googleRedirectUri').value,
            scopes: document.getElementById('googleScopes').value.split(',').map(s => s.trim()).filter(s => s),
            autoCreateAccount: document.getElementById('googleAutoCreateAccount').checked,
            defaultRoles: document.getElementById('googleDefaultRoles').value.split(',').map(s => s.trim()).filter(s => s)
        };

        const response = await fetch('/api/admin/settings/google-auth', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': getAntiForgeryToken()
            },
            body: JSON.stringify(settings)
        });

        if (response.ok) {
            showSuccess('Google認証設定が保存されました');
            loadGoogleAuthSettings(); // 設定を再読み込み
        } else {
            const error = await response.text();
            showError(`Google認証設定の保存に失敗しました: ${error}`);
        }
    } catch (error) {
        console.error('Google認証設定保存エラー:', error);
        showError('Google認証設定の保存中にエラーが発生しました');
    }
}

/**
 * システム設定を読み込み
 */
async function loadSystemSettings() {
    try {
        const response = await fetch('/api/admin/settings/system');
        if (response.ok) {
            const settings = await response.json();
            
            document.getElementById('sessionTimeout').value = settings.sessionTimeoutMinutes;
            document.getElementById('maxConcurrentSessions').value = settings.maxConcurrentSessions;
            document.getElementById('passwordPolicyEnabled').checked = settings.passwordPolicyEnabled;
            document.getElementById('requireTwoFactorAuth').checked = settings.requireTwoFactorAuth;
            document.getElementById('logRetentionDays').value = settings.logRetentionDays;
            document.getElementById('maintenanceMode').checked = settings.maintenanceMode;
            document.getElementById('maintenanceMessage').value = settings.maintenanceMessage || '';
        } else {
            showError('システム設定の読み込みに失敗しました');
        }
    } catch (error) {
        console.error('システム設定読み込みエラー:', error);
        showError('システム設定の読み込み中にエラーが発生しました');
    }
}

/**
 * システム設定を保存
 */
async function saveSystemSettings() {
    try {
        const settings = {
            sessionTimeoutMinutes: parseInt(document.getElementById('sessionTimeout').value),
            maxConcurrentSessions: parseInt(document.getElementById('maxConcurrentSessions').value),
            passwordPolicyEnabled: document.getElementById('passwordPolicyEnabled').checked,
            requireTwoFactorAuth: document.getElementById('requireTwoFactorAuth').checked,
            logRetentionDays: parseInt(document.getElementById('logRetentionDays').value),
            maintenanceMode: document.getElementById('maintenanceMode').checked,
            maintenanceMessage: document.getElementById('maintenanceMessage').value
        };

        const response = await fetch('/api/admin/settings/system', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': getAntiForgeryToken()
            },
            body: JSON.stringify(settings)
        });

        if (response.ok) {
            showSuccess('システム設定が保存されました');
            loadSystemSettings(); // 設定を再読み込み
        } else {
            const error = await response.text();
            showError(`システム設定の保存に失敗しました: ${error}`);
        }
    } catch (error) {
        console.error('システム設定保存エラー:', error);
        showError('システム設定の保存中にエラーが発生しました');
    }
}

/**
 * 統計情報を読み込み
 */
async function loadStatistics() {
    try {
        const response = await fetch('/api/admin/settings/statistics');
        if (response.ok) {
            const stats = await response.json();
            
            document.getElementById('totalUsers').textContent = stats.totalUsers.toLocaleString();
            document.getElementById('googleLinkedUsers').textContent = stats.googleLinkedUsers.toLocaleString();
            document.getElementById('todayLogins').textContent = stats.todayGoogleLogins.toLocaleString();
            document.getElementById('weeklyLogins').textContent = stats.weeklyGoogleLogins.toLocaleString();
            document.getElementById('monthlyLogins').textContent = stats.monthlyGoogleLogins.toLocaleString();
            document.getElementById('todayErrors').textContent = stats.todayAuthErrors.toLocaleString();
            document.getElementById('activeSessions').textContent = stats.activeSessions.toLocaleString();
            document.getElementById('lastUpdated').textContent = new Date(stats.lastUpdated).toLocaleString('ja-JP');
        } else {
            showError('統計情報の読み込みに失敗しました');
        }
    } catch (error) {
        console.error('統計情報読み込みエラー:', error);
        showError('統計情報の読み込み中にエラーが発生しました');
    }
}

/**
 * 変更履歴を読み込み
 */
async function loadHistory() {
    try {
        const response = await fetch('/api/admin/settings/history');
        if (response.ok) {
            const history = await response.json();
            const tbody = document.querySelector('#historyTable tbody');
            
            tbody.innerHTML = '';
            
            history.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${escapeHtml(item.settingName)}</td>
                    <td><code>${escapeHtml(item.oldValue || '-')}</code></td>
                    <td><code>${escapeHtml(item.newValue || '-')}</code></td>
                    <td>${escapeHtml(item.changedBy)}</td>
                    <td>${new Date(item.changedAt).toLocaleString('ja-JP')}</td>
                    <td>${escapeHtml(item.reason || '-')}</td>
                    <td>${escapeHtml(item.ipAddress || '-')}</td>
                `;
                tbody.appendChild(row);
            });
        } else {
            showError('変更履歴の読み込みに失敗しました');
        }
    } catch (error) {
        console.error('変更履歴読み込みエラー:', error);
        showError('変更履歴の読み込み中にエラーが発生しました');
    }
}

/**
 * 成功メッセージを表示
 */
function showSuccess(message) {
    document.getElementById('successMessage').textContent = message;
    const toast = new bootstrap.Toast(document.getElementById('successToast'));
    toast.show();
}

/**
 * エラーメッセージを表示
 */
function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    const toast = new bootstrap.Toast(document.getElementById('errorToast'));
    toast.show();
}

/**
 * アンチフォージェリートークンを取得
 */
function getAntiForgeryToken() {
    return document.querySelector('input[name="__RequestVerificationToken"]')?.value || '';
}

/**
 * HTMLエスケープ
 */
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}