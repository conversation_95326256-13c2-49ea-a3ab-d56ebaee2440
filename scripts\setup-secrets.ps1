# TranscriptCleaner OpenAI API Key Setup Script
# Usage: ./setup-secrets.ps1 [API_KEY]

param(
    [Parameter(Mandatory=$false)]
    [string]$ApiKey
)

# Color output function
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

Write-ColorOutput Green "=== TranscriptCleaner OpenAI API Key Setup ==="
Write-Host ""

# Project root directory
$ProjectRoot = Split-Path $PSScriptRoot -Parent
$SecretsPath = Join-Path $ProjectRoot "TranscriptCleanerMvc\src\TranscriptCleanerMvc.Web\appsettings.secrets.json"
$TemplatePath = "$SecretsPath.template"

# If API key not provided, ask for secure input
if (-not $ApiKey) {
    Write-ColorOutput Yellow "Please enter your OpenAI API key (input will be hidden):"
    $SecureKey = Read-Host -AsSecureString
    $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecureKey)
    $ApiKey = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
    [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)
}

# API key validation
if (-not $ApiKey -or $ApiKey.Length -lt 20) {
    Write-ColorOutput Red "Error: Please enter a valid OpenAI API key"
    exit 1
}

if (-not $ApiKey.StartsWith("sk-")) {
    Write-ColorOutput Red "Error: OpenAI API key must start with 'sk-'"
    exit 1
}

# Check template file
if (-not (Test-Path $TemplatePath)) {
    Write-ColorOutput Red "Error: Template file not found: $TemplatePath"
    exit 1
}

# Generate secrets file
try {
    $TemplateContent = Get-Content $TemplatePath -Raw
    $SecretsContent = $TemplateContent -replace "sk-your-openai-api-key-here", $ApiKey
    
    # Create directory if it doesn't exist
    $SecretsDir = Split-Path $SecretsPath -Parent
    if (-not (Test-Path $SecretsDir)) {
        New-Item -ItemType Directory -Path $SecretsDir -Force | Out-Null
    }
    
    # Write to secrets file
    $SecretsContent | Out-File -FilePath $SecretsPath -Encoding UTF8 -Force
    
    Write-ColorOutput Green "OpenAI API key configured successfully"
    Write-ColorOutput Gray "   Configuration file: $SecretsPath"
    Write-Host ""
    
    # Security warning
    Write-ColorOutput Yellow "Security Notice:"
    Write-ColorOutput Gray "   • appsettings.secrets.json is already in .gitignore"
    Write-ColorOutput Gray "   • Do NOT push this file to GitHub"
    Write-ColorOutput Gray "   • Use environment variables in production"
    Write-Host ""
    
    # Next steps
    Write-ColorOutput Cyan "Next steps:"
    Write-ColorOutput Gray "   1. Start backend: dotnet run (TranscriptCleanerMvc.Web project)"
    Write-ColorOutput Gray "   2. Start MAUI or WinUI application"
    Write-Host ""
    
} catch {
    Write-ColorOutput Red "Error: Failed to create secrets file"
    Write-ColorOutput Red "   $($_.Exception.Message)"
    exit 1
}

# Clear API key variable (security)
$ApiKey = $null
[System.GC]::Collect()