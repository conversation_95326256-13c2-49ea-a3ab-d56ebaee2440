using System;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Storage;

namespace TranscriptCleaner.Maui.Services
{
    /// <summary>
    /// JWTトークン管理サービス実装
    /// </summary>
    public class JwtTokenService : IJwtTokenService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<JwtTokenService> _logger;
        private readonly string _baseUrl;

        // セキュアストレージキー
        private const string JWT_TOKEN_KEY = "abp_jwt_token";
        private const string JWT_REFRESH_TOKEN_KEY = "abp_jwt_refresh_token";
        private const string JWT_TOKEN_INFO_KEY = "abp_jwt_token_info";

        public JwtTokenService(HttpClient httpClient, ILogger<JwtTokenService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            
            // 設定値（実際の実装では設定ファイルから読み込み）
            _baseUrl = "https://localhost:44396"; // ABP Framework API URL
        }

        /// <summary>
        /// ABP Framework JWTトークンを取得
        /// </summary>
        public async Task<JwtTokenInfo?> GetAbpJwtTokenAsync(string googleAccessToken)
        {
            try
            {
                _logger.LogInformation("ABP Framework JWTトークンを取得します");

                var request = new
                {
                    Provider = "Google",
                    AccessToken = googleAccessToken
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/external/token", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var tokenResponse = JsonSerializer.Deserialize<AbpTokenResponse>(responseContent);

                    if (tokenResponse != null)
                    {
                        var tokenInfo = new JwtTokenInfo
                        {
                            AccessToken = tokenResponse.AccessToken,
                            RefreshToken = tokenResponse.RefreshToken,
                            TokenType = tokenResponse.TokenType ?? "Bearer",
                            ExpiresIn = tokenResponse.ExpiresIn,
                            IssuedAt = DateTime.UtcNow,
                            Scope = tokenResponse.Scope
                        };

                        // JWTトークンからユーザー情報を抽出
                        ExtractUserInfoFromJwt(tokenInfo);

                        await SaveJwtTokenAsync(tokenInfo);
                        
                        _logger.LogInformation("ABP Framework JWTトークンを取得しました。UserId: {UserId}", tokenInfo.UserId);
                        return tokenInfo;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("ABP Framework JWTトークン取得に失敗しました。Status: {Status}, Response: {Response}", 
                        response.StatusCode, errorContent);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ABP Framework JWTトークン取得中にエラーが発生しました");
                return null;
            }
        }

        /// <summary>
        /// 現在のJWTトークンを取得
        /// </summary>
        public async Task<string?> GetCurrentJwtTokenAsync()
        {
            try
            {
                var tokenInfo = await GetStoredTokenInfoAsync();
                if (tokenInfo != null && tokenInfo.IsValid)
                {
                    return tokenInfo.AccessToken;
                }

                // トークンが期限切れの場合は自動更新を試行
                if (tokenInfo != null && !tokenInfo.IsValid)
                {
                    var refreshed = await RefreshJwtTokenAsync();
                    if (refreshed)
                    {
                        var newTokenInfo = await GetStoredTokenInfoAsync();
                        return newTokenInfo?.AccessToken;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "現在のJWTトークン取得中にエラーが発生しました");
                return null;
            }
        }

        /// <summary>
        /// JWTトークンを保存
        /// </summary>
        public async Task SaveJwtTokenAsync(JwtTokenInfo tokenInfo)
        {
            try
            {
                await SecureStorage.SetAsync(JWT_TOKEN_KEY, tokenInfo.AccessToken);
                
                if (!string.IsNullOrEmpty(tokenInfo.RefreshToken))
                {
                    await SecureStorage.SetAsync(JWT_REFRESH_TOKEN_KEY, tokenInfo.RefreshToken);
                }

                var json = JsonSerializer.Serialize(tokenInfo);
                await SecureStorage.SetAsync(JWT_TOKEN_INFO_KEY, json);

                _logger.LogInformation("JWTトークンを保存しました。ExpiresAt: {ExpiresAt}", tokenInfo.ExpiresAt);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "JWTトークン保存中にエラーが発生しました");
            }
        }

        /// <summary>
        /// JWTトークンの有効性を検証
        /// </summary>
        public async Task<bool> ValidateJwtTokenAsync()
        {
            try
            {
                var tokenInfo = await GetStoredTokenInfoAsync();
                if (tokenInfo == null || !tokenInfo.IsValid)
                {
                    return false;
                }

                // サーバーでトークンの有効性を検証
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new AuthenticationHeaderValue("Bearer", tokenInfo.AccessToken);

                var response = await _httpClient.GetAsync($"{_baseUrl}/api/auth/validate");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "JWTトークン検証中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// JWTトークンを自動更新
        /// </summary>
        public async Task<bool> RefreshJwtTokenAsync()
        {
            try
            {
                var refreshToken = await SecureStorage.GetAsync(JWT_REFRESH_TOKEN_KEY);
                if (string.IsNullOrEmpty(refreshToken))
                {
                    _logger.LogWarning("リフレッシュトークンが見つかりません");
                    return false;
                }

                var newTokenInfo = await RefreshTokenWithRefreshTokenAsync(refreshToken);
                if (newTokenInfo != null)
                {
                    await SaveJwtTokenAsync(newTokenInfo);
                    _logger.LogInformation("JWTトークンの自動更新が成功しました");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "JWTトークン自動更新中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// リフレッシュトークンでJWTトークンを更新
        /// </summary>
        public async Task<JwtTokenInfo?> RefreshTokenWithRefreshTokenAsync(string refreshToken)
        {
            try
            {
                _logger.LogInformation("リフレッシュトークンでJWTトークンを更新します");

                var request = new
                {
                    RefreshToken = refreshToken
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/refresh", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var tokenResponse = JsonSerializer.Deserialize<AbpTokenResponse>(responseContent);

                    if (tokenResponse != null)
                    {
                        var tokenInfo = new JwtTokenInfo
                        {
                            AccessToken = tokenResponse.AccessToken,
                            RefreshToken = tokenResponse.RefreshToken ?? refreshToken, // 新しいリフレッシュトークンがない場合は既存のものを使用
                            TokenType = tokenResponse.TokenType ?? "Bearer",
                            ExpiresIn = tokenResponse.ExpiresIn,
                            IssuedAt = DateTime.UtcNow,
                            Scope = tokenResponse.Scope
                        };

                        // JWTトークンからユーザー情報を抽出
                        ExtractUserInfoFromJwt(tokenInfo);

                        _logger.LogInformation("JWTトークンの更新が成功しました。UserId: {UserId}", tokenInfo.UserId);
                        return tokenInfo;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("JWTトークン更新に失敗しました。Status: {Status}, Response: {Response}", 
                        response.StatusCode, errorContent);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "JWTトークン更新中にエラーが発生しました");
                return null;
            }
        }

        /// <summary>
        /// JWTトークンをクリア
        /// </summary>
        public Task ClearJwtTokenAsync()
        {
            try
            {
                SecureStorage.Remove(JWT_TOKEN_KEY);
                SecureStorage.Remove(JWT_REFRESH_TOKEN_KEY);
                SecureStorage.Remove(JWT_TOKEN_INFO_KEY);
                
                _logger.LogInformation("JWTトークンをクリアしました");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "JWTトークンクリア中にエラーが発生しました");
            }
            
            return Task.CompletedTask;
        }

        /// <summary>
        /// トークンの有効期限をチェック
        /// </summary>
        public async Task<bool> IsTokenExpiredAsync()
        {
            try
            {
                var tokenInfo = await GetStoredTokenInfoAsync();
                return tokenInfo == null || !tokenInfo.IsValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "トークン有効期限チェック中にエラーが発生しました");
                return true; // エラーの場合は期限切れとして扱う
            }
        }

        /// <summary>
        /// トークンの残り有効時間を取得
        /// </summary>
        public async Task<TimeSpan?> GetTokenRemainingTimeAsync()
        {
            try
            {
                var tokenInfo = await GetStoredTokenInfoAsync();
                if (tokenInfo != null && tokenInfo.IsValid)
                {
                    return tokenInfo.RemainingTime;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "トークン残り時間取得中にエラーが発生しました");
                return null;
            }
        }

        /// <summary>
        /// 保存されたトークン情報を取得
        /// </summary>
        private async Task<JwtTokenInfo?> GetStoredTokenInfoAsync()
        {
            try
            {
                var json = await SecureStorage.GetAsync(JWT_TOKEN_INFO_KEY);
                if (string.IsNullOrEmpty(json))
                {
                    return null;
                }

                return JsonSerializer.Deserialize<JwtTokenInfo>(json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存されたトークン情報取得中にエラーが発生しました");
                return null;
            }
        }

        /// <summary>
        /// JWTトークンからユーザー情報を抽出
        /// </summary>
        private void ExtractUserInfoFromJwt(JwtTokenInfo tokenInfo)
        {
            try
            {
                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(tokenInfo.AccessToken);

                tokenInfo.UserId = jsonToken.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
                tokenInfo.UserName = jsonToken.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Name)?.Value;
                tokenInfo.Email = jsonToken.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Email)?.Value;
                
                var roleClaims = jsonToken.Claims.Where(x => x.Type == ClaimTypes.Role).Select(x => x.Value).ToArray();
                if (roleClaims.Length > 0)
                {
                    tokenInfo.Roles = roleClaims;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "JWTトークンからのユーザー情報抽出中にエラーが発生しました");
            }
        }
    }

    /// <summary>
    /// ABPトークンレスポンス
    /// </summary>
    public class AbpTokenResponse
    {
        public string AccessToken { get; set; } = string.Empty;
        public string? RefreshToken { get; set; }
        public string? TokenType { get; set; }
        public int ExpiresIn { get; set; }
        public string? Scope { get; set; }
    }
}