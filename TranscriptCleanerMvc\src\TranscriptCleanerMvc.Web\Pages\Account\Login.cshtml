@page
@model TranscriptCleanerMvc.Web.Pages.Account.LoginPageModel
@using Microsoft.AspNetCore.Mvc.Localization
@using TranscriptCleanerMvc.Localization
@using System.Globalization
@inject IHtmlLocalizer<TranscriptCleanerMvcResource> L

<!DOCTYPE html>
<html lang="@CultureInfo.CurrentCulture.TwoLetterISOLanguageName">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
    <title>@L["Login"] - @L["AppName"]</title>
    <link href="~/css/login.css" rel="stylesheet" />
</head>
<body>
    <div class="login-wrapper">
        <!-- Left side - Branding -->
        <div class="login-branding">
            <div class="login-branding-content">
                <h1>@L["AppName"]</h1>
                <p>@L["AppDescription"]</p>
            </div>
        </div>
        
        <!-- Right side - Login Form -->
        <div class="login-container">
            <div class="login-form">
                <h2 class="app-title">@L["Login"]</h2>
                
                <div class="language-selector">
                    <span class="globe-icon"></span>
                    <select id="languageSelect" onchange="changeLanguage()" class="language-select">
                        <option value="ja">日本語</option>
                        <option value="en">English</option>
                    </select>
                </div>
                
                @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                {
                    <div class="error-message">
                        <span class="error-icon">⚠</span>
                        <span>@Model.ErrorMessage</span>
                    </div>
                }
                
                <form method="post">
                    <div class="form-group">
                        <label for="Username">@L["UserNameOrEmailAddress"]</label>
                        <input type="text" id="Username" name="Input.UserNameOrEmailAddress" 
                               value="@Model.Input.UserNameOrEmailAddress" required autocomplete="username" 
                               placeholder="@L["PleaseEnterUserName"]" />
                    </div>
                    
                    <div class="form-group">
                        <label for="Password">@L["Password"]</label>
                        <input type="password" id="Password" name="Input.Password" 
                               required autocomplete="current-password" 
                               placeholder="@L["PleaseEnterPassword"]" />
                    </div>
                    
                    <div class="form-group checkbox">
                        <input type="checkbox" id="RememberMe" name="Input.RememberMe" 
                               @(Model.Input.RememberMe ? "checked" : "") />
                        <label for="RememberMe">@L["RememberMe"]</label>
                    </div>
                    
                    <button type="submit" class="login-button">@L["Login"]</button>
                </form>
                
                <!-- Social Login Section -->
                <div class="social-login-section">
                    <div class="divider">
                        <span>@L["OrLoginWith"]</span>
                    </div>
                    
                    <div class="social-login-buttons">
                        <a href="/api/auth/external/google/login?returnUrl=@Html.Raw(Html.Encode(Model.ReturnUrl ?? "/"))" 
                           class="social-login-button google-login">
                            <svg class="google-icon" viewBox="0 0 24 24" width="20" height="20">
                                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                            </svg>
                            <span>@L["LoginWithGoogle"]</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 現在の言語を設定
        document.addEventListener('DOMContentLoaded', function() {
            var currentLang = '@CultureInfo.CurrentCulture.TwoLetterISOLanguageName';
            var selectElement = document.getElementById('languageSelect');
            if (selectElement) {
                selectElement.value = currentLang;
            }
        });
        
        function changeLanguage() {
            var selectedLang = document.getElementById('languageSelect').value;
            document.cookie = 'Abp.Localization.CultureName=' + selectedLang + '; path=/; expires=' + new Date(Date.now() + 365*24*60*60*1000).toUTCString();
            window.location.reload();
        }
    </script>
</body>
</html>
