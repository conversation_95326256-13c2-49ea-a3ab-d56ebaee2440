﻿using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging; // ILoggerの明示的なインポートを追加
using TranscriptCleanerMvc.Data;
using Serilog;
using Volo.Abp;
using Volo.Abp.Data;

namespace TranscriptCleanerMvc.DbMigrator;

public class DbMigratorHostedService : IHostedService
{
    private readonly IHostApplicationLifetime _hostApplicationLifetime;
    private readonly IConfiguration _configuration;
    private readonly ILogger<DbMigratorHostedService> _logger; // 正しいジェネリック型

    public DbMigratorHostedService(
        IHostApplicationLifetime hostApplicationLifetime, 
        IConfiguration configuration,
        ILogger<DbMigratorHostedService> logger)
    {
        _hostApplicationLifetime = hostApplicationLifetime;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        using (var application = await AbpApplicationFactory.CreateAsync<TranscriptCleanerMvcDbMigratorModule>(options =>
        {
           // 設定の置き換えを削除（デフォルトの設定読み込みを使用）
           options.UseAutofac();
           options.Services.AddLogging(c => c.AddSerilog());
           options.AddDataMigrationEnvironment();
        }))
        {
            await application.InitializeAsync();

            // 設定をログ出力して確認
            var config = application.ServiceProvider.GetRequiredService<IConfiguration>();
            _logger.LogInformation("ConnectionString: {0}", config.GetConnectionString("Default"));
            
            await application
                .ServiceProvider
                .GetRequiredService<TranscriptCleanerMvcDbMigrationService>()
                .MigrateAsync();

            await application.ShutdownAsync();

            _hostApplicationLifetime.StopApplication();
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}
