using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// 監査ログサービスインターフェース
    /// </summary>
    public interface IAuditLogService : IApplicationService
    {
        /// <summary>
        /// 外部認証イベントをログに記録
        /// </summary>
        /// <param name="auditEvent">監査イベント</param>
        /// <returns>記録処理</returns>
        Task LogExternalAuthEventAsync(ExternalAuthAuditEvent auditEvent);

        /// <summary>
        /// セキュリティ監査イベントをログに記録
        /// </summary>
        /// <param name="securityEvent">セキュリティイベント</param>
        /// <returns>記録処理</returns>
        Task LogSecurityEventAsync(SecurityAuditEvent securityEvent);

        /// <summary>
        /// 管理者向けアラートを送信
        /// </summary>
        /// <param name="alert">アラート情報</param>
        /// <returns>送信結果</returns>
        Task<bool> SendAdminAlertAsync(AdminAlert alert);

        /// <summary>
        /// 監査ログを検索
        /// </summary>
        /// <param name="input">検索条件</param>
        /// <returns>監査ログ一覧</returns>
        Task<PagedResultDto<AuditLogDto>> SearchAuditLogsAsync(SearchAuditLogsInput input);

        /// <summary>
        /// セキュリティ監査レポートを生成
        /// </summary>
        /// <param name="input">レポート条件</param>
        /// <returns>監査レポート</returns>
        Task<SecurityAuditReport> GenerateSecurityAuditReportAsync(GenerateAuditReportInput input);

        /// <summary>
        /// 異常なアクティビティを検出
        /// </summary>
        /// <param name="input">検出条件</param>
        /// <returns>異常アクティビティ一覧</returns>
        Task<SuspiciousActivity[]> DetectSuspiciousActivitiesAsync(DetectSuspiciousActivitiesInput input);

        /// <summary>
        /// 監査ログをエクスポート
        /// </summary>
        /// <param name="input">エクスポート条件</param>
        /// <returns>エクスポートファイル</returns>
        Task<ExportResult> ExportAuditLogsAsync(ExportAuditLogsInput input);

        /// <summary>
        /// 監査ログの保持期間管理
        /// </summary>
        /// <param name="retentionDays">保持日数</param>
        /// <returns>削除されたログ数</returns>
        Task<int> ManageLogRetentionAsync(int retentionDays);
    }

    /// <summary>
    /// 外部認証監査イベント
    /// </summary>
    public class ExternalAuthAuditEvent
    {
        /// <summary>
        /// イベントタイプ
        /// </summary>
        public ExternalAuthEventType EventType { get; set; }

        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// プロバイダー名
        /// </summary>
        public string Provider { get; set; } = string.Empty;

        /// <summary>
        /// プロバイダーキー
        /// </summary>
        public string? ProviderKey { get; set; }

        /// <summary>
        /// 成功フラグ
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// ユーザーエージェント
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// セッションID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// リクエストID
        /// </summary>
        public string? RequestId { get; set; }

        /// <summary>
        /// 追加データ
        /// </summary>
        public Dictionary<string, object>? AdditionalData { get; set; }

        /// <summary>
        /// 発生日時
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// セキュリティ監査イベント
    /// </summary>
    public class SecurityAuditEvent
    {
        /// <summary>
        /// イベントタイプ
        /// </summary>
        public SecurityEventType EventType { get; set; }

        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// リソース
        /// </summary>
        public string? Resource { get; set; }

        /// <summary>
        /// アクション
        /// </summary>
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// 結果
        /// </summary>
        public string Result { get; set; } = string.Empty;

        /// <summary>
        /// 重要度
        /// </summary>
        public SecuritySeverity Severity { get; set; }

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// ユーザーエージェント
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// 詳細情報
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// 発生日時
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 管理者アラート
    /// </summary>
    public class AdminAlert
    {
        /// <summary>
        /// アラートタイプ
        /// </summary>
        public AlertType AlertType { get; set; }

        /// <summary>
        /// タイトル
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// メッセージ
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 重要度
        /// </summary>
        public AlertSeverity Severity { get; set; }

        /// <summary>
        /// 関連ユーザーID
        /// </summary>
        public Guid? RelatedUserId { get; set; }

        /// <summary>
        /// 関連IPアドレス
        /// </summary>
        public string? RelatedIpAddress { get; set; }

        /// <summary>
        /// 詳細データ
        /// </summary>
        public Dictionary<string, object>? Details { get; set; }

        /// <summary>
        /// 発生日時
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 監査ログ検索条件
    /// </summary>
    public class SearchAuditLogsInput : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// 開始日時
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 終了日時
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// イベントタイプ
        /// </summary>
        public string? EventType { get; set; }

        /// <summary>
        /// プロバイダー
        /// </summary>
        public string? Provider { get; set; }

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// 成功フィルター
        /// </summary>
        public bool? IsSuccess { get; set; }

        /// <summary>
        /// 重要度フィルター
        /// </summary>
        public SecuritySeverity? Severity { get; set; }

        /// <summary>
        /// 検索キーワード
        /// </summary>
        public string? SearchKeyword { get; set; }
    }

    /// <summary>
    /// 監査ログDTO
    /// </summary>
    public class AuditLogDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// イベントタイプ
        /// </summary>
        public string EventType { get; set; } = string.Empty;

        /// <summary>
        /// ユーザーID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// ユーザー名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// プロバイダー
        /// </summary>
        public string? Provider { get; set; }

        /// <summary>
        /// アクション
        /// </summary>
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// 結果
        /// </summary>
        public string Result { get; set; } = string.Empty;

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// ユーザーエージェント
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// 詳細情報
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// 重要度
        /// </summary>
        public SecuritySeverity Severity { get; set; }

        /// <summary>
        /// 発生日時
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 監査レポート生成条件
    /// </summary>
    public class GenerateAuditReportInput
    {
        /// <summary>
        /// 開始日時
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 終了日時
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// レポートタイプ
        /// </summary>
        public AuditReportType ReportType { get; set; }

        /// <summary>
        /// 含めるイベントタイプ
        /// </summary>
        public string[]? IncludeEventTypes { get; set; }

        /// <summary>
        /// 最小重要度
        /// </summary>
        public SecuritySeverity? MinimumSeverity { get; set; }

        /// <summary>
        /// 詳細レベル
        /// </summary>
        public ReportDetailLevel DetailLevel { get; set; }
    }

    /// <summary>
    /// セキュリティ監査レポート
    /// </summary>
    public class SecurityAuditReport
    {
        /// <summary>
        /// レポートID
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// レポートタイプ
        /// </summary>
        public AuditReportType ReportType { get; set; }

        /// <summary>
        /// 期間
        /// </summary>
        public DateRange Period { get; set; } = new();

        /// <summary>
        /// サマリー
        /// </summary>
        public AuditSummary Summary { get; set; } = new();

        /// <summary>
        /// イベント統計
        /// </summary>
        public EventStatistics[] EventStats { get; set; } = Array.Empty<EventStatistics>();

        /// <summary>
        /// セキュリティインシデント
        /// </summary>
        public SecurityIncident[] SecurityIncidents { get; set; } = Array.Empty<SecurityIncident>();

        /// <summary>
        /// 推奨事項
        /// </summary>
        public string[] Recommendations { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 生成日時
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 生成者
        /// </summary>
        public string GeneratedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// 異常アクティビティ検出条件
    /// </summary>
    public class DetectSuspiciousActivitiesInput
    {
        /// <summary>
        /// 検出期間（時間）
        /// </summary>
        public int DetectionWindowHours { get; set; } = 24;

        /// <summary>
        /// 最小重要度
        /// </summary>
        public SecuritySeverity MinimumSeverity { get; set; } = SecuritySeverity.Medium;

        /// <summary>
        /// 検出ルール
        /// </summary>
        public SuspiciousActivityRule[] Rules { get; set; } = Array.Empty<SuspiciousActivityRule>();
    }

    /// <summary>
    /// 異常アクティビティ
    /// </summary>
    public class SuspiciousActivity
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// アクティビティタイプ
        /// </summary>
        public SuspiciousActivityType ActivityType { get; set; }

        /// <summary>
        /// 説明
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 重要度
        /// </summary>
        public SecuritySeverity Severity { get; set; }

        /// <summary>
        /// 関連ユーザーID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 関連IPアドレス
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// 検出時刻
        /// </summary>
        public DateTime DetectedAt { get; set; }

        /// <summary>
        /// 関連イベント数
        /// </summary>
        public int RelatedEventCount { get; set; }

        /// <summary>
        /// 詳細データ
        /// </summary>
        public Dictionary<string, object>? Details { get; set; }

        /// <summary>
        /// 処理状況
        /// </summary>
        public ActivityStatus Status { get; set; }
    }

    /// <summary>
    /// エクスポート条件
    /// </summary>
    public class ExportAuditLogsInput
    {
        /// <summary>
        /// 開始日時
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 終了日時
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// エクスポート形式
        /// </summary>
        public ExportFormat Format { get; set; }

        /// <summary>
        /// フィルター条件
        /// </summary>
        public SearchAuditLogsInput? Filter { get; set; }

        /// <summary>
        /// 含める列
        /// </summary>
        public string[]? IncludeColumns { get; set; }
    }

    /// <summary>
    /// エクスポート結果
    /// </summary>
    public class ExportResult
    {
        /// <summary>
        /// ファイル名
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// ファイルサイズ
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// ファイルデータ
        /// </summary>
        public byte[] FileData { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// コンテンツタイプ
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// エクスポート日時
        /// </summary>
        public DateTime ExportedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// レコード数
        /// </summary>
        public int RecordCount { get; set; }
    }

    // 列挙型定義
    public enum ExternalAuthEventType
    {
        LoginAttempt,
        LoginSuccess,
        LoginFailure,
        AccountLink,
        AccountUnlink,
        TokenRefresh,
        TokenExpired
    }



    public enum SecuritySeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    public enum AlertType
    {
        SecurityBreach,
        SuspiciousActivity,
        SystemError,
        ConfigurationChange,
        PerformanceIssue
    }

    public enum AlertSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    public enum AuditReportType
    {
        Summary,
        Detailed,
        SecurityFocused,
        ComplianceReport
    }

    public enum ReportDetailLevel
    {
        Basic,
        Standard,
        Detailed,
        Comprehensive
    }

    public enum SuspiciousActivityType
    {
        MultipleFailedLogins,
        UnusualLoginTime,
        UnusualLocation,
        PrivilegeEscalation,
        DataExfiltration,
        BruteForceAttack
    }

    public enum ActivityStatus
    {
        New,
        InvestigationRequired,
        Investigating,
        Resolved,
        FalsePositive
    }

    public enum ExportFormat
    {
        Csv,
        Excel,
        Json,
        Xml,
        Pdf
    }

    // 補助クラス
    public class DateRange
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class AuditSummary
    {
        public int TotalEvents { get; set; }
        public int SuccessfulEvents { get; set; }
        public int FailedEvents { get; set; }
        public int SecurityIncidents { get; set; }
        public int UniqueUsers { get; set; }
        public int UniqueIpAddresses { get; set; }
    }

    public class EventStatistics
    {
        public string EventType { get; set; } = string.Empty;
        public int Count { get; set; }
        public double Percentage { get; set; }
        public DateTime LastOccurred { get; set; }
    }

    public class SecurityIncident
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public SecuritySeverity Severity { get; set; }
        public DateTime OccurredAt { get; set; }
        public string? AffectedUser { get; set; }
        public string? SourceIp { get; set; }
    }

    public class SuspiciousActivityRule
    {
        public string RuleName { get; set; } = string.Empty;
        public SuspiciousActivityType ActivityType { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
        public bool IsEnabled { get; set; } = true;
    }
}