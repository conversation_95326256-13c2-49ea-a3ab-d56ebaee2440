using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// 外部ログイン履歴エンティティ
    /// Google認証などの外部認証プロバイダーでのログイン履歴を記録
    /// </summary>
    public class ExternalLoginLog : CreationAuditedEntity<Guid>
    {
        /// <summary>
        /// ユーザーID
        /// </summary>
        public virtual Guid UserId { get; protected set; }

        /// <summary>
        /// 認証プロバイダー名 (例: "Google", "Microsoft", "GitHub")
        /// </summary>
        public virtual string Provider { get; protected set; }

        /// <summary>
        /// プロバイダー固有のユーザーキー
        /// </summary>
        public virtual string ProviderKey { get; protected set; }

        /// <summary>
        /// ログイン日時
        /// </summary>
        public virtual DateTime LoginTime { get; protected set; }

        /// <summary>
        /// IPアドレス
        /// </summary>
        public virtual string? IpAddress { get; set; }

        /// <summary>
        /// ユーザーエージェント
        /// </summary>
        public virtual string? UserAgent { get; set; }

        /// <summary>
        /// ログイン成功フラグ
        /// </summary>
        public virtual bool Success { get; protected set; }

        /// <summary>
        /// エラーメッセージ（失敗時）
        /// </summary>
        public virtual string? ErrorMessage { get; set; }

        /// <summary>
        /// セッションID
        /// </summary>
        public virtual string? SessionId { get; set; }

        /// <summary>
        /// 使用されたスコープ
        /// </summary>
        public virtual string? Scopes { get; set; }

        /// <summary>
        /// 追加のメタデータ（JSON形式）
        /// </summary>
        public virtual string? Metadata { get; set; }

        protected ExternalLoginLog()
        {
        }

        public ExternalLoginLog(
            Guid id,
            Guid userId,
            string provider,
            string providerKey,
            bool success,
            string ipAddress = null,
            string userAgent = null,
            string errorMessage = null)
            : base(id)
        {
            UserId = userId;
            Provider = provider;
            ProviderKey = providerKey;
            Success = success;
            LoginTime = DateTime.UtcNow;
            IpAddress = ipAddress;
            UserAgent = userAgent;
            ErrorMessage = errorMessage;
        }

        /// <summary>
        /// 成功ログを作成
        /// </summary>
        public static ExternalLoginLog CreateSuccessLog(
            Guid userId,
            string provider,
            string providerKey,
            string ipAddress = null,
            string userAgent = null,
            string sessionId = null,
            string scopes = null)
        {
            var log = new ExternalLoginLog(
                Guid.NewGuid(),
                userId,
                provider,
                providerKey,
                true,
                ipAddress,
                userAgent);

            log.SessionId = sessionId;
            log.Scopes = scopes;

            return log;
        }

        /// <summary>
        /// 失敗ログを作成
        /// </summary>
        public static ExternalLoginLog CreateFailureLog(
            Guid userId,
            string provider,
            string providerKey,
            string errorMessage,
            string ipAddress = null,
            string userAgent = null)
        {
            return new ExternalLoginLog(
                Guid.NewGuid(),
                userId,
                provider,
                providerKey,
                false,
                ipAddress,
                userAgent,
                errorMessage);
        }

        /// <summary>
        /// メタデータを設定
        /// </summary>
        public void SetMetadata(string metadata)
        {
            Metadata = metadata;
        }
    }
}