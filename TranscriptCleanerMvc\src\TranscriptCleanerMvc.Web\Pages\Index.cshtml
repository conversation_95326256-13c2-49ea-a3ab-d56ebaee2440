﻿@page
@model TranscriptCleanerMvc.Web.Pages.IndexModel
@using TranscriptCleanerMvc.Web.Menus
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using Microsoft.AspNetCore.Mvc.Localization
@using TranscriptCleanerMvc.Localization
@using Volo.Abp.Users
@inject IPageLayout PageLayout
@inject IHtmlLocalizer<TranscriptCleanerMvcResource> L
@inject ICurrentUser CurrentUser
@{
    ViewBag.PageTitle = "トランスクリプトクリーナー";
    PageLayout.Content.MenuItemName = TranscriptCleanerMvcMenus.Home;
}

@section styles {
    <abp-style src="/Pages/Index.css" />
}

<div class="container-fluid">
    @if (!CurrentUser.IsAuthenticated)
    {
        <div class="main-header text-center">
            <h1 class="app-title">トランスクリプトクリーナー</h1>
            <p class="app-subtitle">Microsoft Teams議事録訂正システム</p>
            <a href="/Account/Login" class="btn btn-primary btn-lg mt-3">
                <i class="fa-solid fa-right-to-bracket me-2"></i>ログイン
            </a>
        </div>
    }
    else
    {
        <div class="main-header">
            <h1 class="app-title">トランスクリプトクリーナー</h1>
            <p class="app-subtitle">Microsoft Teams議事録を自動で訂正します</p>
        </div>
        
        <div class="feature-cards">
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="card-icon">📝</div>
                        <h3>テキスト訂正</h3>
                        <p>AI技術を使用して議事録の誤字脱字や文法を自動訂正</p>
                        <a href="/TranscriptCorrection" class="btn btn-primary">開始する</a>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="card-icon">📊</div>
                        <h3>訂正履歴</h3>
                        <p>過去の訂正履歴を確認し、結果をダウンロード</p>
                        <a href="/History" class="btn btn-outline-primary">履歴を見る</a>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="card-icon">⚙️</div>
                        <h3>設定</h3>
                        <p>訂正レベルや言語設定をカスタマイズ</p>
                        <a href="/Settings" class="btn btn-outline-primary">設定する</a>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .main-header {
        text-align: center;
        padding: 60px 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        margin: -20px -15px 40px -15px;
        border-radius: 0 0 20px 20px;
    }
    
    .app-title {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 15px;
    }
    
    .app-subtitle {
        font-size: 1.3rem;
        opacity: 0.9;
        margin-bottom: 0;
    }
    
    .feature-cards {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 15px;
    }
    
    .feature-card {
        background: white;
        border-radius: 12px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .card-icon {
        font-size: 3rem;
        margin-bottom: 20px;
    }
    
    .feature-card h3 {
        color: #333;
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .feature-card p {
        color: #666;
        margin-bottom: 25px;
        line-height: 1.6;
    }
    
    .btn {
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
    }
    
    .btn-primary {
        background: #4472c4;
        color: white;
    }
    
    .btn-primary:hover {
        background: #3c5aa6;
        transform: translateY(-2px);
    }
    
    .btn-outline-primary {
        border: 2px solid #4472c4;
        color: #4472c4;
        background: transparent;
    }
    
    .btn-outline-primary:hover {
        background: #4472c4;
        color: white;
        transform: translateY(-2px);
    }
    
    .btn-lg {
        padding: 15px 40px;
        font-size: 1.1rem;
    }
</style>