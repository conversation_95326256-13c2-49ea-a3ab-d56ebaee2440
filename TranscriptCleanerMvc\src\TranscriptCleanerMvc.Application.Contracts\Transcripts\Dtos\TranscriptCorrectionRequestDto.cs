using System.ComponentModel.DataAnnotations;

namespace TranscriptCleanerMvc.Transcripts.Dtos;

public class TranscriptCorrectionRequestDto
{
    [Required]
    public string Text { get; set; } = string.Empty;

    public string? Title { get; set; }

    public string Language { get; set; } = "ja";

    public string CorrectionType { get; set; } = "comprehensive";

    public string? CustomPrompt { get; set; }

    public string? WordList { get; set; }
}