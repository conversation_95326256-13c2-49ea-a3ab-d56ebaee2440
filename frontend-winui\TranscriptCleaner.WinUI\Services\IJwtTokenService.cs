using System;
using System.Threading.Tasks;

namespace TranscriptCleaner.WinUI.Services
{
    /// <summary>
    /// JWTトークン管理サービスインターフェース
    /// </summary>
    public interface IJwtTokenService
    {
        /// <summary>
        /// ABP Framework JWTトークンを取得
        /// </summary>
        /// <param name="googleAccessToken">Googleアクセストークン</param>
        /// <returns>JWTトークン情報</returns>
        Task<JwtTokenInfo?> GetAbpJwtTokenAsync(string googleAccessToken);

        /// <summary>
        /// 現在のJWTトークンを取得
        /// </summary>
        /// <returns>JWTトークン</returns>
        Task<string?> GetCurrentJwtTokenAsync();

        /// <summary>
        /// JWTトークンを保存
        /// </summary>
        /// <param name="tokenInfo">トークン情報</param>
        /// <returns>保存処理</returns>
        Task SaveJwtTokenAsync(JwtTokenInfo tokenInfo);

        /// <summary>
        /// JWTトークンの有効性を検証
        /// </summary>
        /// <returns>有効性</returns>
        Task<bool> ValidateJwtTokenAsync();

        /// <summary>
        /// JWTトークンを自動更新
        /// </summary>
        /// <returns>更新成功フラグ</returns>
        Task<bool> RefreshJwtTokenAsync();

        /// <summary>
        /// リフレッシュトークンでJWTトークンを更新
        /// </summary>
        /// <param name="refreshToken">リフレッシュトークン</param>
        /// <returns>更新されたトークン情報</returns>
        Task<JwtTokenInfo?> RefreshTokenWithRefreshTokenAsync(string refreshToken);

        /// <summary>
        /// JWTトークンをクリア
        /// </summary>
        /// <returns>クリア処理</returns>
        Task ClearJwtTokenAsync();

        /// <summary>
        /// トークンの有効期限をチェック
        /// </summary>
        /// <returns>期限切れフラグ</returns>
        Task<bool> IsTokenExpiredAsync();

        /// <summary>
        /// トークンの残り有効時間を取得
        /// </summary>
        /// <returns>残り時間</returns>
        Task<TimeSpan?> GetTokenRemainingTimeAsync();
    }

    /// <summary>
    /// JWTトークン情報
    /// </summary>
    public class JwtTokenInfo
    {
        /// <summary>
        /// アクセストークン
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// リフレッシュトークン
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// トークンタイプ
        /// </summary>
        public string TokenType { get; set; } = "Bearer";

        /// <summary>
        /// 有効期限（秒）
        /// </summary>
        public int ExpiresIn { get; set; }

        /// <summary>
        /// 発行日時
        /// </summary>
        public DateTime IssuedAt { get; set; }

        /// <summary>
        /// 有効期限日時
        /// </summary>
        public DateTime ExpiresAt => IssuedAt.AddSeconds(ExpiresIn);

        /// <summary>
        /// スコープ
        /// </summary>
        public string? Scope { get; set; }

        /// <summary>
        /// ユーザーID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// ユーザー名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// メールアドレス
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// ロール
        /// </summary>
        public string[]? Roles { get; set; }

        /// <summary>
        /// トークンが有効かチェック
        /// </summary>
        public bool IsValid => DateTime.UtcNow < ExpiresAt;

        /// <summary>
        /// トークンがもうすぐ期限切れかチェック（5分以内）
        /// </summary>
        public bool IsNearExpiry => DateTime.UtcNow.AddMinutes(5) >= ExpiresAt;

        /// <summary>
        /// 残り有効時間を取得
        /// </summary>
        public TimeSpan RemainingTime => ExpiresAt - DateTime.UtcNow;
    }

    /// <summary>
    /// トークンリフレッシュ結果
    /// </summary>
    public class TokenRefreshResult
    {
        /// <summary>
        /// 成功フラグ
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 新しいトークン情報
        /// </summary>
        public JwtTokenInfo? TokenInfo { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// エラーコード
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 成功結果を作成
        /// </summary>
        public static TokenRefreshResult Success(JwtTokenInfo tokenInfo)
        {
            return new TokenRefreshResult
            {
                IsSuccess = true,
                TokenInfo = tokenInfo
            };
        }

        /// <summary>
        /// 失敗結果を作成
        /// </summary>
        public static TokenRefreshResult Failure(string errorMessage, string? errorCode = null)
        {
            return new TokenRefreshResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode
            };
        }
    }
}