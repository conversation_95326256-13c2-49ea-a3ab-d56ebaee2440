.settings-container {
    padding: 20px;
    max-width: 1000px;
    margin: 0 auto;
}

.content-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    text-align: center;
}

.content-header h2 {
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
}

.content-header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.settings-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.settings-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-section h3 {
    color: #495057;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.settings-section h3 i {
    margin-right: 10px;
    color: #6c757d;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-control, .form-select {
    border: 1px solid #ced4da;
    border-radius: 8px;
    padding: 10px 12px;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus, .form-select:focus {
    border-color: #4472c4;
    box-shadow: 0 0 0 2px rgba(68, 114, 196, 0.2);
    outline: none;
}

.form-check {
    margin-bottom: 15px;
}

.form-check-input {
    margin-top: 0.25rem;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
    margin-left: 5px;
}

.input-group .btn {
    border-left: 0;
}

.settings-actions {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.settings-actions .btn {
    margin: 0 10px;
    padding: 12px 25px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-success {
    background: #28a745;
    border: none;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    border: none;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-info {
    background: #17a2b8;
    border: none;
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-1px);
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

@media (max-width: 768px) {
    .settings-container {
        padding: 10px;
    }
    
    .settings-content {
        padding: 20px;
    }
    
    .settings-actions .btn {
        margin: 5px;
        width: 100%;
    }
    
    .row > div {
        margin-bottom: 15px;
    }
}