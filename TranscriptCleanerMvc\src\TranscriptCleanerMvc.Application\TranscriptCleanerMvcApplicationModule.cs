﻿using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.Account;
using Volo.Abp.Identity;
using Volo.Abp.AutoMapper;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Modularity;
using Volo.Abp.TenantManagement;

namespace TranscriptCleanerMvc;

[DependsOn(
    typeof(TranscriptCleanerMvcDomainModule),
    typeof(TranscriptCleanerMvcApplicationContractsModule),
    typeof(AbpPermissionManagementApplicationModule),
    typeof(AbpFeatureManagementApplicationModule),
    typeof(AbpIdentityApplicationModule),
    typeof(AbpAccountApplicationModule),
    typeof(AbpTenantManagementApplicationModule),
    typeof(AbpSettingManagementApplicationModule)
    )]
public class TranscriptCleanerMvcApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<TranscriptCleanerMvcApplicationModule>();
        });

        var services = context.Services;
        
        // Identity関連サービスの登録
        services.AddTransient<TranscriptCleanerMvc.Identity.IExternalLoginService, TranscriptCleanerMvc.Identity.ExternalLoginService>();
        services.AddTransient<TranscriptCleanerMvc.Identity.IAccountLinkService, TranscriptCleanerMvc.Identity.AccountLinkService>();
        services.AddTransient<TranscriptCleanerMvc.Identity.IProfileSyncService, TranscriptCleanerMvc.Identity.ProfileSyncService>();
        services.AddTransient<TranscriptCleanerMvc.Identity.ISecurityService, TranscriptCleanerMvc.Identity.SecurityService>();
        // services.AddTransient<TranscriptCleanerMvc.Identity.IErrorHandlingService, TranscriptCleanerMvc.Identity.ErrorHandlingService>();
        services.AddTransient<TranscriptCleanerMvc.Identity.IAuditLogService, TranscriptCleanerMvc.Identity.AuditLogService>();
        services.AddTransient<TranscriptCleanerMvc.Identity.IAdminSettingsService, TranscriptCleanerMvc.Identity.AdminSettingsService>();
        services.AddTransient<TranscriptCleanerMvc.Identity.IUserManagementService, TranscriptCleanerMvc.Identity.UserManagementService>();
    }
}
