using System.Threading.Tasks;
using Volo.Abp.UI.Navigation;

namespace TranscriptCleanerMvc.Web.Menus;

public class TranscriptCleanerMvcMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }
    }

    private static Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        var menu = context.Menu;

        // トランスクリプト訂正メニューを追加
        menu.AddItem(new ApplicationMenuItem(
            "TranscriptCorrection",
            "トランスクリプト訂正",
            "/TranscriptCorrection",
            icon: "fa fa-edit",
            order: 100
        ));

        // 認証済みユーザー向けメニュー（認証チェックを削除して常に表示）
        // アカウント管理メニューを追加
        menu.AddItem(new ApplicationMenuItem(
            "AccountManagement",
            "アカウント管理",
            "/Account/Manage/ExternalLogins",
            icon: "fa fa-user-cog",
            order: 200
        ));

        return Task.CompletedTask;
    }
}
