using System;
using Volo.Abp.Application.Dtos;

namespace TranscriptCleanerMvc.WordLists.Dtos;

public class WordListDto : FullAuditedEntityDto<Guid>
{
    public string IncorrectWord { get; set; } = string.Empty;
    public string CorrectWord { get; set; } = string.Empty;
    public string Language { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public int UsageCount { get; set; }
    public DateTime? LastUsedAt { get; set; }
}

public class CreateWordListDto
{
    public string IncorrectWord { get; set; } = string.Empty;
    public string CorrectWord { get; set; } = string.Empty;
    public string Language { get; set; } = "ja";
    public string Category { get; set; } = "general";
    public string? Description { get; set; }
}

public class UpdateWordListDto
{
    public string CorrectWord { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}

public class WordListFilterDto : PagedAndSortedResultRequestDto
{
    public string? Filter { get; set; }
    public string? Language { get; set; }
    public string? Category { get; set; }
    public bool? IsActive { get; set; }
}

public class ImportWordListDto
{
    public string CsvContent { get; set; } = string.Empty;
    public string Language { get; set; } = "ja";
    public string Category { get; set; } = "general";
    public bool OverwriteExisting { get; set; } = false;
}