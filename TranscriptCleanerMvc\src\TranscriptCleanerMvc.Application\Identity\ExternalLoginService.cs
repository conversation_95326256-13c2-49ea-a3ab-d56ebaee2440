using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Users;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// 外部ログインサービス実装
    /// </summary>
    public class ExternalLoginService : ApplicationService, IExternalLoginService
    {
        private readonly IRepository<AppUser, Guid> _appUserRepository;
        private readonly IRepository<ExternalLoginLog, Guid> _externalLoginLogRepository;
        private readonly IIdentityUserRepository _identityUserRepository;
        private readonly ICurrentUser _currentUser;

        public ExternalLoginService(
            IRepository<AppUser, Guid> appUserRepository,
            IRepository<ExternalLoginLog, Guid> externalLoginLogRepository,
            IIdentityUserRepository identityUserRepository,
            ICurrentUser currentUser)
        {
            _appUserRepository = appUserRepository;
            _externalLoginLogRepository = externalLoginLogRepository;
            _identityUserRepository = identityUserRepository;
            _currentUser = currentUser;
        }

        /// <summary>
        /// Google認証コールバックを処理
        /// </summary>
        public async Task<ExternalLoginResultDto> HandleGoogleCallbackAsync(string code, string state)
        {
            try
            {
                // TODO: Google APIを使用してユーザー情報を取得
                // 現在はモック実装
                var googleUserInfo = new GoogleUserInfoDto
                {
                    Id = "mock_google_id",
                    Email = "<EMAIL>",
                    Name = "Test User",
                    GivenName = "Test",
                    FamilyName = "User",
                    Picture = "https://example.com/avatar.jpg",
                    EmailVerified = true
                };

                // 既存ユーザーを検索
                var existingUser = await _appUserRepository.FirstOrDefaultAsync(u => u.GoogleId == googleUserInfo.Id);
                bool isNewUser = false;

                if (existingUser == null)
                {
                    // 新規ユーザーを作成
                    existingUser = new AppUser(
                        Guid.NewGuid(),
                        googleUserInfo.Email,
                        googleUserInfo.Email
                    );
                    
                    existingUser.LinkGoogleAccount(
                        googleUserInfo.Id,
                        googleUserInfo.Email,
                        googleUserInfo.Picture
                    );

                    await _appUserRepository.InsertAsync(existingUser);
                    isNewUser = true;
                }
                else
                {
                    // 既存ユーザーのプロフィールを更新
                    existingUser.UpdateGoogleProfile(
                        googleUserInfo.GivenName,
                        googleUserInfo.FamilyName,
                        googleUserInfo.Picture
                    );

                    await _appUserRepository.UpdateAsync(existingUser);
                }

                // 成功ログを記録
                var successLog = ExternalLoginLog.CreateSuccessLog(
                    existingUser.Id,
                    "Google",
                    googleUserInfo.Id,
                    GetClientIpAddress(),
                    GetUserAgent()
                );

                await _externalLoginLogRepository.InsertAsync(successLog);

                return ExternalLoginResultDto.CreateSuccess(
                    "mock_access_token",
                    googleUserInfo,
                    isNewUser
                );
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Google認証コールバック処理中にエラーが発生しました");

                // 失敗ログを記録
                var failureLog = ExternalLoginLog.CreateFailureLog(
                    Guid.Empty,
                    "Google",
                    "unknown",
                    ex.Message,
                    GetClientIpAddress(),
                    GetUserAgent()
                );

                await _externalLoginLogRepository.InsertAsync(failureLog);

                return ExternalLoginResultDto.CreateFailure($"認証処理中にエラーが発生しました: {ex.Message}");
            }
        }

        /// <summary>
        /// Googleアカウントを既存ユーザーに連携
        /// </summary>
        public async Task<bool> LinkGoogleAccountAsync(Guid userId, string googleId)
        {
            try
            {
                var user = await _appUserRepository.GetAsync(userId);
                
                if (user.IsGoogleLinked)
                {
                    return false; // 既に連携済み
                }

                // 他のユーザーが同じGoogleアカウントを使用していないかチェック
                var existingGoogleUser = await _appUserRepository.FirstOrDefaultAsync(u => u.GoogleId == googleId);
                if (existingGoogleUser != null)
                {
                    return false; // 既に他のユーザーが使用中
                }

                // TODO: Google APIからユーザー情報を取得
                user.LinkGoogleAccount(googleId, "<EMAIL>", "https://example.com/avatar.jpg");
                
                await _appUserRepository.UpdateAsync(user);
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Googleアカウント連携中にエラーが発生しました。UserId: {UserId}, GoogleId: {GoogleId}", userId, googleId);
                return false;
            }
        }

        /// <summary>
        /// Googleアカウント連携を解除
        /// </summary>
        public async Task<bool> UnlinkGoogleAccountAsync(Guid userId)
        {
            try
            {
                var user = await _appUserRepository.GetAsync(userId);
                
                if (!user.IsGoogleLinked)
                {
                    return false; // 連携されていない
                }

                user.UnlinkGoogleAccount();
                await _appUserRepository.UpdateAsync(user);
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Googleアカウント連携解除中にエラーが発生しました。UserId: {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// Googleプロフィール情報を同期
        /// </summary>
        public async Task<GoogleProfileDto> SyncGoogleProfileAsync(Guid userId)
        {
            var user = await _appUserRepository.GetAsync(userId);
            
            if (!user.IsGoogleLinked)
            {
                throw new InvalidOperationException("Googleアカウントが連携されていません");
            }

            // TODO: Google APIから最新のプロフィール情報を取得
            user.UpdateGoogleProfile("Updated Name", "Updated Surname", "https://example.com/new-avatar.jpg");
            await _appUserRepository.UpdateAsync(user);

            return new GoogleProfileDto
            {
                GoogleId = user.GoogleId!,
                GoogleEmail = user.GoogleEmail!,
                Name = user.Name ?? string.Empty,
                Surname = user.Surname ?? string.Empty,
                ProfileImageUrl = user.ProfileImageUrl,
                LastSyncTime = user.LastGoogleSync,
                IsLinked = user.IsGoogleLinked
            };
        }

        /// <summary>
        /// ユーザーの外部アカウント連携状況を取得
        /// </summary>
        public async Task<ExternalAccountLinkStatusDto> GetExternalAccountLinkStatusAsync(Guid userId)
        {
            var user = await _appUserRepository.GetAsync(userId);

            var result = new ExternalAccountLinkStatusDto
            {
                UserId = userId,
                LinkedAccountsCount = 0
            };

            if (user.IsGoogleLinked)
            {
                result.Google = new GoogleLinkInfoDto
                {
                    IsLinked = true,
                    Email = user.GoogleEmail,
                    LastSyncTime = user.LastGoogleSync
                };
                result.LinkedAccountsCount++;
            }

            return result;
        }

        private string? GetClientIpAddress()
        {
            // TODO: HttpContextAccessorを使用せずにIPアドレスを取得する方法を実装
            return "unknown";
        }

        private string? GetUserAgent()
        {
            // TODO: HttpContextAccessorを使用せずにUser-Agentを取得する方法を実装
            return "unknown";
        }
    }
}