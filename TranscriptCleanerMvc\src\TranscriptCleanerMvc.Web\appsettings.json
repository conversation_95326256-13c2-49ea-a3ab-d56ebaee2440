{"App": {"SelfUrl": "https://localhost:44396", "HealthCheckUrl": "http://localhost:44396/health-status", "HealthUiCheckUrl": "http://localhost:44396/health-status", "CorsOrigins": "https://localhost:3000,http://localhost:3000,https://localhost:5173,http://localhost:5173"}, "ConnectionStrings": {"Default": "Server=(LocalDb)\\MSSQLLocalDB;Database=TranscriptCleanerMvc;Trusted_Connection=True;TrustServerCertificate=true"}, "AuthServer": {"Authority": "https://localhost:44396", "RequireHttpsMetadata": true, "CertificatePassPhrase": "3f72aed6-7b1b-4e55-8e53-83a38a78040c"}, "StringEncryption": {"DefaultPassPhrase": "LHOOmlkwPOq0U2ku"}, "OpenAI": {"_comment": "Set OPENAI_API_KEY environment variable for API access", "InputCostPerToken": 0.00015, "OutputCostPerToken": 0.0006}, "Authentication": {"Google": {"ClientId": "666982533390-ocdqjrmqmlbdg0a7f1ai47q4tjt66ksc.apps.googleusercontent.com", "ClientSecret": "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", "Enabled": true, "Scopes": ["openid", "profile", "email"], "CallbackPath": "/signin-google", "SaveTokens": true}}, "ExternalAuth": {"TokenCacheExpiry": "01:00:00", "ProfileSyncInterval": "24:00:00", "MaxRetryAttempts": 3}}