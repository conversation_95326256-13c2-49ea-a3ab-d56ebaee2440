using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TranscriptCleanerMvc.Entities;
using Volo.Abp.Domain.Repositories;

namespace TranscriptCleanerMvc.Repositories;

public interface IWordListRepository : IRepository<WordList, Guid>
{
    Task<List<WordList>> GetListAsync(
        int skipCount = 0,
        int maxResultCount = 10,
        string? sorting = null,
        string? filter = null,
        string? language = null,
        string? category = null,
        bool? isActive = null,
        CancellationToken cancellationToken = default
    );
    
    Task<long> GetCountAsync(
        string? filter = null,
        string? language = null,
        string? category = null,
        bool? isActive = null,
        CancellationToken cancellationToken = default
    );
    
    Task<List<WordList>> GetActiveByLanguageAsync(
        string language,
        CancellationToken cancellationToken = default
    );
    
    Task<WordList?> FindByIncorrectWordAsync(
        string incorrectWord,
        string language,
        CancellationToken cancellationToken = default
    );
    
    Task<bool> IsIncorrectWordExistsAsync(
        string incorrectWord,
        string language,
        Guid? excludeId = null,
        CancellationToken cancellationToken = default
    );
    
    Task<List<WordList>> GetMostUsedAsync(
        int count = 10,
        string? language = null,
        CancellationToken cancellationToken = default
    );
    
    Task<List<string>> GetCategoriesAsync(
        string? language = null,
        CancellationToken cancellationToken = default
    );
}