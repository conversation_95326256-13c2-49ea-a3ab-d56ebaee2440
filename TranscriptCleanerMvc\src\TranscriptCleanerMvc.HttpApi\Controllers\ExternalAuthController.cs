using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using TranscriptCleanerMvc.Identity;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Users;

namespace TranscriptCleanerMvc.Controllers
{
    /// <summary>
    /// 外部認証コントローラー
    /// Google認証などの外部認証プロバイダーとの連携を処理
    /// </summary>
    [ApiController]
    [Route("api/auth/external")]
    public class ExternalAuthController : AbpControllerBase
    {
        private readonly IExternalLoginService _externalLoginService;

        public ExternalAuthController(IExternalLoginService externalLoginService)
        {
            _externalLoginService = externalLoginService;
        }

        /// <summary>
        /// Google認証を開始
        /// </summary>
        /// <param name="returnUrl">認証後のリダイレクト先URL</param>
        /// <returns>Google認証ページへのリダイレクト</returns>
        [HttpGet("google/login")]
        public IActionResult GoogleLogin(string? returnUrl = null)
        {
            try
            {
                var redirectUrl = Url.Action(nameof(GoogleCallback), "ExternalAuth", new { returnUrl });
                var properties = new AuthenticationProperties
                {
                    RedirectUri = redirectUrl,
                    Items = { { "returnUrl", returnUrl ?? "/" } }
                };

                return Challenge(properties, "Google");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Google認証開始中にエラーが発生しました");
                return BadRequest(new { error = "認証開始に失敗しました", details = ex.Message });
            }
        }

        /// <summary>
        /// Google認証コールバック
        /// </summary>
        /// <param name="returnUrl">認証後のリダイレクト先URL</param>
        /// <param name="remoteError">リモートエラー</param>
        /// <returns>認証結果</returns>
        [HttpGet("google/callback")]
        public async Task<IActionResult> GoogleCallback(string? returnUrl = null, string? remoteError = null)
        {
            try
            {
                if (!string.IsNullOrEmpty(remoteError))
                {
                    Logger.LogWarning("Google認証でリモートエラーが発生しました: {Error}", remoteError);
                    return BadRequest(new { error = "認証が拒否されました", details = remoteError });
                }

                var authenticateResult = await HttpContext.AuthenticateAsync("Google");
                if (!authenticateResult.Succeeded)
                {
                    Logger.LogWarning("Google認証に失敗しました");
                    return BadRequest(new { error = "認証に失敗しました" });
                }

                // 認証情報からコードと状態を取得
                var code = authenticateResult.Properties?.Items.TryGetValue("code", out var codeValue) == true ? codeValue : "mock_code";
                var state = authenticateResult.Properties?.Items.TryGetValue("state", out var stateValue) == true ? stateValue : "mock_state";

                // 外部ログインサービスで処理
                var result = await _externalLoginService.HandleGoogleCallbackAsync(code, state);

                if (result.Success)
                {
                    Logger.LogInformation("Google認証が成功しました。ユーザー: {Email}, 新規: {IsNew}", 
                        result.User?.Email, result.IsNewUser);

                    // 成功時のレスポンス
                    return Ok(new
                    {
                        success = true,
                        isNewUser = result.IsNewUser,
                        user = result.User,
                        redirectUrl = returnUrl ?? "/"
                    });
                }
                else
                {
                    Logger.LogError("Google認証処理に失敗しました: {Error}", result.ErrorMessage);
                    return BadRequest(new { error = result.ErrorMessage });
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Google認証コールバック処理中にエラーが発生しました");
                return StatusCode(500, new { error = "内部サーバーエラーが発生しました", details = ex.Message });
            }
        }

        /// <summary>
        /// Googleアカウントを現在のユーザーに連携
        /// </summary>
        /// <returns>連携結果</returns>
        [HttpPost("google/link")]
        [Authorize]
        public async Task<IActionResult> LinkGoogleAccount()
        {
            try
            {
                if (!CurrentUser.IsAuthenticated)
                {
                    return Unauthorized(new { error = "認証が必要です" });
                }

                // TODO: 実際のGoogle認証フローを実装
                var googleId = "mock_google_id"; // 実際の実装では認証フローから取得
                var userId = CurrentUser.Id ?? Guid.Empty;

                var success = await _externalLoginService.LinkGoogleAccountAsync(userId, googleId);

                if (success)
                {
                    Logger.LogInformation("Googleアカウント連携が成功しました。UserId: {UserId}", userId);
                    return Ok(new { success = true, message = "Googleアカウントが連携されました" });
                }
                else
                {
                    Logger.LogWarning("Googleアカウント連携に失敗しました。UserId: {UserId}", userId);
                    return BadRequest(new { error = "アカウント連携に失敗しました" });
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Googleアカウント連携中にエラーが発生しました");
                return StatusCode(500, new { error = "内部サーバーエラーが発生しました", details = ex.Message });
            }
        }

        /// <summary>
        /// Googleアカウント連携を解除
        /// </summary>
        /// <returns>解除結果</returns>
        [HttpDelete("google/unlink")]
        [Authorize]
        public async Task<IActionResult> UnlinkGoogleAccount()
        {
            try
            {
                if (!CurrentUser.IsAuthenticated)
                {
                    return Unauthorized(new { error = "認証が必要です" });
                }

                var userId = CurrentUser.Id ?? Guid.Empty;
                var success = await _externalLoginService.UnlinkGoogleAccountAsync(userId);

                if (success)
                {
                    Logger.LogInformation("Googleアカウント連携解除が成功しました。UserId: {UserId}", userId);
                    return Ok(new { success = true, message = "Googleアカウント連携が解除されました" });
                }
                else
                {
                    Logger.LogWarning("Googleアカウント連携解除に失敗しました。UserId: {UserId}", userId);
                    return BadRequest(new { error = "連携解除に失敗しました" });
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Googleアカウント連携解除中にエラーが発生しました");
                return StatusCode(500, new { error = "内部サーバーエラーが発生しました", details = ex.Message });
            }
        }

        /// <summary>
        /// 外部アカウント連携状況を取得
        /// </summary>
        /// <returns>連携状況</returns>
        [HttpGet("status")]
        [Authorize]
        public async Task<IActionResult> GetExternalAccountStatus()
        {
            try
            {
                if (!CurrentUser.IsAuthenticated)
                {
                    return Unauthorized(new { error = "認証が必要です" });
                }

                var userId = CurrentUser.Id ?? Guid.Empty;
                var status = await _externalLoginService.GetExternalAccountLinkStatusAsync(userId);

                return Ok(status);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "外部アカウント状況取得中にエラーが発生しました");
                return StatusCode(500, new { error = "内部サーバーエラーが発生しました", details = ex.Message });
            }
        }

        /// <summary>
        /// Googleプロフィール情報を同期
        /// </summary>
        /// <returns>同期結果</returns>
        [HttpPost("google/sync")]
        [Authorize]
        public async Task<IActionResult> SyncGoogleProfile()
        {
            try
            {
                if (!CurrentUser.IsAuthenticated)
                {
                    return Unauthorized(new { error = "認証が必要です" });
                }

                var userId = CurrentUser.Id ?? Guid.Empty;
                var profile = await _externalLoginService.SyncGoogleProfileAsync(userId);

                Logger.LogInformation("Googleプロフィール同期が成功しました。UserId: {UserId}", userId);
                return Ok(new { success = true, profile });
            }
            catch (InvalidOperationException ex)
            {
                Logger.LogWarning("Googleプロフィール同期に失敗しました: {Error}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Googleプロフィール同期中にエラーが発生しました");
                return StatusCode(500, new { error = "内部サーバーエラーが発生しました", details = ex.Message });
            }
        }
    }
}