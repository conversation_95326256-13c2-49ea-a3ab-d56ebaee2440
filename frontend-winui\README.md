# TranscriptCleaner WinUI プロジェクト

Microsoft Teams議事録の自動訂正を行うWinUIアプリケーションです。MAUIプロジェクトと同等の機能を提供します。

## 🚀 セットアップ手順

### 1. OpenAI APIキーの設定

```powershell
# プロジェクトルートディレクトリで実行
.\scripts\setup-secrets.ps1
```

プロンプトに従ってOpenAI APIキーを入力してください。入力内容は表示されません。

### 2. 環境の切り替え

```powershell
# 開発環境に切り替え
.\scripts\switch-environment.ps1 development

# ステージング環境に切り替え
.\scripts\switch-environment.ps1 staging

# 本番環境に切り替え
.\scripts\switch-environment.ps1 production
```

### 3. バックエンドの起動

```powershell

dotnet run
```

### 4. WinUIアプリの起動

```powershell
cd frontend-winui\TranscriptCleaner.WinUI
dotnet run
```

## 📁 プロジェクト構造

```
frontend-winui/TranscriptCleaner.WinUI/
├── Services/
│   └── TranscriptCorrectionService.cs    # API通信サービス
├── ViewModels/
│   ├── LoginPageViewModel.cs             # ログイン画面ViewModel
│   └── TranscriptPageViewModel.cs        # 議事録訂正画面ViewModel
├── Views/
│   ├── LoginPage.xaml                    # ログイン画面
│   └── TranscriptPage.xaml               # 議事録訂正画面（作成予定）
├── App.xaml                              # アプリケーション設定
├── MainWindow.xaml                       # メインウィンドウ
└── TranscriptCleaner.WinUI.csproj        # プロジェクトファイル
```

## 🔧 機能

### 実装済み
- ログイン機能（デモアカウント：admin/admin123, testuser/test123, demo/demo123）
- NavigationView による画面遷移
- バックエンドAPI通信サービス
- モック処理による基本テキスト訂正

### 実装予定
- 議事録訂正画面（TranscriptPage）
- ファイル選択機能
- AI訂正実行
- 差分表示機能
- 結果保存機能

## 🔐 セキュリティ

- OpenAI APIキーは `appsettings.secrets.json` で管理
- シークレットファイルは `.gitignore` に追加済み
- 本番環境では環境変数を使用推奨

## 📝 開発メモ

- WinUI 3.0 + .NET 9.0 を使用
- CommunityToolkit.Mvvm によるMVVMパターン
- MAUIプロジェクトと同等の機能を提供
- Windows 10 バージョン 1903 以降をサポート

## 🔄 MAUIとの違い

- **プラットフォーム**: Windows専用（WinUI）
- **UI フレームワーク**: WinUI 3.0 Native
- **パフォーマンス**: Windows最適化により高速
- **機能**: MAUIプロジェクトと同等

## 🐛 トラブルシューティング

### バックエンド接続エラー
- `https://localhost:44396` でバックエンドが起動していることを確認
- 証明書エラーの場合は開発証明書をインストール

### APIキー設定エラー
- `setup-secrets.ps1` を再実行
- `appsettings.secrets.json` ファイルの内容を確認

### ビルドエラー
- Visual Studio 2022 17.8 以降を使用
- Windows App SDK がインストールされていることを確認