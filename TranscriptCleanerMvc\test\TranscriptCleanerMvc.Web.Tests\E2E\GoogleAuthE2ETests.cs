using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using Xunit;
using AngleSharp;
using AngleSharp.Html.Dom;
using System.Net.Http;
using System.Text;
using Microsoft.Extensions.Configuration;

namespace TranscriptCleanerMvc.Web.Tests.E2E
{
    /// <summary>
    /// Google認証のE2Eテスト
    /// </summary>
    public class GoogleAuthE2ETests : TranscriptCleanerMvcWebTestBase
    {
        [Fact]
        public async Task GoogleLogin_ValidFlow_ShouldRedirectToCallback()
        {
            // Arrange
            var client = GetRequiredService<HttpClient>();

            // Act
            var response = await client.GetAsync("/Account/Login");

            // Assert
            response.IsSuccessStatusCode.ShouldBeTrue();
        }

        [Fact]
        public async Task GoogleCallback_ValidCode_ShouldProcessSuccessfully()
        {
            // Arrange
            var client = GetRequiredService<HttpClient>();
            var callbackUrl = "/signin-google?code=test_code&state=test_state";

            // Act
            var response = await client.GetAsync(callbackUrl);

            // Assert
            // リダイレクトまたは成功レスポンスを期待
            (response.IsSuccessStatusCode || response.StatusCode == System.Net.HttpStatusCode.Redirect).ShouldBeTrue();
        }
    }
}