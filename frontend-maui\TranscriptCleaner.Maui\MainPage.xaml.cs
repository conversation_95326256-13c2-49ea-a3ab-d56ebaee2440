using TranscriptCleaner.Maui.ViewModels;
using TranscriptCleaner.Maui.Services;
using Microsoft.Maui.Graphics;
using Microsoft.Maui.Controls.Shapes;
using System.Collections.ObjectModel;
using System.ComponentModel;

namespace TranscriptCleaner.Maui
{
    public class WordListItem : INotifyPropertyChanged
    {
        private string _incorrect = string.Empty;
        private string _correct = string.Empty;

        public string Incorrect
        {
            get => _incorrect;
            set
            {
                if (_incorrect != value)
                {
                    _incorrect = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Correct
        {
            get => _correct;
            set
            {
                if (_correct != value)
                {
                    _correct = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    [QueryProperty(nameof(Username), "Username")]
    [QueryProperty(nameof(IsLoggedInString), "IsLoggedIn")]
    public partial class MainPage : ContentPage
    {
        private readonly MainPageViewModel _viewModel;
        private readonly TranscriptCorrectionService _correctionService;

        private readonly ObservableCollection<WordListItem> _wordListItems;

        private string _username = string.Empty;
        public string Username 
        { 
            get => _username;
            set 
            { 
                _username = value;
                System.Diagnostics.Debug.WriteLine($"Username設定: {value}");
                
                // パラメータ設定後に初期化処理を実行
                if (IsLoggedIn && !string.IsNullOrEmpty(value))
                {
                    InitializeUserSession();
                }
            }
        }
        
        private string _isLoggedInString = "false";
        public string IsLoggedInString 
        { 
            get => _isLoggedInString;
            set 
            { 
                _isLoggedInString = value;
                IsLoggedIn = value?.ToLower() == "true";
                System.Diagnostics.Debug.WriteLine($"IsLoggedInString設定: {value} -> IsLoggedIn: {IsLoggedIn}");
                
                // パラメータ設定後に初期化処理を実行
                if (IsLoggedIn && !string.IsNullOrEmpty(Username))
                {
                    InitializeUserSession();
                }
            }
        }
        
        public bool IsLoggedIn { get; private set; }

        public MainPage(AuthenticationService authService)
        {
            InitializeComponent();
            _viewModel = new MainPageViewModel(authService);
            _correctionService = new TranscriptCorrectionService();
            _wordListItems = new ObservableCollection<WordListItem>();
            WordListCollectionView.ItemsSource = _wordListItems;
            BindingContext = _viewModel;
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            System.Diagnostics.Debug.WriteLine($"MainPage OnAppearing - Username: '{Username}', IsLoggedIn: {IsLoggedIn}");
            
            // QueryPropertyによるパラメータ設定を待つため、ここでは何もしない
            // 実際の初期化はInitializeUserSessionで行う
        }

        private void InitializeUserSession()
        {
            System.Diagnostics.Debug.WriteLine($"InitializeUserSession - Username: '{Username}', IsLoggedIn: {IsLoggedIn}");
            
            if (IsLoggedIn && !string.IsNullOrEmpty(Username))
            {
                System.Diagnostics.Debug.WriteLine("ユーザーセッション初期化中");
                _viewModel.UpdateLoginStatus(); // 認証サービスから状態を取得
                _viewModel.UpdateStatus($"ようこそ {Username} さん！ファイルを選択してAI訂正を開始してください。");
            }
        }

        private void ShowOriginalText()
        {
            // 初期メッセージを非表示
            InitialMessageBorder.IsVisible = false;
            
            // 元テキスト表示を有効
            OriginalTextBorder.IsVisible = true;
            OriginalTextEditor.Text = _viewModel.OriginalText;
        }

        private void ShowCorrectedText()
        {
            // 訂正後テキスト表示を有効
            CorrectedTextBorder.IsVisible = true;
            CorrectedTextEditor.Text = _viewModel.CorrectedText;
            
            // 差分表示も有効
            DiffDisplayBorder.IsVisible = true;
            GenerateWordLevelDiff();
        }

        private void GenerateWordLevelDiff()
        {
            try
            {
                if (string.IsNullOrEmpty(_viewModel.OriginalText) || string.IsNullOrEmpty(_viewModel.CorrectedText))
                    return;

                var originalWords = _viewModel.OriginalText.Split(new[] { ' ', '\n', '\r', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                var correctedWords = _viewModel.CorrectedText.Split(new[] { ' ', '\n', '\r', '\t' }, StringSplitOptions.RemoveEmptyEntries);

                var diffText = GenerateSimpleDiff(originalWords, correctedWords);
                DiffResultLabel.Text = diffText;
            }
            catch (Exception ex)
            {
                DiffResultLabel.Text = $"差分生成中にエラーが発生しました: {ex.Message}";
            }
        }

        private string GenerateSimpleDiff(string[] original, string[] corrected)
        {
            var result = new List<string>();
            var maxLength = Math.Max(original.Length, corrected.Length);
            
            for (int i = 0; i < maxLength; i++)
            {
                var originalWord = i < original.Length ? original[i] : null;
                var correctedWord = i < corrected.Length ? corrected[i] : null;
                
                if (originalWord == correctedWord)
                {
                    result.Add(originalWord ?? "");
                }
                else if (originalWord != null && correctedWord != null)
                {
                    // 変更
                    result.Add($"[変更: {originalWord} → {correctedWord}]");
                }
                else if (originalWord != null)
                {
                    // 削除
                    result.Add($"[削除: {originalWord}]");
                }
                else if (correctedWord != null)
                {
                    // 追加
                    result.Add($"[追加: {correctedWord}]");
                }
            }
            
            return string.Join(" ", result);
        }

        private async void OnSelectTranscriptFileClicked(object sender, EventArgs e)
        {
            try
            {
                var textFileType = new FilePickerFileType(
                    new Dictionary<DevicePlatform, IEnumerable<string>>
                    {
                        { DevicePlatform.iOS, new[] { "public.text", "public.plain-text", "public.utf8-plain-text" } },
                        { DevicePlatform.Android, new[] { "text/plain" } },
                        { DevicePlatform.WinUI, new[] { ".txt" } },
                        { DevicePlatform.Tizen, new[] { "text/plain" } },
                        { DevicePlatform.macOS, new[] { "txt" } },
                    });

                var options = new PickOptions()
                {
                    PickerTitle = "議事録テキストファイルを選択してください",
                    FileTypes = textFileType,
                };

                var fileResult = await FilePicker.Default.PickAsync(options);
                if (fileResult != null)
                {
                    TranscriptFileLabel.Text = fileResult.FileName;
                    _viewModel.SelectedTranscriptFilePath = fileResult.FullPath;
                    _viewModel.UpdateStatus("ファイルを読み込んでいます...");

                    // ファイル内容を読み込み
                    using var stream = await fileResult.OpenReadAsync();
                    using var reader = new StreamReader(stream);
                    var content = await reader.ReadToEndAsync();

                    _viewModel.OriginalText = content;
                    _viewModel.UpdateStatus($"ファイルを読み込みました: {fileResult.FileName}");
                    
                    // 元テキスト表示エリアを表示
                    ShowOriginalText();
                }
            }
            catch (Exception ex)
            {
                _viewModel.UpdateStatus($"ファイル選択エラー: {ex.Message}");
                await DisplayAlert("エラー", $"ファイルの読み込みに失敗しました:\n{ex.Message}", "OK");
            }
        }

        private async void OnSelectWordListFileClicked(object sender, EventArgs e)
        {
            try
            {
                var csvFileType = new FilePickerFileType(
                    new Dictionary<DevicePlatform, IEnumerable<string>>
                    {
                        { DevicePlatform.iOS, new[] { "public.comma-separated-values-text" } },
                        { DevicePlatform.Android, new[] { "text/csv" } },
                        { DevicePlatform.WinUI, new[] { ".csv" } },
                        { DevicePlatform.Tizen, new[] { "text/csv" } },
                        { DevicePlatform.macOS, new[] { "csv" } },
                    });

                var options = new PickOptions()
                {
                    PickerTitle = "誤字脱字リストCSVファイルを選択してください",
                    FileTypes = csvFileType,
                };

                var fileResult = await FilePicker.Default.PickAsync(options);
                if (fileResult != null)
                {
                    WordListFileLabel.Text = fileResult.FileName;
                    _viewModel.SelectedWordListFilePath = fileResult.FullPath;
                    _viewModel.UpdateStatus("誤字脱字リストを読み込んでいます...");

                    // CSVファイル内容を読み込み
                    using var stream = await fileResult.OpenReadAsync();
                    using var reader = new StreamReader(stream);
                    var content = await reader.ReadToEndAsync();

                    _viewModel.WordListContent = content;
                    ParseAndDisplayWordList(content);
                    _viewModel.UpdateStatus($"誤字脱字リストを読み込みました: {fileResult.FileName}");
                }
            }
            catch (Exception ex)
            {
                _viewModel.UpdateStatus($"誤字脱字リスト選択エラー: {ex.Message}");
                await DisplayAlert("エラー", $"誤字脱字リストの読み込みに失敗しました:\n{ex.Message}", "OK");
            }
        }

        private async void OnCompareClicked(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(_viewModel.OriginalText) || string.IsNullOrWhiteSpace(_viewModel.CorrectedText))
                {
                    await DisplayAlert("警告", "差分表示するには元のテキストと訂正後のテキストが必要です。", "OK");
                    return;
                }

                // 差分表示の再生成
                GenerateWordLevelDiff();
                
                // 差分エリアにスクロール（可能であれば）
                _viewModel.UpdateStatus("単語レベルの差分を更新しました。下部の差分表示をご確認ください。");
                
                await DisplayAlert("差分更新", "単語レベルの差分表示を更新しました。\n下部エリアで詳細な変更内容をご確認ください。", "OK");
            }
            catch (Exception ex)
            {
                await DisplayAlert("エラー", $"差分表示の更新でエラーが発生しました:\n{ex.Message}", "OK");
            }
        }

        private async void OnLogoutClicked(object sender, EventArgs e)
        {
            try
            {
                var result = await DisplayAlert("ログアウト確認", "ログアウトしますか？", "はい", "いいえ");
                if (result)
                {
                    // 認証サービスからログアウト
                    await _viewModel.LogoutAsync();
                    
                    // データをクリア
                    _viewModel.OriginalText = string.Empty;
                    _viewModel.CorrectedText = string.Empty;
                    _viewModel.WordListContent = string.Empty;
                    
                    TranscriptFileLabel.Text = "📄 ここをタップしてファイル選択 (またはボタンをクリック)";
                    WordListFileLabel.Text = "📊 ここをタップしてCSVファイル選択 (またはボタンをクリック)";
                    
                    // ログイン画面に戻る
                    await Shell.Current.GoToAsync("//LoginPage");
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("エラー", $"ログアウト処理でエラーが発生しました:\n{ex.Message}", "OK");
            }
        }

        private async void OnProcessClicked(object sender, EventArgs e)
        {
            if (!_viewModel.IsLoggedIn)
            {
                await DisplayAlert("警告", "ログインしてください。", "OK");
                return;
            }
            
            if (string.IsNullOrWhiteSpace(_viewModel.OriginalText))
            {
                await DisplayAlert("警告", "訂正するテキストを入力してください。", "OK");
                return;
            }

            try
            {
                _viewModel.SetProcessing(true);
                _viewModel.UpdateStatus("AI訂正処理を開始しています...");
                
                // プロセスバー表示のため少し待機
                await Task.Delay(100);
                
                // 処理開始確認
                var wordListInfo = !string.IsNullOrWhiteSpace(_viewModel.WordListContent) 
                    ? "誤字脱字リストを使用して" 
                    : "";
                _viewModel.UpdateStatus($"AI訂正処理を実行中です...{wordListInfo}");

                // AI訂正処理を実行（誤字脱字リストも渡す）
                var startTime = DateTime.Now;
                var correctedText = await _correctionService.CorrectTranscriptAsync(_viewModel.OriginalText, _viewModel.WordListContent);
                var processingTime = DateTime.Now - startTime;
                
                _viewModel.CorrectedText = correctedText;
                
                // 処理結果の統計情報を表示
                var originalLength = _viewModel.OriginalText.Length;
                var correctedLength = correctedText.Length;
                var lengthDiff = correctedLength - originalLength;
                var lengthChange = lengthDiff >= 0 ? $"+{lengthDiff}" : $"{lengthDiff}";
                
                var statsMessage = $"AI訂正完了 | 処理時間: {processingTime.TotalSeconds:F1}秒 | 文字数: {originalLength} → {correctedLength} ({lengthChange})";
                _viewModel.UpdateStatus(statsMessage);
                
                // 訂正後テキストと差分を表示
                ShowCorrectedText();
                
                // 成功メッセージ
                await DisplayAlert("処理完了", 
                    $"AI訂正処理が完了しました。\n\n処理時間: {processingTime.TotalSeconds:F1}秒\n文字数変化: {lengthChange}\n\n下部の差分表示で詳細な変更を確認できます。", 
                    "OK");
            }
            catch (Exception ex)
            {
                _viewModel.UpdateStatus($"処理エラー: {ex.Message}");
                await DisplayAlert("エラー", $"訂正処理に失敗しました:\n{ex.Message}", "OK");
            }
            finally
            {
                _viewModel.SetProcessing(false);
            }
        }

        private async void OnSaveClicked(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(_viewModel.CorrectedText))
            {
                await DisplayAlert("警告", "保存する訂正済みテキストがありません。", "OK");
                return;
            }

            try
            {
                _viewModel.UpdateStatus("ファイルを保存しています...");

                // 保存先ファイル名を生成
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var fileName = $"corrected_transcript_{timestamp}.txt";

                // プラットフォーム別の保存処理
                if (DeviceInfo.Platform == DevicePlatform.WinUI)
                {
                    await SaveFileWindows(fileName, _viewModel.CorrectedText);
                }
                else
                {
                    await SaveFileOtherPlatforms(fileName, _viewModel.CorrectedText);
                }

                _viewModel.UpdateStatus($"ファイルを保存しました: {fileName}");
                await DisplayAlert("完了", $"訂正済みテキストを保存しました:\n{fileName}", "OK");
            }
            catch (Exception ex)
            {
                _viewModel.UpdateStatus($"保存エラー: {ex.Message}");
                await DisplayAlert("エラー", $"ファイルの保存に失敗しました:\n{ex.Message}", "OK");
            }
        }

        private static async Task SaveFileWindows(string fileName, string content)
        {
            var downloadsPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            var downloadsFolder = System.IO.Path.Combine(downloadsPath, "Downloads");
            
            if (!Directory.Exists(downloadsFolder))
                Directory.CreateDirectory(downloadsFolder);

            var filePath = System.IO.Path.Combine(downloadsFolder, fileName);
            await File.WriteAllTextAsync(filePath, content, System.Text.Encoding.UTF8);
        }

        private static async Task SaveFileOtherPlatforms(string fileName, string content)
        {
            // その他のプラットフォーム用の保存処理
            var tempFile = System.IO.Path.Combine(System.IO.Path.GetTempPath(), fileName);
            await File.WriteAllTextAsync(tempFile, content, System.Text.Encoding.UTF8);

            // ファイル共有機能を使用
            await Share.Default.RequestAsync(new ShareFileRequest
            {
                Title = "訂正済みテキストを保存",
                File = new ShareFile(tempFile)
            });
        }
        
        // タップジェスチャーイベントハンドラー
        private void OnTranscriptFileAreaTapped(object sender, EventArgs e)
        {
            OnSelectTranscriptFileClicked(sender, e);
        }

        private void OnWordListFileAreaTapped(object sender, EventArgs e)
        {
            OnSelectWordListFileClicked(sender, e);
        }

        private void OnAddWordClicked(object sender, EventArgs e)
        {
            _wordListItems.Add(new WordListItem { Incorrect = "", Correct = "" });
            WordListDisplaySection.IsVisible = true;
            UpdateWordListCsv();
        }

        private void OnRemoveWordClicked(object sender, EventArgs e)
        {
            if (sender is Button button && button.CommandParameter is WordListItem item)
            {
                _wordListItems.Remove(item);
                UpdateWordListCsv();
                
                if (_wordListItems.Count == 0)
                {
                    WordListDisplaySection.IsVisible = false;
                }
            }
        }

        private async void OnSaveWordListClicked(object sender, EventArgs e)
        {
            try
            {
                UpdateWordListCsv();
                var fileName = $"wordlist_{DateTime.Now:yyyyMMdd}.csv";
                var content = _viewModel.WordListContent ?? "";

                if (string.IsNullOrWhiteSpace(content))
                {
                    await DisplayAlert("警告", "保存する誤字脱字一覧がありません。", "OK");
                    return;
                }

#if WINDOWS
                await SaveFileWindows(fileName, content);
#else
                await SaveFileOtherPlatforms(fileName, content);
#endif

                _viewModel.UpdateStatus($"誤字脱字一覧を保存しました: {fileName}");
                await DisplayAlert("完了", $"誤字脱字一覧を保存しました:\n{fileName}", "OK");
            }
            catch (Exception ex)
            {
                _viewModel.UpdateStatus($"保存エラー: {ex.Message}");
                await DisplayAlert("エラー", $"ファイルの保存に失敗しました:\n{ex.Message}", "OK");
            }
        }

        private void UpdateWordListCsv()
        {
            var csvLines = _wordListItems
                .Where(item => !string.IsNullOrWhiteSpace(item.Incorrect) && !string.IsNullOrWhiteSpace(item.Correct))
                .Select(item => $@"""{item.Incorrect.Replace(@"""", @"""""")}"",""{item.Correct.Replace(@"""", @"""""")}""");

            _viewModel.WordListContent = string.Join(Environment.NewLine, csvLines);
        }

        private void ParseAndDisplayWordList(string csvContent)
        {
            _wordListItems.Clear();
            
            if (string.IsNullOrWhiteSpace(csvContent))
                return;

            var lines = csvContent.Split(new char[] { '\n' }, StringSplitOptions.RemoveEmptyEntries);
            
            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                if (string.IsNullOrEmpty(trimmedLine))
                    continue;

                var columns = ParseCsvLine(trimmedLine);
                if (columns.Count >= 2)
                {
                    _wordListItems.Add(new WordListItem
                    {
                        Incorrect = columns[0],
                        Correct = columns[1]
                    });
                }
            }

            WordListDisplaySection.IsVisible = _wordListItems.Count > 0;
        }

        private List<string> ParseCsvLine(string line)
        {
            var result = new List<string>();
            var current = string.Empty;
            var inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                var c = line[i];

                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(current.Trim().Trim('\"'));
                    current = string.Empty;
                }
                else
                {
                    current += c;
                }
            }

            if (!string.IsNullOrEmpty(current))
            {
                result.Add(current.Trim().Trim('\"'));
            }

            return result;
        }
    }
}
