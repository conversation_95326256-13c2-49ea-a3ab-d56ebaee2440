using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// アカウント連携サービスインターフェース
    /// </summary>
    public interface IAccountLinkService : IApplicationService
    {
        /// <summary>
        /// 外部アカウント連携の重複チェック
        /// </summary>
        /// <param name="provider">プロバイダー名</param>
        /// <param name="providerKey">プロバイダーキー</param>
        /// <param name="excludeUserId">除外するユーザーID</param>
        /// <returns>重複している場合はtrue</returns>
        Task<bool> IsExternalAccountAlreadyLinkedAsync(string provider, string providerKey, Guid? excludeUserId = null);

        /// <summary>
        /// ユーザーの外部アカウント連携一覧を取得
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>連携アカウント一覧</returns>
        Task<List<ExternalAccountLinkDto>> GetUserExternalAccountLinksAsync(Guid userId);

        /// <summary>
        /// 外部アカウント連携を強制解除（管理者用）
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="provider">プロバイダー名</param>
        /// <returns>解除成功フラグ</returns>
        Task<bool> ForceUnlinkExternalAccountAsync(Guid userId, string provider);

        /// <summary>
        /// 外部アカウント連携の検証
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="provider">プロバイダー名</param>
        /// <param name="providerKey">プロバイダーキー</param>
        /// <returns>検証結果</returns>
        Task<AccountLinkValidationResultDto> ValidateAccountLinkAsync(Guid userId, string provider, string providerKey);
    }
}