using System;
using System.Threading.Tasks;

namespace TranscriptCleaner.WinUI.Services
{
    /// <summary>
    /// 認証状態管理サービスインターフェース
    /// </summary>
    public interface IAuthStateManager
    {
        /// <summary>
        /// 認証状態変更イベント
        /// </summary>
        event EventHandler<AuthStateChangedEventArgs> AuthStateChanged;

        /// <summary>
        /// 現在の認証状態を取得
        /// </summary>
        /// <returns>認証状態</returns>
        Task<AuthenticationStatus> GetCurrentAuthStatusAsync();

        /// <summary>
        /// 認証状態を更新
        /// </summary>
        /// <param name="status">新しい認証状態</param>
        /// <returns>更新処理</returns>
        Task UpdateAuthStatusAsync(AuthenticationStatus status);

        /// <summary>
        /// アプリケーション最小化時の処理
        /// </summary>
        /// <returns>処理</returns>
        Task OnApplicationMinimizedAsync();

        /// <summary>
        /// アプリケーション復元時の処理
        /// </summary>
        /// <returns>処理</returns>
        Task OnApplicationRestoredAsync();

        /// <summary>
        /// ローカル認証情報を管理
        /// </summary>
        /// <returns>管理処理</returns>
        Task ManageLocalAuthDataAsync();

        /// <summary>
        /// キャッシュをクリア
        /// </summary>
        /// <returns>クリア処理</returns>
        Task ClearCacheAsync();

        /// <summary>
        /// 認証の有効性を検証
        /// </summary>
        /// <returns>有効性</returns>
        Task<bool> ValidateAuthenticationAsync();

        /// <summary>
        /// 自動ログイン設定を管理
        /// </summary>
        /// <param name="enabled">有効フラグ</param>
        /// <returns>設定処理</returns>
        Task SetAutoLoginAsync(bool enabled);

        /// <summary>
        /// 自動ログイン設定を取得
        /// </summary>
        /// <returns>設定状態</returns>
        Task<bool> GetAutoLoginSettingAsync();
    }

    /// <summary>
    /// 認証状態
    /// </summary>
    public enum AuthenticationStatus
    {
        /// <summary>
        /// 未認証
        /// </summary>
        NotAuthenticated,

        /// <summary>
        /// 認証中
        /// </summary>
        Authenticating,

        /// <summary>
        /// 認証済み
        /// </summary>
        Authenticated,

        /// <summary>
        /// トークン期限切れ
        /// </summary>
        TokenExpired,

        /// <summary>
        /// エラー
        /// </summary>
        Error,

        /// <summary>
        /// ログアウト中
        /// </summary>
        LoggingOut
    }

    /// <summary>
    /// 認証状態変更イベント引数
    /// </summary>
    public class AuthStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 前の状態
        /// </summary>
        public AuthenticationStatus PreviousStatus { get; set; }

        /// <summary>
        /// 現在の状態
        /// </summary>
        public AuthenticationStatus CurrentStatus { get; set; }

        /// <summary>
        /// 変更理由
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 変更日時
        /// </summary>
        public DateTime Timestamp { get; set; }

        public AuthStateChangedEventArgs(AuthenticationStatus previous, AuthenticationStatus current, string? reason = null)
        {
            PreviousStatus = previous;
            CurrentStatus = current;
            Reason = reason;
            Timestamp = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// ローカル認証データ
    /// </summary>
    public class LocalAuthData
    {
        /// <summary>
        /// ユーザーID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// ユーザー名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// メールアドレス
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 最後のログイン日時
        /// </summary>
        public DateTime LastLoginTime { get; set; }

        /// <summary>
        /// 認証プロバイダー
        /// </summary>
        public string Provider { get; set; } = string.Empty;

        /// <summary>
        /// プロフィール画像URL
        /// </summary>
        public string? ProfileImageUrl { get; set; }

        /// <summary>
        /// 自動ログイン設定
        /// </summary>
        public bool AutoLoginEnabled { get; set; }

        /// <summary>
        /// データの有効期限
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// データが有効かチェック
        /// </summary>
        public bool IsValid => DateTime.UtcNow < ExpiresAt;
    }
}