@page
@model TranscriptCleanerMvc.Web.Pages.Account.Manage.ExternalLoginsPageModel
@using Microsoft.AspNetCore.Mvc.Localization
@using TranscriptCleanerMvc.Localization
@inject IHtmlLocalizer<TranscriptCleanerMvcResource> L
@{
    ViewData["Title"] = L["ExternalAccountLinking"];
    ViewData["ActivePage"] = "ExternalLogins";
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">@L["ExternalAccountLinking"]</h4>
            </div>
            <div class="card-body">
                @if (Model.ShowRemoveButton || Model.ExternalLogins.Any())
                {
                    <div class="alert alert-info">
                        <p>@L["ExternalAccountLinkingDescription"]</p>
                    </div>
                }

                @if (Model.ExternalLogins.Any())
                {
                    <h5>@L["LinkedAccounts"]</h5>
                    <div class="external-accounts-list">
                        @foreach (var login in Model.ExternalLogins)
                        {
                            <div class="external-account-item">
                                <div class="account-info">
                                    <div class="provider-icon">
                                        @if (login.LoginProvider == "Google")
                                        {
                                            <svg class="google-icon" viewBox="0 0 24 24" width="24" height="24">
                                                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                            </svg>
                                        }
                                    </div>
                                    <div class="account-details">
                                        <div class="provider-name">@login.ProviderDisplayName</div>
                                        <div class="account-status">@L["AccountLinked"]</div>
                                    </div>
                                </div>
                                <div class="account-actions">
                                    <form asp-page-handler="RemoveLogin" method="post">
                                        <input asp-for="@login.LoginProvider" name="LoginProvider" type="hidden" />
                                        <input asp-for="@login.ProviderKey" name="ProviderKey" type="hidden" />
                                        <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                title="@L["UnlinkAccount"]"
                                                onclick="return confirm('@L["ConfirmUnlinkAccount"]')">
                                            @L["UnlinkAccount"]
                                        </button>
                                    </form>
                                </div>
                            </div>
                        }
                    </div>
                }

                @if (Model.OtherLogins.Any())
                {
                    <h5 class="mt-4">@L["AvailableProviders"]</h5>
                    <div class="external-providers-list">
                        @foreach (var provider in Model.OtherLogins)
                        {
                            <div class="external-provider-item">
                                <div class="provider-info">
                                    <div class="provider-icon">
                                        @if (provider.Name == "Google")
                                        {
                                            <svg class="google-icon" viewBox="0 0 24 24" width="24" height="24">
                                                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                            </svg>
                                        }
                                    </div>
                                    <div class="provider-details">
                                        <div class="provider-name">@provider.DisplayName</div>
                                        <div class="provider-status">@L["AccountNotLinked"]</div>
                                    </div>
                                </div>
                                <div class="provider-actions">
                                    <form asp-page-handler="LinkLogin" method="post">
                                        <input type="hidden" name="Provider" value="@provider.Name" />
                                        <button type="submit" class="btn btn-outline-primary btn-sm">
                                            @L["LinkAccount"]
                                        </button>
                                    </form>
                                </div>
                            </div>
                        }
                    </div>
                }

                @if (!Model.ExternalLogins.Any() && !Model.OtherLogins.Any())
                {
                    <div class="alert alert-info">
                        <p>@L["NoExternalProvidersAvailable"]</p>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">@L["SyncSettings"]</h5>
            </div>
            <div class="card-body">
                <form method="post" asp-page-handler="UpdateSyncSettings">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="autoSync" 
                               asp-for="SyncSettings.AutoSyncEnabled" />
                        <label class="form-check-label" for="autoSync">
                            @L["EnableAutoSync"]
                        </label>
                    </div>
                    
                    <div class="mb-3">
                        <label for="syncInterval" class="form-label">@L["SyncInterval"]</label>
                        <select class="form-select" id="syncInterval" asp-for="SyncSettings.SyncIntervalHours">
                            <option value="1">@L["EveryHour"]</option>
                            <option value="6">@L["Every6Hours"]</option>
                            <option value="12">@L["Every12Hours"]</option>
                            <option value="24">@L["Daily"]</option>
                            <option value="168">@L["Weekly"]</option>
                        </select>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="syncName" 
                               asp-for="SyncSettings.SyncName" />
                        <label class="form-check-label" for="syncName">
                            @L["SyncName"]
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="syncProfileImage" 
                               asp-for="SyncSettings.SyncProfileImage" />
                        <label class="form-check-label" for="syncProfileImage">
                            @L["SyncProfileImage"]
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-sm">
                        @L["SaveSettings"]
                    </button>
                </form>
                
                @if (Model.ExternalLogins.Any())
                {
                    <hr />
                    <form method="post" asp-page-handler="SyncNow">
                        <button type="submit" class="btn btn-outline-secondary btn-sm">
                            @L["SyncNow"]
                        </button>
                    </form>
                }
            </div>
        </div>
    </div>
</div>

<style>
.external-accounts-list, .external-providers-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.external-account-item, .external-provider-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    background: #f8f9fa;
}

.account-info, .provider-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.provider-icon {
    flex-shrink: 0;
}

.account-details, .provider-details {
    display: flex;
    flex-direction: column;
}

.provider-name {
    font-weight: 500;
    color: #333;
}

.account-status, .provider-status {
    font-size: 0.875rem;
    color: #666;
}

.google-icon {
    width: 24px;
    height: 24px;
}
</style>