using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// 管理者設定サービスインターフェース
    /// </summary>
    public interface IAdminSettingsService : IApplicationService
    {
        /// <summary>
        /// Google認証設定を取得
        /// </summary>
        /// <returns>Google認証設定</returns>
        Task<GoogleAuthSettingsDto> GetGoogleAuthSettingsAsync();

        /// <summary>
        /// Google認証設定を更新
        /// </summary>
        /// <param name="input">更新する設定</param>
        /// <returns>更新された設定</returns>
        Task<GoogleAuthSettingsDto> UpdateGoogleAuthSettingsAsync(UpdateGoogleAuthSettingsDto input);

        /// <summary>
        /// 外部認証統計を取得
        /// </summary>
        /// <returns>統計情報</returns>
        Task<ExternalAuthStatisticsDto> GetExternalAuthStatisticsAsync();

        /// <summary>
        /// システム設定を取得
        /// </summary>
        /// <returns>システム設定</returns>
        Task<SystemSettingsDto> GetSystemSettingsAsync();

        /// <summary>
        /// システム設定を更新
        /// </summary>
        /// <param name="input">更新する設定</param>
        /// <returns>更新された設定</returns>
        Task<SystemSettingsDto> UpdateSystemSettingsAsync(UpdateSystemSettingsDto input);

        /// <summary>
        /// 設定変更履歴を取得
        /// </summary>
        /// <returns>変更履歴</returns>
        Task<SettingsChangeHistoryDto[]> GetSettingsChangeHistoryAsync();
    }

    /// <summary>
    /// Google認証設定DTO
    /// </summary>
    public class GoogleAuthSettingsDto
    {
        /// <summary>
        /// Google認証有効フラグ
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// クライアントID
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// クライアントシークレット（マスク済み）
        /// </summary>
        public string? ClientSecretMasked { get; set; }

        /// <summary>
        /// リダイレクトURI
        /// </summary>
        public string? RedirectUri { get; set; }

        /// <summary>
        /// スコープ
        /// </summary>
        public string[]? Scopes { get; set; }

        /// <summary>
        /// 自動アカウント作成フラグ
        /// </summary>
        public bool AutoCreateAccount { get; set; }

        /// <summary>
        /// デフォルトロール
        /// </summary>
        public string[]? DefaultRoles { get; set; }

        /// <summary>
        /// 最終更新日時
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// 最終更新者
        /// </summary>
        public string? LastUpdatedBy { get; set; }
    }

    /// <summary>
    /// Google認証設定更新DTO
    /// </summary>
    public class UpdateGoogleAuthSettingsDto
    {
        /// <summary>
        /// Google認証有効フラグ
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// クライアントID
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// クライアントシークレット
        /// </summary>
        public string? ClientSecret { get; set; }

        /// <summary>
        /// リダイレクトURI
        /// </summary>
        public string? RedirectUri { get; set; }

        /// <summary>
        /// スコープ
        /// </summary>
        public string[]? Scopes { get; set; }

        /// <summary>
        /// 自動アカウント作成フラグ
        /// </summary>
        public bool AutoCreateAccount { get; set; }

        /// <summary>
        /// デフォルトロール
        /// </summary>
        public string[]? DefaultRoles { get; set; }
    }

    /// <summary>
    /// 外部認証統計DTO
    /// </summary>
    public class ExternalAuthStatisticsDto
    {
        /// <summary>
        /// 総ユーザー数
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// Google連携ユーザー数
        /// </summary>
        public int GoogleLinkedUsers { get; set; }

        /// <summary>
        /// 今日のGoogle認証数
        /// </summary>
        public int TodayGoogleLogins { get; set; }

        /// <summary>
        /// 今週のGoogle認証数
        /// </summary>
        public int WeeklyGoogleLogins { get; set; }

        /// <summary>
        /// 今月のGoogle認証数
        /// </summary>
        public int MonthlyGoogleLogins { get; set; }

        /// <summary>
        /// 認証エラー数（今日）
        /// </summary>
        public int TodayAuthErrors { get; set; }

        /// <summary>
        /// アクティブセッション数
        /// </summary>
        public int ActiveSessions { get; set; }

        /// <summary>
        /// 最後の統計更新時刻
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// システム設定DTO
    /// </summary>
    public class SystemSettingsDto
    {
        /// <summary>
        /// セッションタイムアウト（分）
        /// </summary>
        public int SessionTimeoutMinutes { get; set; }

        /// <summary>
        /// 最大同時セッション数
        /// </summary>
        public int MaxConcurrentSessions { get; set; }

        /// <summary>
        /// パスワードポリシー有効フラグ
        /// </summary>
        public bool PasswordPolicyEnabled { get; set; }

        /// <summary>
        /// 2要素認証必須フラグ
        /// </summary>
        public bool RequireTwoFactorAuth { get; set; }

        /// <summary>
        /// ログ保持期間（日）
        /// </summary>
        public int LogRetentionDays { get; set; }

        /// <summary>
        /// メンテナンスモードフラグ
        /// </summary>
        public bool MaintenanceMode { get; set; }

        /// <summary>
        /// メンテナンスメッセージ
        /// </summary>
        public string? MaintenanceMessage { get; set; }
    }

    /// <summary>
    /// システム設定更新DTO
    /// </summary>
    public class UpdateSystemSettingsDto
    {
        /// <summary>
        /// セッションタイムアウト（分）
        /// </summary>
        public int SessionTimeoutMinutes { get; set; }

        /// <summary>
        /// 最大同時セッション数
        /// </summary>
        public int MaxConcurrentSessions { get; set; }

        /// <summary>
        /// パスワードポリシー有効フラグ
        /// </summary>
        public bool PasswordPolicyEnabled { get; set; }

        /// <summary>
        /// 2要素認証必須フラグ
        /// </summary>
        public bool RequireTwoFactorAuth { get; set; }

        /// <summary>
        /// ログ保持期間（日）
        /// </summary>
        public int LogRetentionDays { get; set; }

        /// <summary>
        /// メンテナンスモードフラグ
        /// </summary>
        public bool MaintenanceMode { get; set; }

        /// <summary>
        /// メンテナンスメッセージ
        /// </summary>
        public string? MaintenanceMessage { get; set; }
    }

    /// <summary>
    /// 設定変更履歴DTO
    /// </summary>
    public class SettingsChangeHistoryDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 設定名
        /// </summary>
        public string SettingName { get; set; } = string.Empty;

        /// <summary>
        /// 変更前の値
        /// </summary>
        public string? OldValue { get; set; }

        /// <summary>
        /// 変更後の値
        /// </summary>
        public string? NewValue { get; set; }

        /// <summary>
        /// 変更者
        /// </summary>
        public string ChangedBy { get; set; } = string.Empty;

        /// <summary>
        /// 変更日時
        /// </summary>
        public DateTime ChangedAt { get; set; }

        /// <summary>
        /// 変更理由
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string? IpAddress { get; set; }
    }
}