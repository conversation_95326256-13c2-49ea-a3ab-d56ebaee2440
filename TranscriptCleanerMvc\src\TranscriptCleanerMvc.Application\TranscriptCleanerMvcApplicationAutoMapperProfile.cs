using AutoMapper;
using TranscriptCleanerMvc.Entities;
using TranscriptCleanerMvc.Transcripts.Dtos;
using TranscriptCleanerMvc.WordLists.Dtos;

namespace TranscriptCleanerMvc;

public class TranscriptCleanerMvcApplicationAutoMapperProfile : Profile
{
    public TranscriptCleanerMvcApplicationAutoMapperProfile()
    {
        /* You can configure your AutoMapper mapping configuration here.
         * Alternatively, you can split your mapping configurations
         * into multiple profile classes for a better organization. */
         
        CreateMap<Transcript, TranscriptDto>()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));
        CreateMap<CreateTranscriptDto, Transcript>();
        CreateMap<UpdateTranscriptDto, Transcript>();
        
        CreateMap<WordList, WordListDto>();
        CreateMap<CreateWordListDto, WordList>();
        CreateMap<UpdateWordListDto, WordList>();
    }
}
