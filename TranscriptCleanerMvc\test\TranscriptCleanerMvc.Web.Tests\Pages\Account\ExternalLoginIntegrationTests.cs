using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using Volo.Abp.AspNetCore.TestBase;
using Xunit;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Web.Tests.Pages.Account
{
    /// <summary>
    /// 外部ログインページの統合テスト
    /// </summary>
    public class ExternalLoginIntegrationTests : TranscriptCleanerMvcWebTestBase
    {
        private readonly IExternalLoginService _externalLoginService;

        public ExternalLoginIntegrationTests()
        {
            _externalLoginService = GetRequiredService<IExternalLoginService>();
        }

        [Fact]
        public async Task HandleGoogleCallback_ValidCode_ShouldReturnResult()
        {
            // Arrange
            var code = "test_auth_code";
            var state = "test_state";

            // Act
            var result = await _externalLoginService.HandleGoogleCallbackAsync(code, state);

            // Assert
            result.ShouldNotBeNull();
        }

        [Fact]
        public async Task LinkGoogleAccount_ValidParameters_ShouldComplete()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var googleId = "google_test_123";

            // Act
            var result = await _externalLoginService.LinkGoogleAccountAsync(userId, googleId);

            // Assert
            result.ShouldBeOfType<bool>();
        }
    }
}