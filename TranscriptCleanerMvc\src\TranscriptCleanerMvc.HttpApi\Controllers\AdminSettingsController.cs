using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Controllers
{
    /// <summary>
    /// 管理者設定コントローラー
    /// </summary>
    [ApiController]
    [Route("api/admin/settings")]
    [Authorize(Roles = "admin")]
    public class AdminSettingsController : AbpControllerBase
    {
        private readonly IAdminSettingsService _adminSettingsService;

        public AdminSettingsController(IAdminSettingsService adminSettingsService)
        {
            _adminSettingsService = adminSettingsService;
        }

        /// <summary>
        /// Google認証設定を取得
        /// </summary>
        /// <returns>Google認証設定</returns>
        [HttpGet("google-auth")]
        public async Task<GoogleAuthSettingsDto> GetGoogleAuthSettingsAsync()
        {
            return await _adminSettingsService.GetGoogleAuthSettingsAsync();
        }

        /// <summary>
        /// Google認証設定を更新
        /// </summary>
        /// <param name="input">更新する設定</param>
        /// <returns>更新された設定</returns>
        [HttpPut("google-auth")]
        public async Task<GoogleAuthSettingsDto> UpdateGoogleAuthSettingsAsync(UpdateGoogleAuthSettingsDto input)
        {
            return await _adminSettingsService.UpdateGoogleAuthSettingsAsync(input);
        }

        /// <summary>
        /// 外部認証統計を取得
        /// </summary>
        /// <returns>統計情報</returns>
        [HttpGet("statistics")]
        public async Task<ExternalAuthStatisticsDto> GetExternalAuthStatisticsAsync()
        {
            return await _adminSettingsService.GetExternalAuthStatisticsAsync();
        }

        /// <summary>
        /// システム設定を取得
        /// </summary>
        /// <returns>システム設定</returns>
        [HttpGet("system")]
        public async Task<SystemSettingsDto> GetSystemSettingsAsync()
        {
            return await _adminSettingsService.GetSystemSettingsAsync();
        }

        /// <summary>
        /// システム設定を更新
        /// </summary>
        /// <param name="input">更新する設定</param>
        /// <returns>更新された設定</returns>
        [HttpPut("system")]
        public async Task<SystemSettingsDto> UpdateSystemSettingsAsync(UpdateSystemSettingsDto input)
        {
            return await _adminSettingsService.UpdateSystemSettingsAsync(input);
        }

        /// <summary>
        /// 設定変更履歴を取得
        /// </summary>
        /// <returns>変更履歴</returns>
        [HttpGet("history")]
        public async Task<SettingsChangeHistoryDto[]> GetSettingsChangeHistoryAsync()
        {
            return await _adminSettingsService.GetSettingsChangeHistoryAsync();
        }
    }
}