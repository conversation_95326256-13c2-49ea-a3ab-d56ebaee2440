using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace TranscriptCleaner.Maui.Services;

public class AuthenticationService
{
    private readonly HttpClient _httpClient;
    private readonly string _baseUrl = "https://localhost:44396"; // TranscriptCleanerMvc Backend URL
    private string? _authToken;
    private UserInfo? _currentUser;

    public bool IsAuthenticated => !string.IsNullOrEmpty(_authToken) && _currentUser != null;
    public UserInfo? CurrentUser => _currentUser;

    public AuthenticationService()
    {
        // For development: Skip HTTPS certificate validation
        var handler = new HttpClientHandler();
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
        
        _httpClient = new HttpClient(handler);
        _httpClient.Timeout = TimeSpan.FromMinutes(2);
    }

    public async Task<LoginResult> LoginAsync(string username, string password)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"Attempting login for user: {username}");
            
            // Try ABP Account API first
            var loginRequest = new AbpLoginRequest
            {
                UserNameOrEmailAddress = username,
                Password = password,
                RememberMe = true
            };

            var json = JsonSerializer.Serialize(loginRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            System.Diagnostics.Debug.WriteLine($"ABP Login endpoint: {_baseUrl}/api/account/login");
            var response = await _httpClient.PostAsync($"{_baseUrl}/api/account/login", content);
            
            System.Diagnostics.Debug.WriteLine($"ABP Login Response Status: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                System.Diagnostics.Debug.WriteLine($"ABP Login Success Response: {responseJson}");

                // ABP login success - now get the session info
                var sessionResponse = await _httpClient.GetAsync($"{_baseUrl}/api/abp/application-configuration");
                if (sessionResponse.IsSuccessStatusCode)
                {
                    _authToken = "abp_session_authenticated";
                    _currentUser = new UserInfo
                    {
                        Username = username,
                        DisplayName = username,
                        Roles = new string[] { "User" }
                    };

                    return new LoginResult { Success = true, Message = "ログイン成功 (ABP)" };
                }
            }
            
            // Fallback to OAuth2 if ABP login fails
            return await TryOAuth2LoginAsync(username, password);
        }
        catch (HttpRequestException)
        {
            // Fallback to demo mode if backend is not available
            return await LoginDemoAsync(username, password);
        }
        catch (Exception ex)
        {
            return new LoginResult { Success = false, Message = $"ログインエラー: {ex.Message}" };
        }
    }

    private async Task<LoginResult> TryOAuth2LoginAsync(string username, string password)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"Attempting OAuth2 login for user: {username}");
            System.Diagnostics.Debug.WriteLine($"Token endpoint: {_baseUrl}/connect/token");
            
            // Use OAuth2 Token Endpoint with form data
            var formData = new List<KeyValuePair<string, string>>
            {
                new("grant_type", "password"),
                new("username", username),
                new("password", password),
                new("client_id", "TranscriptCleanerMvc_App"),
                new("scope", "openid profile email roles TranscriptCleanerMvc")
            };

            var content = new FormUrlEncodedContent(formData);
            var response = await _httpClient.PostAsync($"{_baseUrl}/connect/token", content);
            
            System.Diagnostics.Debug.WriteLine($"OAuth2 Response Status: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                System.Diagnostics.Debug.WriteLine($"OAuth2 Success Response: {responseJson}");
                
                try
                {
                    var tokenResponse = JsonSerializer.Deserialize<OAuth2TokenResponse>(responseJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (!string.IsNullOrEmpty(tokenResponse?.AccessToken))
                    {
                        _authToken = tokenResponse.AccessToken;
                        _currentUser = new UserInfo
                        {
                            Username = username,
                            DisplayName = username, // OAuth doesn't return user details directly
                            Roles = new string[0] // Will be retrieved separately if needed
                        };

                        // Set authorization header for future requests
                        _httpClient.DefaultRequestHeaders.Authorization = 
                            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _authToken);

                        return new LoginResult { Success = true, Message = "ログイン成功" };
                    }
                    else
                    {
                        return new LoginResult { Success = false, Message = $"アクセストークンが取得できませんでした。Response: {responseJson}" };
                    }
                }
                catch (JsonException ex)
                {
                    return new LoginResult { Success = false, Message = $"JSON解析エラー: {ex.Message}. Response: {responseJson}" };
                }
            }
            else
            {
                // Try to get error details from response
                try
                {
                    var errorJson = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"OAuth2 Error Response: {errorJson}");
                    
                    var errorResponse = JsonSerializer.Deserialize<OAuth2TokenResponse>(errorJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (!string.IsNullOrEmpty(errorResponse?.ErrorDescription))
                    {
                        return new LoginResult { Success = false, Message = $"認証エラー: {errorResponse.ErrorDescription}" };
                    }
                    else if (!string.IsNullOrEmpty(errorResponse?.Error))
                    {
                        return new LoginResult { Success = false, Message = $"OAuth2エラー: {errorResponse.Error}" };
                    }
                    
                    // Return raw error response for debugging
                    return new LoginResult { Success = false, Message = $"認証失敗 ({response.StatusCode}): {errorJson}" };
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error parsing OAuth2 response: {ex.Message}");
                }

                return new LoginResult { Success = false, Message = $"HTTP Error: {response.StatusCode}" };
            }
        }
        catch (HttpRequestException)
        {
            // Fallback to demo mode if backend is not available
            return await LoginDemoAsync(username, password);
        }
        catch (Exception ex)
        {
            return new LoginResult { Success = false, Message = $"Error during login: {ex.Message}" };
        }
    }

    private async Task<LoginResult> LoginDemoAsync(string username, string password)
    {
        // Demo credentials for offline mode
        await Task.Delay(1000); // Simulate network delay

        var demoUsers = new[]
        {
            new { Username = "admin", Password = "1q2w3E*", DisplayName = "Administrator", Roles = new[] { "Admin" } },
            new { Username = "testuser", Password = "test123", DisplayName = "Test User", Roles = new[] { "User" } },
            new { Username = "demo", Password = "demo123", DisplayName = "Demo User", Roles = new[] { "Guest" } }
        };

        var user = Array.Find(demoUsers, u => 
            u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) && 
            u.Password == password);

        if (user != null)
        {
            _authToken = $"demo_token_{Guid.NewGuid():N}";
            _currentUser = new UserInfo
            {
                Username = user.Username,
                DisplayName = user.DisplayName,
                Roles = user.Roles
            };

            return new LoginResult 
            { 
                Success = true, 
                Message = $"Login successful (Demo Mode)\nWelcome, {user.DisplayName}!" 
            };
        }

        return new LoginResult { Success = false, Message = "Invalid username or password" };
    }

    public async Task<bool> LogoutAsync()
    {
        try
        {
            if (!string.IsNullOrEmpty(_authToken))
            {
                // Try to logout from server
                await _httpClient.PostAsync($"{_baseUrl}/api/TokenAuth/Logout", null);
            }
        }
        catch
        {
            // Ignore errors during logout
        }
        finally
        {
            // Clear local session
            _authToken = null;
            _currentUser = null;
            _httpClient.DefaultRequestHeaders.Authorization = null;
        }

        return true;
    }

    public async Task<bool> ValidateTokenAsync()
    {
        if (string.IsNullOrEmpty(_authToken))
            return false;

        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/services/app/Session/GetCurrentLoginInformations");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    public string GetAuthorizationHeader()
    {
        return !string.IsNullOrEmpty(_authToken) ? $"Bearer {_authToken}" : string.Empty;
    }
}

public class AbpLoginRequest
{
    public string UserNameOrEmailAddress { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public bool RememberMe { get; set; }
}

public class LoginResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
}

public class OAuth2TokenResponse
{
    [JsonPropertyName("access_token")]
    public string? AccessToken { get; set; }
    
    [JsonPropertyName("token_type")]
    public string? TokenType { get; set; }
    
    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }
    
    [JsonPropertyName("refresh_token")]
    public string? RefreshToken { get; set; }
    
    [JsonPropertyName("scope")]
    public string? Scope { get; set; }
    
    [JsonPropertyName("error")]
    public string? Error { get; set; }
    
    [JsonPropertyName("error_description")]
    public string? ErrorDescription { get; set; }
}

public class UserInfo
{
    public string Username { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string[] Roles { get; set; } = new string[0];
    
    public bool IsInRole(string role)
    {
        return Roles.Contains(role, StringComparer.OrdinalIgnoreCase);
    }
    
    public bool IsAdmin => IsInRole("Admin");
}