/* TranscriptCorrection 新UIスタイル */

.transcript-container {
    display: flex;
    height: calc(100vh - 120px);
    background: #f8f9fa;
    position: relative;
}

/* リサイザブルサイドバー */
.sidebar {
    width: 280px;
    min-width: 200px;
    max-width: 500px;
    background: white;
    border-right: 1px solid #dee2e6;
    padding: 20px;
    overflow-y: auto;
    flex-shrink: 0;
}

.sidebar h5 {
    color: #4472c4;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    font-size: 18px;
}

.sidebar h5 i {
    margin-right: 8px;
    font-size: 16px;
}

/* リサイザー */
.resizer {
    width: 4px;
    background: #dee2e6;
    cursor: col-resize;
    position: relative;
    flex-shrink: 0;
    transition: background-color 0.2s;
}

.resizer:hover {
    background: #4472c4;
}

.resizer::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 3px;
    height: 40px;
    background: rgba(68, 114, 196, 0.3);
    transform: translate(-50%, -50%);
    border-radius: 2px;
}

/* フォーム要素 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.form-select, .form-control {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.3s, box-shadow 0.3s;
    width: 100%;
}

.form-select:focus, .form-control:focus {
    border-color: #4472c4;
    box-shadow: 0 0 0 2px rgba(68, 114, 196, 0.2);
    outline: none;
}

/* ファイルアップロード */
.file-section {
    margin-bottom: 20px;
}

.file-section h6 {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
    display: flex;
    align-items: center;
}

.file-section h6 i {
    margin-right: 6px;
    font-size: 14px;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background: #fafbfc;
    transition: all 0.3s;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80px;
}

.upload-area:hover {
    border-color: #4472c4;
    background: #f0f4ff;
}

.upload-area.csv-upload:hover {
    border-color: #28a745;
    background: #f0fff4;
}

.upload-area i {
    font-size: 24px;
    color: #4472c4;
    margin-bottom: 8px;
}

.upload-area.csv-upload i {
    color: #28a745;
}

.upload-area p {
    margin: 0;
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

/* 誤字脱字一覧 */
.word-list-section {
    margin-top: 20px;
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
}

.word-list-section h6 {
    color: #4472c4;
    font-weight: 600;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    font-size: 14px;
}

.word-list-section h6 i {
    margin-right: 8px;
}

.word-list-controls {
    margin-bottom: 15px;
    display: flex;
    gap: 8px;
}

.word-list-container {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
    background: white;
    max-height: 300px;
    overflow-y: auto;
}

.word-list-header {
    display: grid;
    grid-template-columns: 1fr 1fr 80px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #333;
    font-size: 12px;
}

.word-list-col {
    padding: 12px;
    border-right: 1px solid #dee2e6;
    display: flex;
    align-items: center;
}

.word-list-col:last-child {
    border-right: none;
    justify-content: center;
}

.word-list-row {
    display: grid;
    grid-template-columns: 1fr 1fr 80px;
    border-bottom: 1px solid #f0f0f0;
}

.word-list-row:last-child {
    border-bottom: none;
}

.word-list-row:hover {
    background: #f8f9fa;
}

.word-list-row .word-list-col {
    padding: 8px;
}

.word-list-row .word-list-col input {
    border: none;
    background: transparent;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
    width: 100%;
    font-size: 13px;
}

.word-list-row .word-list-col input:focus {
    background: white;
    border: 1px solid #4472c4;
    box-shadow: 0 0 0 1px rgba(68, 114, 196, 0.2);
    outline: none;
}

/* 実行ボタン */
.btn-execute {
    background: #4472c4;
    color: white;
    border: none;
    padding: 15px 0;
    border-radius: 6px;
    font-weight: 600;
    font-size: 16px;
    width: 100%;
    margin-top: 20px;
    transition: background-color 0.3s;
    cursor: pointer;
}

.btn-execute:hover {
    background: #3c5aa6;
}

.btn-execute:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* メインコンテンツ */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    overflow: hidden;
}

/* ヘッダー */
.content-header {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    min-height: 60px;
}

.header-left, .header-right {
    flex: 1;
    padding: 15px 20px;
    display: flex;
    align-items: center;
}

.header-left {
    border-right: 1px solid #dee2e6;
}

.content-header h4 {
    margin: 0;
    color: #333;
    font-weight: 600;
    font-size: 16px;
}

/* 読み込み中表示 */
.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #666;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: none;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4472c4;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 2カラム比較表示 */
.comparison-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.comparison-left, .comparison-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.comparison-left {
    border-right: 1px solid #dee2e6;
}

/* パネルヘッダー */
.comparison-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

.panel-actions {
    display: flex;
    gap: 4px;
}

.btn-panel-action {
    background: none;
    border: none;
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 12px;
}

.btn-panel-action:hover {
    background: #e9ecef;
    color: #495057;
}

/* 比較パネル */
.comparison-panel {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    line-height: 1.6;
    white-space: pre-wrap;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    background: #ffffff;
    border: none;
    outline: none;
    resize: none;
}

.comparison-panel.readonly {
    background: #f8f9fa;
    color: #495057;
}

.comparison-panel.editable {
    background: #ffffff;
    color: #212529;
}

.comparison-panel:focus {
    box-shadow: inset 0 0 0 2px rgba(68, 114, 196, 0.2);
}

.comparison-panel.has-content {
    color: #212529;
}

/* プレースホルダーテキスト */
.placeholder-text {
    color: #6c757d;
    font-style: italic;
    line-height: 1.8;
    text-align: center;
    margin-top: 40px;
}

.comparison-panel.has-content .placeholder-text {
    display: none;
}

/* 操作バー */
.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.action-left, .action-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 差分トグル */
.diff-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #495057;
}

.diff-toggle input[type="checkbox"] {
    margin-right: 8px;
}

/* アクションボタン */
.btn-action {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-action i {
    font-size: 12px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-primary {
    background: #4472c4;
    color: white;
}

.btn-primary:hover {
    background: #3c5aa6;
}

/* 小さなボタン */
.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
}

.btn-primary.btn-sm {
    background: #4472c4;
    color: white;
    border: none;
}

.btn-success.btn-sm {
    background: #28a745;
    color: white;
    border: none;
}

.btn-danger.btn-sm {
    background: #dc3545;
    color: white;
    border: none;
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
    .transcript-container {
        flex-direction: column;
        height: auto;
    }
    
    .sidebar {
        width: 100%;
        max-width: none;
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .resizer {
        display: none;
    }
    
    .comparison-content {
        flex-direction: column;
    }
    
    .comparison-left {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .content-header {
        flex-direction: column;
    }
    
    .header-left {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .action-bar {
        flex-direction: column;
        gap: 12px;
    }
    
    .action-left, .action-right {
        width: 100%;
        justify-content: center;
    }
}

/* ダークモード対応（将来用） */
@media (prefers-color-scheme: dark) {
    .transcript-container {
        background: #1e1e1e;
    }
    
    .sidebar {
        background: #252525;
        border-right-color: #404040;
    }
    
    .main-content {
        background: #252525;
    }
    
    .content-header {
        background: #2d2d2d;
        border-bottom-color: #404040;
    }
    
    .content-header h4 {
        color: #ffffff;
    }
    
    .comparison-panel {
        background: #1e1e1e;
        color: #ffffff;
    }
    
    .comparison-panel.readonly {
        background: #2d2d2d;
        color: #cccccc;
    }
}

/* スクロールバーのカスタマイズ */
.sidebar::-webkit-scrollbar,
.comparison-panel::-webkit-scrollbar,
.word-list-container::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track,
.comparison-panel::-webkit-scrollbar-track,
.word-list-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb,
.comparison-panel::-webkit-scrollbar-thumb,
.word-list-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.comparison-panel::-webkit-scrollbar-thumb:hover,
.word-list-container::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}