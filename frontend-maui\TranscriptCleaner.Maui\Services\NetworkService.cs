using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace TranscriptCleaner.Maui.Services;

/// <summary>
/// ネットワークサービス実装
/// </summary>
public class NetworkService : INetworkService, IDisposable
{
    private readonly ILogger<NetworkService> _logger;
    private readonly HttpClient _httpClient;
    private readonly string _baseUrl = "https://localhost:44396";
    private bool _isConnected;
    private bool _disposed;

    public bool IsConnected => _isConnected;

    public event EventHandler<NetworkStatusChangedEventArgs>? NetworkStatusChanged;

    public NetworkService(ILogger<NetworkService> logger)
    {
        _logger = logger;
        
        // HTTPS証明書検証をスキップ（開発環境用）
        var handler = new HttpClientHandler();
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
        
        _httpClient = new HttpClient(handler);
        _httpClient.Timeout = TimeSpan.FromSeconds(10);

        // 初期状態を確認
        _ = Task.Run(async () =>
        {
            _isConnected = await CheckConnectivityAsync();
        });

        // ネットワーク状態変更の監視を開始
        Connectivity.Current.ConnectivityChanged += OnConnectivityChanged;
    }

    /// <summary>
    /// ネットワーク接続を確認
    /// </summary>
    public async Task<bool> CheckConnectivityAsync()
    {
        try
        {
            var networkAccess = Connectivity.Current.NetworkAccess;
            var isNetworkConnected = networkAccess == NetworkAccess.Internet;

            if (isNetworkConnected)
            {
                // サーバー接続も確認
                var isServerConnected = await CheckServerConnectivityAsync();
                UpdateConnectionStatus(isServerConnected);
                return isServerConnected;
            }
            else
            {
                UpdateConnectionStatus(false);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ネットワーク接続確認中にエラーが発生しました");
            UpdateConnectionStatus(false);
            return false;
        }
    }

    /// <summary>
    /// サーバー接続を確認
    /// </summary>
    public async Task<bool> CheckServerConnectivityAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/health-status");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "サーバー接続確認に失敗しました");
            return false;
        }
    }

    /// <summary>
    /// 接続状態を更新
    /// </summary>
    private void UpdateConnectionStatus(bool isConnected)
    {
        if (_isConnected != isConnected)
        {
            _isConnected = isConnected;
            var connectionType = GetConnectionType();
            
            _logger.LogInformation("ネットワーク状態が変更されました: {IsConnected} ({ConnectionType})", 
                isConnected, connectionType);

            NetworkStatusChanged?.Invoke(this, new NetworkStatusChangedEventArgs(isConnected, connectionType));
        }
    }

    /// <summary>
    /// 接続タイプを取得
    /// </summary>
    private string GetConnectionType()
    {
        try
        {
            var profiles = Connectivity.Current.ConnectionProfiles;
            if (profiles.Contains(ConnectionProfile.WiFi))
                return "WiFi";
            if (profiles.Contains(ConnectionProfile.Cellular))
                return "Cellular";
            if (profiles.Contains(ConnectionProfile.Ethernet))
                return "Ethernet";
            
            return "Unknown";
        }
        catch
        {
            return "Unknown";
        }
    }

    /// <summary>
    /// ネットワーク状態変更イベントハンドラー
    /// </summary>
    private async void OnConnectivityChanged(object? sender, ConnectivityChangedEventArgs e)
    {
        await CheckConnectivityAsync();
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            Connectivity.Current.ConnectivityChanged -= OnConnectivityChanged;
            _httpClient?.Dispose();
            _disposed = true;
        }
    }
}