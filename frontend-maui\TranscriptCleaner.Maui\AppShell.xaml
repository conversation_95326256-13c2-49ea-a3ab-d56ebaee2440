<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="TranscriptCleaner.Maui.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:TranscriptCleaner.Maui"
    Title="TranscriptCleaner.Maui">

    <ShellContent
        Title="ログイン"
        ContentTemplate="{DataTemplate local:LoginPage}"
        Route="LoginPage"
        Icon="{FontImageSource FontFamily=MaterialDesignIcons, Glyph=&#xF0C5;}" />
        
    <ShellContent
        Title="議事録訂正"
        ContentTemplate="{DataTemplate local:MainPage}"
        Route="MainPage"
        Icon="{FontImageSource FontFamily=MaterialDesignIcons, Glyph=&#xF0C5;}" />

</Shell>
