using System;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// Google認証設定オプション
    /// </summary>
    public class GoogleAuthOptions
    {
        /// <summary>
        /// 設定セクション名
        /// </summary>
        public const string SectionName = "Authentication:Google";

        /// <summary>
        /// Google OAuth クライアントID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Google OAuth クライアントシークレット
        /// </summary>
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// Google認証が有効かどうか
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 要求するスコープ
        /// </summary>
        public string[] Scopes { get; set; } = { "openid", "profile", "email" };

        /// <summary>
        /// コールバックパス
        /// </summary>
        public string CallbackPath { get; set; } = "/signin-google";

        /// <summary>
        /// トークンを保存するかどうか
        /// </summary>
        public bool SaveTokens { get; set; } = true;

        /// <summary>
        /// 設定が有効かどうかを検証
        /// </summary>
        public bool IsValid()
        {
            return Enabled && 
                   !string.IsNullOrEmpty(ClientId) && 
                   !string.IsNullOrEmpty(ClientSecret);
        }

        /// <summary>
        /// 環境変数から設定を読み込み
        /// </summary>
        public void LoadFromEnvironment()
        {
            var envClientId = Environment.GetEnvironmentVariable("GOOGLE_OAUTH_CLIENT_ID");
            var envClientSecret = Environment.GetEnvironmentVariable("GOOGLE_OAUTH_CLIENT_SECRET");

            if (!string.IsNullOrEmpty(envClientId))
            {
                ClientId = envClientId;
            }

            if (!string.IsNullOrEmpty(envClientSecret))
            {
                ClientSecret = envClientSecret;
            }
        }
    }
}