using System;
using System.Threading.Tasks;

namespace TranscriptCleaner.WinUI.Services
{
    /// <summary>
    /// 認証状態同期サービスインターフェース
    /// </summary>
    public interface IAuthSyncService
    {
        /// <summary>
        /// サーバー接続確認
        /// </summary>
        /// <returns>接続状況</returns>
        Task<ServerConnectionStatus> CheckServerConnectionAsync();

        /// <summary>
        /// 複数クライアント間の認証状態同期
        /// </summary>
        /// <returns>同期結果</returns>
        Task<AuthSyncResult> SyncAuthStateAsync();

        /// <summary>
        /// 一貫した認証体験を提供
        /// </summary>
        /// <returns>体験提供結果</returns>
        Task<bool> EnsureConsistentAuthExperienceAsync();

        /// <summary>
        /// 認証状態の変更を他のクライアントに通知
        /// </summary>
        /// <param name="authState">認証状態</param>
        /// <returns>通知結果</returns>
        Task<bool> NotifyAuthStateChangeAsync(AuthenticationState authState);

        /// <summary>
        /// 他のクライアントからの認証状態変更を受信
        /// </summary>
        /// <returns>受信処理</returns>
        Task StartListeningForAuthStateChangesAsync();

        /// <summary>
        /// 認証状態変更の受信を停止
        /// </summary>
        /// <returns>停止処理</returns>
        Task StopListeningForAuthStateChangesAsync();

        /// <summary>
        /// 現在のクライアント情報を登録
        /// </summary>
        /// <returns>登録結果</returns>
        Task<bool> RegisterClientAsync();

        /// <summary>
        /// クライアント情報を登録解除
        /// </summary>
        /// <returns>解除結果</returns>
        Task<bool> UnregisterClientAsync();

        /// <summary>
        /// アクティブなクライアント一覧を取得
        /// </summary>
        /// <returns>クライアント一覧</returns>
        Task<ClientInfo[]> GetActiveClientsAsync();

        /// <summary>
        /// 認証状態変更イベント
        /// </summary>
        event EventHandler<AuthStateChangedEventArgs> AuthStateChanged;
    }

    /// <summary>
    /// サーバー接続状況
    /// </summary>
    public enum ServerConnectionStatus
    {
        /// <summary>
        /// 接続済み
        /// </summary>
        Connected,

        /// <summary>
        /// 切断
        /// </summary>
        Disconnected,

        /// <summary>
        /// 接続中
        /// </summary>
        Connecting,

        /// <summary>
        /// エラー
        /// </summary>
        Error,

        /// <summary>
        /// タイムアウト
        /// </summary>
        Timeout
    }

    /// <summary>
    /// 認証同期結果
    /// </summary>
    public class AuthSyncResult
    {
        /// <summary>
        /// 成功フラグ
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 同期されたクライアント数
        /// </summary>
        public int SyncedClientCount { get; set; }

        /// <summary>
        /// 同期に失敗したクライアント数
        /// </summary>
        public int FailedClientCount { get; set; }

        /// <summary>
        /// 同期時刻
        /// </summary>
        public DateTime SyncTime { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 詳細情報
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// 成功結果を作成
        /// </summary>
        public static AuthSyncResult Success(int syncedCount, int failedCount = 0)
        {
            return new AuthSyncResult
            {
                IsSuccess = true,
                SyncedClientCount = syncedCount,
                FailedClientCount = failedCount,
                SyncTime = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 失敗結果を作成
        /// </summary>
        public static AuthSyncResult Failure(string errorMessage)
        {
            return new AuthSyncResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                SyncTime = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// 認証状態
    /// </summary>
    public enum AuthenticationState
    {
        /// <summary>
        /// 未認証
        /// </summary>
        NotAuthenticated,

        /// <summary>
        /// 認証済み
        /// </summary>
        Authenticated,

        /// <summary>
        /// トークン期限切れ
        /// </summary>
        TokenExpired,

        /// <summary>
        /// ログアウト
        /// </summary>
        LoggedOut,

        /// <summary>
        /// エラー
        /// </summary>
        Error
    }

    /// <summary>
    /// クライアント情報
    /// </summary>
    public class ClientInfo
    {
        /// <summary>
        /// クライアントID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// クライアントタイプ
        /// </summary>
        public ClientType Type { get; set; }

        /// <summary>
        /// プラットフォーム
        /// </summary>
        public string Platform { get; set; } = string.Empty;

        /// <summary>
        /// バージョン
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 最後のアクティビティ時刻
        /// </summary>
        public DateTime LastActivity { get; set; }

        /// <summary>
        /// 認証状態
        /// </summary>
        public AuthenticationState AuthState { get; set; }

        /// <summary>
        /// ユーザーID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// デバイス名
        /// </summary>
        public string? DeviceName { get; set; }

        /// <summary>
        /// IPアドレス
        /// </summary>
        public string? IpAddress { get; set; }
    }

    /// <summary>
    /// クライアントタイプ
    /// </summary>
    public enum ClientType
    {
        /// <summary>
        /// Webクライアント
        /// </summary>
        Web,

        /// <summary>
        /// MAUIクライアント
        /// </summary>
        Maui,

        /// <summary>
        /// WinUIクライアント
        /// </summary>
        WinUI,

        /// <summary>
        /// モバイルアプリ
        /// </summary>
        Mobile,

        /// <summary>
        /// デスクトップアプリ
        /// </summary>
        Desktop,

        /// <summary>
        /// 不明
        /// </summary>
        Unknown
    }


}