using Microsoft.EntityFrameworkCore;
using TranscriptCleanerMvc.Entities;
using TranscriptCleanerMvc.Identity;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.TenantManagement;
using Volo.Abp.TenantManagement.EntityFrameworkCore;

namespace TranscriptCleanerMvc.EntityFrameworkCore;

[ReplaceDbContext(typeof(IIdentityDbContext))]
[ReplaceDbContext(typeof(ITenantManagementDbContext))]
[ConnectionStringName("Default")]
public class TranscriptCleanerMvcDbContext :
    AbpDbContext<TranscriptCleanerMvcDbContext>,
    ITenantManagementDbContext,
    IIdentityDbContext
{
    /* Add DbSet properties for your Aggregate Roots / Entities here. */
    
    public DbSet<Transcript> Transcripts { get; set; }
    public DbSet<CorrectionHistory> CorrectionHistories { get; set; }
    public DbSet<WordList> WordLists { get; set; }
    
    // ソーシャルアカウント連携用エンティティ
    public DbSet<AppUser> AppUsers { get; set; }
    public DbSet<ExternalLoginLog> ExternalLoginLogs { get; set; }


    #region Entities from the modules

    /* Notice: We only implemented IIdentityProDbContext and ISaasDbContext
     * and replaced them for this DbContext. This allows you to perform JOIN
     * queries for the entities of these modules over the repositories easily. You
     * typically don't need that for other modules. But, if you need, you can
     * implement the DbContext interface of the needed module and use ReplaceDbContext
     * attribute just like IIdentityProDbContext and ISaasDbContext.
     *
     * More info: Replacing a DbContext of a module ensures that the related module
     * uses this DbContext on runtime. Otherwise, it will use its own DbContext class.
     */

    // Identity
    public DbSet<IdentityUser> Users { get; set; }
    public DbSet<IdentityRole> Roles { get; set; }
    public DbSet<IdentityClaimType> ClaimTypes { get; set; }
    public DbSet<OrganizationUnit> OrganizationUnits { get; set; }
    public DbSet<IdentitySecurityLog> SecurityLogs { get; set; }
    public DbSet<IdentityLinkUser> LinkUsers { get; set; }
    public DbSet<IdentityUserDelegation> UserDelegations { get; set; }
    public DbSet<IdentitySession> Sessions { get; set; }

    // Tenant Management
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<TenantConnectionString> TenantConnectionStrings { get; set; }

    #endregion

    public TranscriptCleanerMvcDbContext(DbContextOptions<TranscriptCleanerMvcDbContext> options)
        : base(options)
    {

    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        /* Include modules to your migration db context */

        builder.ConfigurePermissionManagement();
        builder.ConfigureSettingManagement();
        builder.ConfigureBackgroundJobs();
        builder.ConfigureAuditLogging();
        builder.ConfigureFeatureManagement();
        builder.ConfigureIdentity();
        builder.ConfigureOpenIddict();
        builder.ConfigureTenantManagement();
        builder.ConfigureBlobStoring();
        
        // カスタムユーザーエンティティの設定
        builder.Entity<AppUser>(b =>
        {
            b.ToTable(TranscriptCleanerMvcConsts.DbTablePrefix + "AppUsers", TranscriptCleanerMvcConsts.DbSchema);
            b.ConfigureByConvention();
            
            // ソーシャルアカウント連携用プロパティ
            b.Property(x => x.GoogleId).HasMaxLength(256);
            b.Property(x => x.GoogleEmail).HasMaxLength(256);
            b.Property(x => x.ProfileImageUrl).HasMaxLength(512);
            
            // インデックス
            b.HasIndex(x => x.GoogleId);
            b.HasIndex(x => x.GoogleEmail);
            b.HasIndex(x => x.IsGoogleLinked);
        });
        
        // 外部ログイン履歴エンティティの設定
        builder.Entity<ExternalLoginLog>(b =>
        {
            b.ToTable(TranscriptCleanerMvcConsts.DbTablePrefix + "ExternalLoginLogs", TranscriptCleanerMvcConsts.DbSchema);
            b.ConfigureByConvention();
            
            b.Property(x => x.Provider).IsRequired().HasMaxLength(50);
            b.Property(x => x.ProviderKey).IsRequired().HasMaxLength(256);
            b.Property(x => x.IpAddress).HasMaxLength(45);
            b.Property(x => x.UserAgent).HasMaxLength(512);
            b.Property(x => x.ErrorMessage).HasMaxLength(1024);
            
            b.HasIndex(x => x.UserId);
            b.HasIndex(x => x.Provider);
            b.HasIndex(x => x.LoginTime);
            b.HasIndex(x => x.Success);
        });
        
        /* Configure your own tables/entities inside here */

        builder.Entity<Transcript>(b =>
        {
            b.ToTable(TranscriptCleanerMvcConsts.DbTablePrefix + "Transcripts", TranscriptCleanerMvcConsts.DbSchema);
            b.ConfigureByConvention();
            
            b.Property(x => x.Title).IsRequired().HasMaxLength(500);
            b.Property(x => x.OriginalText).IsRequired();
            b.Property(x => x.Language).IsRequired().HasMaxLength(10);
            b.Property(x => x.CorrectionType).IsRequired().HasMaxLength(50);
            b.Property(x => x.ErrorMessage).HasMaxLength(1000);
            b.Property(x => x.ProcessingCost).HasColumnType("decimal(18,6)");
            
            b.HasIndex(x => x.CreatorId);
            b.HasIndex(x => x.Status);
            b.HasIndex(x => x.Language);
            b.HasIndex(x => x.CreationTime);
        });

        builder.Entity<CorrectionHistory>(b =>
        {
            b.ToTable(TranscriptCleanerMvcConsts.DbTablePrefix + "CorrectionHistories", TranscriptCleanerMvcConsts.DbSchema);
            b.ConfigureByConvention();
            
            // プロパティ設定
            b.Property(x => x.TranscriptId).IsRequired();
            b.Property(x => x.BeforeText).IsRequired();
            b.Property(x => x.AfterText).IsRequired();
            b.Property(x => x.CorrectionType).IsRequired().HasMaxLength(100);
            b.Property(x => x.Description).HasMaxLength(500);
            b.Property(x => x.Cost).HasColumnType("decimal(18,6)");
            
            // インデックス設定
            b.HasIndex(x => x.TranscriptId);
            b.HasIndex(x => x.CreationTime);
        });

        builder.Entity<WordList>(b =>
        {
            b.ToTable(TranscriptCleanerMvcConsts.DbTablePrefix + "WordLists", TranscriptCleanerMvcConsts.DbSchema);
            b.ConfigureByConvention();
            
            b.Property(x => x.IncorrectWord).IsRequired().HasMaxLength(200);
            b.Property(x => x.CorrectWord).IsRequired().HasMaxLength(200);
            b.Property(x => x.Language).IsRequired().HasMaxLength(10);
            b.Property(x => x.Category).IsRequired().HasMaxLength(50);
            b.Property(x => x.Description).HasMaxLength(500);
            
            b.HasIndex(x => new { x.IncorrectWord, x.Language }).IsUnique();
            b.HasIndex(x => x.Language);
            b.HasIndex(x => x.Category);
            b.HasIndex(x => x.IsActive);
            b.HasIndex(x => x.UsageCount);
        });
    }
}
