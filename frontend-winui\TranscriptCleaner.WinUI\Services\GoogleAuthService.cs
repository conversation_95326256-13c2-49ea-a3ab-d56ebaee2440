using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Extensions.Logging;
using Microsoft.Web.WebView2.Core;
using Microsoft.UI.Xaml.Controls;
using Windows.Storage;

namespace TranscriptCleaner.WinUI.Services
{
    /// <summary>
    /// WinUI用Google認証サービス実装
    /// </summary>
    public class GoogleAuthService : IGoogleAuthService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<GoogleAuthService> _logger;
        private readonly string _baseUrl;
        private readonly string _googleClientId;
        private readonly string _redirectUri;

        // ローカル設定キー
        private const string ACCESS_TOKEN_KEY = "google_access_token";
        private const string REFRESH_TOKEN_KEY = "google_refresh_token";
        private const string TOKEN_EXPIRES_KEY = "google_token_expires";
        private const string USER_INFO_KEY = "google_user_info";

        public GoogleAuthService(HttpClient httpClient, ILogger<GoogleAuthService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            
            // 設定値（実際の実装では設定ファイルから読み込み）
            _baseUrl = "https://localhost:44396"; // ABP Framework API URL
            _googleClientId = "YOUR_GOOGLE_CLIENT_ID"; // Google Cloud Consoleから取得
            _redirectUri = "http://localhost:8080/auth/callback"; // ローカルコールバック
        }

        /// <summary>
        /// Googleアカウントでログイン
        /// </summary>
        public async Task<AuthResult> LoginWithGoogleAsync()
        {
            try
        {
                _logger.LogInformation("WinUI Google認証を開始します");

                // PKCE用のコードベリファイアとチャレンジを生成
                var codeVerifier = GenerateCodeVerifier();
                var codeChallenge = GenerateCodeChallenge(codeVerifier);
                var state = GenerateRandomString(32);

                // Google認証URLを構築
                var authUrl = BuildGoogleAuthUrl(codeChallenge, state);

                // WebView2を使用して認証を実行
                var authCode = await ShowWebView2AuthAsync(authUrl, state);
                if (string.IsNullOrEmpty(authCode))
                {
                    return AuthResult.Failure("認証がキャンセルされました");
                }

                // 認証コードをアクセストークンに交換
                var tokenResponse = await ExchangeCodeForTokenAsync(authCode, codeVerifier);
                if (tokenResponse == null)
                {
                    return AuthResult.Failure("トークン取得に失敗しました");
                }

                // ユーザー情報を取得
                var userInfo = await GetGoogleUserInfoAsync(tokenResponse.AccessToken);
                if (userInfo == null)
                {
                    return AuthResult.Failure("ユーザー情報の取得に失敗しました");
                }

                // トークンとユーザー情報をローカルストレージに保存
                await SaveTokensAsync(tokenResponse.AccessToken, tokenResponse.RefreshToken, 
                    DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn));
                await SaveUserInfoAsync(userInfo);

                _logger.LogInformation("WinUI Google認証が成功しました。ユーザー: {Email}", userInfo.Email);
                
                return AuthResult.Success(tokenResponse.AccessToken, userInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WinUI Google認証中にエラーが発生しました");
                return AuthResult.Failure($"認証エラー: {ex.Message}");
            }
        }

        /// <summary>
        /// Googleアカウントが連携されているかチェック
        /// </summary>
        public async Task<bool> IsGoogleLinkedAsync()
        {
            try
            {
                var localSettings = ApplicationData.Current.LocalSettings;
                var accessToken = localSettings.Values[ACCESS_TOKEN_KEY] as string;
                var userInfo = await GetStoredUserInfoAsync();
                
                return !string.IsNullOrEmpty(accessToken) && userInfo != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Google連携状況確認中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// 現在のユーザーにGoogleアカウントを連携
        /// </summary>
        public async Task<bool> LinkGoogleAccountAsync()
        {
            try
            {
                _logger.LogInformation("Googleアカウント連携を開始します");

                // 既存のアクセストークンを取得（ABP Framework認証用）
                var existingToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(existingToken))
                {
                    _logger.LogWarning("既存の認証トークンが見つかりません");
                    return false;
                }

                // Google認証を実行
                var authResult = await LoginWithGoogleAsync();
                if (!authResult.IsSuccess)
                {
                    return false;
                }

                // ABP Framework APIに連携リクエストを送信
                var linkRequest = new
                {
                    Provider = "Google",
                    ProviderKey = authResult.UserInfo?.Id
                };

                var json = JsonSerializer.Serialize(linkRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", existingToken);

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/external/google/link", content);
                
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Googleアカウント連携が成功しました");
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Googleアカウント連携に失敗しました。レスポンス: {Response}", errorContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Googleアカウント連携中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// ログアウト
        /// </summary>
        public async Task LogoutAsync()
        {
            try
            {
                _logger.LogInformation("ログアウトを実行します");
                await ClearAuthDataAsync();
                _logger.LogInformation("ログアウトが完了しました");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ログアウト中にエラーが発生しました");
            }
        }

        /// <summary>
        /// 現在の認証状態を取得
        /// </summary>
        public async Task<AuthState> GetAuthStateAsync()
        {
            try
            {
                var localSettings = ApplicationData.Current.LocalSettings;
                var accessToken = localSettings.Values[ACCESS_TOKEN_KEY] as string;
                
                if (string.IsNullOrEmpty(accessToken))
                {
                    return AuthState.NotAuthenticated;
                }

                var expiresString = localSettings.Values[TOKEN_EXPIRES_KEY] as string;
                if (DateTime.TryParse(expiresString, out var expires))
                {
                    if (DateTime.UtcNow >= expires)
                    {
                        return AuthState.TokenExpired;
                    }
                }

                return AuthState.Authenticated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証状態確認中にエラーが発生しました");
                return AuthState.Error;
            }
        }

        /// <summary>
        /// アクセストークンを取得
        /// </summary>
        public async Task<string?> GetAccessTokenAsync()
        {
            try
            {
                var authState = await GetAuthStateAsync();
                if (authState == AuthState.Authenticated)
                {
                    var localSettings = ApplicationData.Current.LocalSettings;
                    return localSettings.Values[ACCESS_TOKEN_KEY] as string;
                }
                else if (authState == AuthState.TokenExpired)
                {
                    // トークンリフレッシュを試行
                    var refreshed = await RefreshTokenAsync();
                    if (refreshed)
                    {
                        var localSettings = ApplicationData.Current.LocalSettings;
                        return localSettings.Values[ACCESS_TOKEN_KEY] as string;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "アクセストークン取得中にエラーが発生しました");
                return null;
            }
        }

        /// <summary>
        /// トークンをリフレッシュ
        /// </summary>
        public async Task<bool> RefreshTokenAsync()
        {
            try
            {
                var localSettings = ApplicationData.Current.LocalSettings;
                var refreshToken = localSettings.Values[REFRESH_TOKEN_KEY] as string;
                
                if (string.IsNullOrEmpty(refreshToken))
                {
                    _logger.LogWarning("リフレッシュトークンが見つかりません");
                    return false;
                }

                // TODO: 実際のトークンリフレッシュ実装
                // 現在はモック実装
                _logger.LogInformation("トークンリフレッシュを実行します（モック）");
                
                var newAccessToken = "new_mock_access_token";
                await SaveTokensAsync(newAccessToken, refreshToken, DateTime.UtcNow.AddHours(1));
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "トークンリフレッシュ中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// 認証情報をクリア
        /// </summary>
        public async Task ClearAuthDataAsync()
        {
            try
            {
                var localSettings = ApplicationData.Current.LocalSettings;
                localSettings.Values.Remove(ACCESS_TOKEN_KEY);
                localSettings.Values.Remove(REFRESH_TOKEN_KEY);
                localSettings.Values.Remove(TOKEN_EXPIRES_KEY);
                localSettings.Values.Remove(USER_INFO_KEY);
                
                _logger.LogInformation("認証情報をクリアしました");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証情報のクリア中にエラーが発生しました");
            }
        }

        /// <summary>
        /// WebView2を使用して認証を実行
        /// </summary>
        private async Task<string?> ShowWebView2AuthAsync(string authUrl, string expectedState)
        {
            var tcs = new TaskCompletionSource<string?>();
            
            var webView = new WebView2();
            await webView.EnsureCoreWebView2Async();

            webView.CoreWebView2.NavigationStarting += (sender, args) =>
            {
                if (args.Uri.StartsWith(_redirectUri))
                {
                    var uri = new Uri(args.Uri);
                    var query = HttpUtility.ParseQueryString(uri.Query);
                    
                    var state = query["state"];
                    var code = query["code"];
                    var error = query["error"];

                    if (!string.IsNullOrEmpty(error))
                    {
                        tcs.SetResult(null);
                        return;
                    }

                    if (state != expectedState)
                    {
                        tcs.SetResult(null);
                        return;
                    }

                    tcs.SetResult(code);
                    args.Cancel = true;
                }
            };

            webView.CoreWebView2.Navigate(authUrl);
            
            // TODO: WebView2をダイアログで表示する実装が必要
            // 現在はモック実装
            await Task.Delay(1000);
            return "mock_auth_code";
        }

        /// <summary>
        /// Google認証URLを構築
        /// </summary>
        private string BuildGoogleAuthUrl(string codeChallenge, string state)
        {
            var parameters = new Dictionary<string, string>
            {
                ["client_id"] = _googleClientId,
                ["redirect_uri"] = _redirectUri,
                ["response_type"] = "code",
                ["scope"] = "openid email profile",
                ["code_challenge"] = codeChallenge,
                ["code_challenge_method"] = "S256",
                ["state"] = state
            };

            var queryString = string.Join("&", parameters.Select(kvp => 
                $"{HttpUtility.UrlEncode(kvp.Key)}={HttpUtility.UrlEncode(kvp.Value)}"));

            return $"https://accounts.google.com/o/oauth2/v2/auth?{queryString}";
        }

        /// <summary>
        /// 認証コードをアクセストークンに交換
        /// </summary>
        private async Task<TokenResponse?> ExchangeCodeForTokenAsync(string code, string codeVerifier)
        {
            try
            {
                // TODO: 実際のトークン交換実装
                // 現在はモック実装
                await Task.Delay(500);
                
                return new TokenResponse
                {
                    AccessToken = "mock_access_token",
                    RefreshToken = "mock_refresh_token",
                    ExpiresIn = 3600
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "トークン交換中にエラーが発生しました");
                return null;
            }
        }

        /// <summary>
        /// Googleユーザー情報を取得
        /// </summary>
        private async Task<GoogleUserInfo?> GetGoogleUserInfoAsync(string accessToken)
        {
            try
            {
                // TODO: 実際のGoogle People API呼び出し
                // 現在はモック実装
                await Task.Delay(500);
                
                return new GoogleUserInfo
                {
                    Id = "mock_google_id_" + DateTime.UtcNow.Ticks,
                    Email = "<EMAIL>",
                    Name = "Test User",
                    GivenName = "Test",
                    FamilyName = "User",
                    Picture = "https://example.com/avatar.jpg",
                    EmailVerified = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Googleユーザー情報取得中にエラーが発生しました");
                return null;
            }
        }

        /// <summary>
        /// トークンをローカルストレージに保存
        /// </summary>
        private async Task SaveTokensAsync(string accessToken, string? refreshToken, DateTime expiresAt)
        {
            var localSettings = ApplicationData.Current.LocalSettings;
            localSettings.Values[ACCESS_TOKEN_KEY] = accessToken;
            
            if (!string.IsNullOrEmpty(refreshToken))
            {
                localSettings.Values[REFRESH_TOKEN_KEY] = refreshToken;
            }
            
            localSettings.Values[TOKEN_EXPIRES_KEY] = expiresAt.ToString("O");
        }

        /// <summary>
        /// ユーザー情報をローカルストレージに保存
        /// </summary>
        private async Task SaveUserInfoAsync(GoogleUserInfo userInfo)
        {
            var json = JsonSerializer.Serialize(userInfo);
            var localSettings = ApplicationData.Current.LocalSettings;
            localSettings.Values[USER_INFO_KEY] = json;
        }

        /// <summary>
        /// 保存されたユーザー情報を取得
        /// </summary>
        private async Task<GoogleUserInfo?> GetStoredUserInfoAsync()
        {
            try
            {
                var localSettings = ApplicationData.Current.LocalSettings;
                var json = localSettings.Values[USER_INFO_KEY] as string;
                
                if (string.IsNullOrEmpty(json))
                {
                    return null;
                }

                return JsonSerializer.Deserialize<GoogleUserInfo>(json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存されたユーザー情報の取得中にエラーが発生しました");
                return null;
            }
        }

        /// <summary>
        /// PKCEコードベリファイアを生成
        /// </summary>
        private string GenerateCodeVerifier()
        {
            var bytes = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(bytes);
            }
            return Convert.ToBase64String(bytes)
                .TrimEnd('=')
                .Replace('+', '-')
                .Replace('/', '_');
        }

        /// <summary>
        /// PKCEコードチャレンジを生成
        /// </summary>
        private string GenerateCodeChallenge(string codeVerifier)
        {
            using (var sha256 = SHA256.Create())
            {
                var challengeBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(codeVerifier));
                return Convert.ToBase64String(challengeBytes)
                    .TrimEnd('=')
                    .Replace('+', '-')
                    .Replace('/', '_');
            }
        }

        /// <summary>
        /// ランダム文字列を生成
        /// </summary>
        private string GenerateRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }

    /// <summary>
    /// トークンレスポンス
    /// </summary>
    public class TokenResponse
    {
        public string AccessToken { get; set; } = string.Empty;
        public string? RefreshToken { get; set; }
        public int ExpiresIn { get; set; }
    }
}