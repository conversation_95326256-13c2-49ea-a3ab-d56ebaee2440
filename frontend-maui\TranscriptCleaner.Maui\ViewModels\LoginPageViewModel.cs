using System.ComponentModel;
using System.Runtime.CompilerServices;
using TranscriptCleaner.Maui.Services;

namespace TranscriptCleaner.Maui.ViewModels
{
    public class LoginPageViewModel : INotifyPropertyChanged
    {
        private readonly AuthenticationService _authService;
        private readonly IGoogleAuthService _googleAuthService;
        private readonly IOfflineAuthService _offlineAuthService;
        private readonly INetworkService _networkService;
        private string _username = string.Empty;
        private string _password = string.Empty;
        private bool _isProcessing = false;
        private string _statusMessage = string.Empty;

        public LoginPageViewModel(
            AuthenticationService authService,
            IGoogleAuthService googleAuthService,
            IOfflineAuthService offlineAuthService,
            INetworkService networkService)
        {
            _authService = authService;
            _googleAuthService = googleAuthService;
            _offlineAuthService = offlineAuthService;
            _networkService = networkService;
        }

        public string Username
        {
            get => _username;
            set
            {
                if (_username != value)
                {
                    _username = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CanLogin));
                }
            }
        }

        public string Password
        {
            get => _password;
            set
            {
                if (_password != value)
                {
                    _password = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CanLogin));
                }
            }
        }

        public bool IsProcessing
        {
            get => _isProcessing;
            set
            {
                if (_isProcessing != value)
                {
                    _isProcessing = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CanLogin));
                }
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool CanLogin => !IsProcessing && 
                               !string.IsNullOrWhiteSpace(Username) && 
                               !string.IsNullOrWhiteSpace(Password);

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void SetProcessing(bool processing, string message = "")
        {
            IsProcessing = processing;
            StatusMessage = message;
        }

        public async Task<LoginResult> LoginAsync()
        {
            if (!CanLogin)
                return new LoginResult { Success = false, Message = "ユーザー名とパスワードを入力してください。" };

            SetProcessing(true, "ログイン中...");

            try
            {
                var result = await _authService.LoginAsync(Username, Password);
                return result;
            }
            finally
            {
                SetProcessing(false, "");
            }
        }

        public async Task<AuthResult> LoginWithGoogleAsync()
        {
            SetProcessing(true, "Google認証中...");

            try
            {
                var result = await _googleAuthService.LoginWithGoogleAsync();
                
                if (result.Success && result.User != null)
                {
                    // オフライン認証データを保存
                    var offlineData = new OfflineAuthData
                    {
                        UserId = result.User.Id,
                        UserInfo = result.User,
                        ExpiresAt = DateTime.UtcNow.AddHours(72),
                        CreatedAt = DateTime.UtcNow,
                        LastUpdatedAt = DateTime.UtcNow
                    };

                    await _offlineAuthService.SaveOfflineAuthAsync(offlineData);
                }

                // GoogleAuthResultをAuthResultに変換
                return new AuthResult
                {
                    IsSuccess = result.Success,
                    AccessToken = "", // GoogleAuthResultにAccessTokenがない場合
                    UserInfo = result.User,
                    AuthType = AuthType.Online
                };
            }
            finally
            {
                SetProcessing(false, "");
            }
        }

        public async Task<bool> LogoutAsync()
        {
            SetProcessing(true, "ログアウト中...");

            try
            {
                // Google認証からログアウト
                await _googleAuthService.LogoutAsync();
                
                // オフライン認証データをクリア
                await _offlineAuthService.ClearOfflineAuthAsync();
                
                // 通常の認証からもログアウト
                return await _authService.LogoutAsync();
            }
            finally
            {
                SetProcessing(false, "");
            }
        }

        public async Task<bool> CheckOfflineAuthAsync()
        {
            var isConnected = await _networkService.CheckConnectivityAsync();
            var networkStatus = isConnected ? NetworkStatus.Online : NetworkStatus.Offline;
            if (networkStatus == NetworkStatus.Offline)
            {
                return await _offlineAuthService.ValidateOfflineAuthAsync();
            }
            return false;
        }

        public bool IsAuthenticated => _authService.IsAuthenticated;
        public UserInfo? CurrentUser => _authService.CurrentUser;
    }
}