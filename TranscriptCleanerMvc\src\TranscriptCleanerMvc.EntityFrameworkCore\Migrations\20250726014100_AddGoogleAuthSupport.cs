﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TranscriptCleanerMvc.Migrations
{
    /// <inheritdoc />
    public partial class AddGoogleAuthSupport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AppAppUsers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    GoogleId = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    GoogleEmail = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    ProfileImageUrl = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: true),
                    IsGoogleLinked = table.Column<bool>(type: "bit", nullable: false),
                    LastGoogleSync = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppAppUsers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppAppUsers_AbpUsers_Id",
                        column: x => x.Id,
                        principalTable: "AbpUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AppExternalLoginLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ProviderKey = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LoginTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IpAddress = table.Column<string>(type: "nvarchar(45)", maxLength: 45, nullable: true),
                    UserAgent = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: true),
                    Success = table.Column<bool>(type: "bit", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    SessionId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Scopes = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Metadata = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppExternalLoginLogs", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppAppUsers_GoogleEmail",
                table: "AppAppUsers",
                column: "GoogleEmail");

            migrationBuilder.CreateIndex(
                name: "IX_AppAppUsers_GoogleId",
                table: "AppAppUsers",
                column: "GoogleId");

            migrationBuilder.CreateIndex(
                name: "IX_AppAppUsers_IsGoogleLinked",
                table: "AppAppUsers",
                column: "IsGoogleLinked");

            migrationBuilder.CreateIndex(
                name: "IX_AppExternalLoginLogs_LoginTime",
                table: "AppExternalLoginLogs",
                column: "LoginTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppExternalLoginLogs_Provider",
                table: "AppExternalLoginLogs",
                column: "Provider");

            migrationBuilder.CreateIndex(
                name: "IX_AppExternalLoginLogs_Success",
                table: "AppExternalLoginLogs",
                column: "Success");

            migrationBuilder.CreateIndex(
                name: "IX_AppExternalLoginLogs_UserId",
                table: "AppExternalLoginLogs",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppAppUsers");

            migrationBuilder.DropTable(
                name: "AppExternalLoginLogs");
        }
    }
}
