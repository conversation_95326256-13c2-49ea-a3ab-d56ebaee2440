!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("just-compare")):"function"==typeof define&&define.amd?define("@abp/utils",["exports","just-compare"],r):r(((t=t||self).abp=t.abp||{},t.abp.utils=t.abp.utils||{},t.abp.utils.common={}),t.compare)}(this,(function(t,r){"use strict";r=r&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r;function e(t,r){var e,n,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(o){return function(u){return function(o){if(e)throw new TypeError("Generator is already executing.");for(;a;)try{if(e=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,n=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=r.call(t,a)}catch(t){o=[6,t],n=0}finally{e=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,u])}}}Object.create;function n(t,r){var e="function"==typeof Symbol&&t[Symbol.iterator];if(!e)return t;var n,i,o=e.call(t),a=[];try{for(;(void 0===r||r-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(e=o.return)&&e.call(o)}finally{if(i)throw i.error}}return a}function i(){for(var t=[],r=0;r<arguments.length;r++)t=t.concat(n(arguments[r]));return t}Object.create;var o=function(t){this.value=t},a=function(){function t(){this.size=0}return Object.defineProperty(t.prototype,"head",{get:function(){return this.first},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tail",{get:function(){return this.last},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"length",{get:function(){return this.size},enumerable:!1,configurable:!0}),t.prototype.attach=function(t,r,e){if(!r)return this.addHead(t);if(!e)return this.addTail(t);var n=new o(t);return n.previous=r,r.next=n,n.next=e,e.previous=n,this.size++,n},t.prototype.attachMany=function(r,e,n){if(!r.length)return[];if(!e)return this.addManyHead(r);if(!n)return this.addManyTail(r);var i=new t;return i.addManyTail(r),i.first.previous=e,e.next=i.first,i.last.next=n,n.previous=i.last,this.size+=r.length,i.toNodeArray()},t.prototype.detach=function(t){return t.previous?t.next?(t.previous.next=t.next,t.next.previous=t.previous,this.size--,t):this.dropTail():this.dropHead()},t.prototype.add=function(t){var r=this;return{after:function(){for(var e,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];return(e=r.addAfter).call.apply(e,i([r,t],n))},before:function(){for(var e,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];return(e=r.addBefore).call.apply(e,i([r,t],n))},byIndex:function(e){return r.addByIndex(t,e)},head:function(){return r.addHead(t)},tail:function(){return r.addTail(t)}}},t.prototype.addMany=function(t){var r=this;return{after:function(){for(var e,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];return(e=r.addManyAfter).call.apply(e,i([r,t],n))},before:function(){for(var e,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];return(e=r.addManyBefore).call.apply(e,i([r,t],n))},byIndex:function(e){return r.addManyByIndex(t,e)},head:function(){return r.addManyHead(t)},tail:function(){return r.addManyTail(t)}}},t.prototype.addAfter=function(t,e,n){void 0===n&&(n=r);var i=this.find((function(t){return n(t.value,e)}));return i?this.attach(t,i,i.next):this.addTail(t)},t.prototype.addBefore=function(t,e,n){void 0===n&&(n=r);var i=this.find((function(t){return n(t.value,e)}));return i?this.attach(t,i.previous,i):this.addHead(t)},t.prototype.addByIndex=function(t,r){if(r<0)r+=this.size;else if(r>=this.size)return this.addTail(t);if(r<=0)return this.addHead(t);var e=this.get(r);return this.attach(t,e.previous,e)},t.prototype.addHead=function(t){var r=new o(t);return r.next=this.first,this.first?this.first.previous=r:this.last=r,this.first=r,this.size++,r},t.prototype.addTail=function(t){var r=new o(t);return this.first?(r.previous=this.last,this.last.next=r,this.last=r):(this.first=r,this.last=r),this.size++,r},t.prototype.addManyAfter=function(t,e,n){void 0===n&&(n=r);var i=this.find((function(t){return n(t.value,e)}));return i?this.attachMany(t,i,i.next):this.addManyTail(t)},t.prototype.addManyBefore=function(t,e,n){void 0===n&&(n=r);var i=this.find((function(t){return n(t.value,e)}));return i?this.attachMany(t,i.previous,i):this.addManyHead(t)},t.prototype.addManyByIndex=function(t,r){if(r<0&&(r+=this.size),r<=0)return this.addManyHead(t);if(r>=this.size)return this.addManyTail(t);var e=this.get(r);return this.attachMany(t,e.previous,e)},t.prototype.addManyHead=function(t){var r=this;return t.reduceRight((function(t,e){return t.unshift(r.addHead(e)),t}),[])},t.prototype.addManyTail=function(t){var r=this;return t.map((function(t){return r.addTail(t)}))},t.prototype.drop=function(){var t=this;return{byIndex:function(r){return t.dropByIndex(r)},byValue:function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];return t.dropByValue.apply(t,r)},byValueAll:function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];return t.dropByValueAll.apply(t,r)},head:function(){return t.dropHead()},tail:function(){return t.dropTail()}}},t.prototype.dropMany=function(t){var r=this;return{byIndex:function(e){return r.dropManyByIndex(t,e)},head:function(){return r.dropManyHead(t)},tail:function(){return r.dropManyTail(t)}}},t.prototype.dropByIndex=function(t){t<0&&(t+=this.size);var r=this.get(t);return r?this.detach(r):void 0},t.prototype.dropByValue=function(t,e){void 0===e&&(e=r);var n=this.findIndex((function(r){return e(r.value,t)}));return n<0?void 0:this.dropByIndex(n)},t.prototype.dropByValueAll=function(t,e){void 0===e&&(e=r);for(var n=[],i=this.first,o=0;i;o++,i=i.next)e(i.value,t)&&n.push(this.dropByIndex(o-n.length));return n},t.prototype.dropHead=function(){var t=this.first;if(t)return this.first=t.next,this.first?this.first.previous=void 0:this.last=void 0,this.size--,t},t.prototype.dropTail=function(){var t=this.last;if(t)return this.last=t.previous,this.last?this.last.next=void 0:this.first=void 0,this.size--,t},t.prototype.dropManyByIndex=function(t,r){if(t<=0)return[];if(r<0)r=Math.max(r+this.size,0);else if(r>=this.size)return[];t=Math.min(t,this.size-r);for(var e=[];t--;){var n=this.get(r);e.push(this.detach(n))}return e},t.prototype.dropManyHead=function(t){if(t<=0)return[];t=Math.min(t,this.size);for(var r=[];t--;)r.unshift(this.dropHead());return r},t.prototype.dropManyTail=function(t){if(t<=0)return[];t=Math.min(t,this.size);for(var r=[];t--;)r.push(this.dropTail());return r},t.prototype.find=function(t){for(var r=this.first,e=0;r;e++,r=r.next)if(t(r,e,this))return r},t.prototype.findIndex=function(t){for(var r=this.first,e=0;r;e++,r=r.next)if(t(r,e,this))return e;return-1},t.prototype.forEach=function(t){for(var r=this.first,e=0;r;e++,r=r.next)t(r,e,this)},t.prototype.get=function(t){return this.find((function(r,e){return t===e}))},t.prototype.indexOf=function(t,e){return void 0===e&&(e=r),this.findIndex((function(r){return e(r.value,t)}))},t.prototype.toArray=function(){var t=new Array(this.size);return this.forEach((function(r,e){return t[e]=r.value})),t},t.prototype.toNodeArray=function(){var t=new Array(this.size);return this.forEach((function(r,e){return t[e]=r})),t},t.prototype.toString=function(t){return void 0===t&&(t=JSON.stringify),this.toArray().map((function(r){return t(r)})).join(" <-> ")},t.prototype[Symbol.iterator]=function(){var t;return e(this,(function(r){switch(r.label){case 0:t=this.first,0,r.label=1;case 1:return t?[4,t.value]:[3,4];case 2:r.sent(),r.label=3;case 3:return t=t.next,[3,1];case 4:return[2]}}))},t}();t.LinkedList=a,t.ListNode=o,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=abp-utils.umd.min.js.map