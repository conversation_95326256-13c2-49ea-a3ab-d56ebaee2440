using System;
using System.Threading.Tasks;
using TranscriptCleanerMvc.Transcripts.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TranscriptCleanerMvc.Transcripts;

public interface ITranscriptAppService : ICrudAppService<
    TranscriptDto,
    Guid,
    TranscriptFilterDto,
    CreateTranscriptDto,
    UpdateTranscriptDto>
{
    Task<TranscriptCorrectionResponseDto> CorrectTranscriptAsync(TranscriptCorrectionRequestDto input);
    
    Task<PagedResultDto<TranscriptDto>> GetMyTranscriptsAsync(TranscriptFilterDto input);
    
    Task<ListResultDto<TranscriptDto>> GetRecentTranscriptsAsync(int count = 10);
    
    Task<TranscriptDto> GetWithHistoryAsync(Guid id);
    
    Task<TranscriptDto> ReprocessAsync(Guid id);
}