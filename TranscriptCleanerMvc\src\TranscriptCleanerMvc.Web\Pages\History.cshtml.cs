using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Authorization;
using TranscriptCleanerMvc.Transcripts;
using TranscriptCleanerMvc.Transcripts.Dtos;

namespace TranscriptCleanerMvc.Web.Pages
{
    [Authorize]
    public class HistoryPageModel : PageModel
    {
        private readonly ITranscriptAppService _transcriptAppService;

        public HistoryPageModel(ITranscriptAppService transcriptAppService)
        {
            _transcriptAppService = transcriptAppService;
        }

        public IReadOnlyList<TranscriptDto> Transcripts { get; set; } = new List<TranscriptDto>();

        public async Task OnGetAsync()
        {
            var result = await _transcriptAppService.GetMyTranscriptsAsync(new TranscriptFilterDto
            {
                MaxResultCount = 50,
                SkipCount = 0
            });
            
            Transcripts = result.Items;
        }

        public string GetCorrectionTypeText(string correctionType)
        {
            return correctionType switch
            {
                "comprehensive" => "総合訂正",
                "grammar" => "文法訂正",
                "formatting" => "フォーマット整理",
                _ => correctionType
            };
        }
    }
}