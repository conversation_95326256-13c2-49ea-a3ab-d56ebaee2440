using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Controllers
{
    /// <summary>
    /// ユーザー管理コントローラー
    /// </summary>
    [ApiController]
    [Route("api/admin/users")]
    [Authorize(Roles = "admin")]
    public class UserManagementController : AbpControllerBase
    {
        private readonly IUserManagementService _userManagementService;

        public UserManagementController(IUserManagementService userManagementService)
        {
            _userManagementService = userManagementService;
        }

        /// <summary>
        /// ユーザー一覧を取得（Google連携状況付き）
        /// </summary>
        /// <param name="input">検索条件</param>
        /// <returns>ユーザー一覧</returns>
        [HttpGet]
        public async Task<PagedResultDto<UserWithExternalLinksDto>> GetUsersWithExternalLinksAsync([FromQuery] GetUsersWithExternalLinksInput input)
        {
            return await _userManagementService.GetUsersWithExternalLinksAsync(input);
        }

        /// <summary>
        /// ユーザーのGoogle連携状況を取得
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>連携状況</returns>
        [HttpGet("{userId}/external-links")]
        public async Task<UserExternalLinkStatusDto> GetUserExternalLinkStatusAsync(Guid userId)
        {
            return await _userManagementService.GetUserExternalLinkStatusAsync(userId);
        }

        /// <summary>
        /// 管理者による強制連携解除
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="provider">プロバイダー名</param>
        /// <returns>解除結果</returns>
        [HttpDelete("{userId}/external-links/{provider}")]
        public async Task<bool> ForceUnlinkExternalAccountAsync(Guid userId, string provider)
        {
            return await _userManagementService.ForceUnlinkExternalAccountAsync(userId, provider);
        }

        /// <summary>
        /// ユーザーに通知を送信
        /// </summary>
        /// <param name="input">通知内容</param>
        /// <returns>送信結果</returns>
        [HttpPost("notifications")]
        public async Task<bool> SendUserNotificationAsync(SendUserNotificationInput input)
        {
            return await _userManagementService.SendUserNotificationAsync(input);
        }

        /// <summary>
        /// 複数ユーザーに一括通知を送信
        /// </summary>
        /// <param name="input">一括通知内容</param>
        /// <returns>送信結果</returns>
        [HttpPost("notifications/bulk")]
        public async Task<BulkNotificationResultDto> SendBulkNotificationAsync(SendBulkNotificationInput input)
        {
            return await _userManagementService.SendBulkNotificationAsync(input);
        }

        /// <summary>
        /// ユーザーのログイン履歴を取得
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="input">検索条件</param>
        /// <returns>ログイン履歴</returns>
        [HttpGet("{userId}/login-history")]
        public async Task<PagedResultDto<UserLoginHistoryDto>> GetUserLoginHistoryAsync(Guid userId, [FromQuery] GetUserLoginHistoryInput input)
        {
            return await _userManagementService.GetUserLoginHistoryAsync(userId, input);
        }

        /// <summary>
        /// ユーザーアカウントを無効化
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <param name="request">無効化リクエスト</param>
        /// <returns>無効化結果</returns>
        [HttpPost("{userId}/disable")]
        public async Task<bool> DisableUserAccountAsync(Guid userId, [FromBody] DisableUserRequest request)
        {
            return await _userManagementService.DisableUserAccountAsync(userId, request.Reason);
        }

        /// <summary>
        /// ユーザーアカウントを有効化
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>有効化結果</returns>
        [HttpPost("{userId}/enable")]
        public async Task<bool> EnableUserAccountAsync(Guid userId)
        {
            return await _userManagementService.EnableUserAccountAsync(userId);
        }
    }

    /// <summary>
    /// ユーザー無効化リクエスト
    /// </summary>
    public class DisableUserRequest
    {
        /// <summary>
        /// 無効化理由
        /// </summary>
        public string Reason { get; set; } = string.Empty;
    }
}