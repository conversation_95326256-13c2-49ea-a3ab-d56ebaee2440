namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// Googleユーザー情報DTO
    /// </summary>
    public class GoogleUserInfoDto
    {
        /// <summary>
        /// Google ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// メールアドレス
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 表示名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 名前
        /// </summary>
        public string GivenName { get; set; } = string.Empty;

        /// <summary>
        /// 姓
        /// </summary>
        public string FamilyName { get; set; } = string.Empty;

        /// <summary>
        /// プロフィール画像URL
        /// </summary>
        public string? Picture { get; set; }

        /// <summary>
        /// メールアドレス確認済みフラグ
        /// </summary>
        public bool EmailVerified { get; set; }

        /// <summary>
        /// ロケール
        /// </summary>
        public string? Locale { get; set; }
    }
}