using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.Generic;

namespace TranscriptCleaner.WinUI.ViewModels;

public partial class LoginPageViewModel : ObservableObject
{
    [ObservableProperty]
    private string _username = string.Empty;

    [ObservableProperty]
    private string _password = string.Empty;

    [ObservableProperty]
    private bool _isProcessing = false;

    [ObservableProperty]
    private string _statusMessage = string.Empty;

    public bool CanLogin 
    { 
        get => !IsProcessing && 
               !string.IsNullOrWhiteSpace(Username) && 
               !string.IsNullOrWhiteSpace(Password); 
    }

    public bool IsValidLogin(string username, string password)
    {
        // Simple demo authentication
        var validCredentials = new Dictionary<string, string>
        {
            {"admin", "admin123"},
            {"testuser", "test123"},
            {"demo", "demo123"}
        };

        return validCredentials.ContainsKey(username) && 
               validCredentials[username] == password;
    }

    public void SetProcessing(bool processing, string message = "")
    {
        IsProcessing = processing;
        StatusMessage = message;
        OnPropertyChanged(nameof(CanLogin));
    }

    partial void OnUsernameChanged(string value)
    {
        OnPropertyChanged(nameof(CanLogin));
    }

    partial void OnPasswordChanged(string value)
    {
        OnPropertyChanged(nameof(CanLogin));
    }

    partial void OnIsProcessingChanged(bool value)
    {
        OnPropertyChanged(nameof(CanLogin));
    }
}