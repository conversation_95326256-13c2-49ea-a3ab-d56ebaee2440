using System;
using Volo.Abp.Application.Dtos;

namespace TranscriptCleanerMvc.Transcripts.Dtos;

public class TranscriptDto : EntityDto<Guid>
{
    public string Title { get; set; } = string.Empty;
    
    public string OriginalText { get; set; } = string.Empty;
    
    public string? CorrectedText { get; set; }
    
    public string Language { get; set; } = string.Empty;
    
    public string CorrectionType { get; set; } = string.Empty;
    
    public string Status { get; set; } = string.Empty;
    
    public int OriginalLength { get; set; }
    
    public int? CorrectedLength { get; set; }
    
    public int? ProcessingTimeMs { get; set; }
    
    public decimal? ProcessingCost { get; set; }
    
    public DateTime? ProcessedAt { get; set; }
    
    public string? ErrorMessage { get; set; }
    
    public DateTime CreationTime { get; set; }
}

public class CreateTranscriptDto
{
    public string Title { get; set; } = string.Empty;
    
    public string OriginalText { get; set; } = string.Empty;
    
    public string Language { get; set; } = "ja";
    
    public string CorrectionType { get; set; } = "comprehensive";
}

public class UpdateTranscriptDto
{
    public string? Title { get; set; }
    
    public string? CorrectedText { get; set; }
}

public class TranscriptFilterDto : PagedAndSortedResultRequestDto
{
    public string? Language { get; set; }
    
    public string? CorrectionType { get; set; }
    
    public string? Status { get; set; }
    
    public string? Filter { get; set; }
    
    public DateTime? CreatedAfter { get; set; }
    
    public DateTime? CreatedBefore { get; set; }
}