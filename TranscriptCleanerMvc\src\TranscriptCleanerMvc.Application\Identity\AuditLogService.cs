using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// 監査ログサービス実装
    /// </summary>
    public class AuditLogService : ApplicationService, IAuditLogService
    {
        private readonly IRepository<ExternalLoginLog, Guid> _externalLoginLogRepository;
        private readonly IIdentityUserRepository _userRepository;
        private readonly ILogger<AuditLogService> _logger;

        public AuditLogService(
            IRepository<ExternalLoginLog, Guid> externalLoginLogRepository,
            IIdentityUserRepository userRepository,
            ILogger<AuditLogService> logger)
        {
            _externalLoginLogRepository = externalLoginLogRepository;
            _userRepository = userRepository;
            _logger = logger;
        }

        /// <summary>
        /// 外部認証イベントをログに記録
        /// </summary>
        public async Task LogExternalAuthEventAsync(ExternalAuthAuditEvent auditEvent)
        {
            try
            {
                _logger.LogInformation("外部認証イベントをログに記録します。EventType: {EventType}, Provider: {Provider}, UserId: {UserId}", 
                    auditEvent.EventType, auditEvent.Provider, auditEvent.UserId);

                var logEntry = auditEvent.IsSuccess 
                    ? ExternalLoginLog.CreateSuccessLog(
                        auditEvent.UserId ?? Guid.Empty,
                        auditEvent.Provider,
                        auditEvent.ProviderKey ?? string.Empty,
                        auditEvent.IpAddress,
                        auditEvent.UserAgent)
                    : ExternalLoginLog.CreateFailureLog(
                        auditEvent.UserId ?? Guid.Empty,
                        auditEvent.Provider,
                        auditEvent.ProviderKey ?? string.Empty,
                        auditEvent.ErrorMessage ?? "Unknown error",
                        auditEvent.IpAddress,
                        auditEvent.UserAgent);

                await _externalLoginLogRepository.InsertAsync(logEntry);

                // 高重要度イベントの場合はアラートを送信
                if (ShouldSendAlert(auditEvent))
                {
                    await SendAdminAlertAsync(new AdminAlert
                    {
                        AlertType = AlertType.SecurityBreach,
                        Title = $"外部認証イベント: {auditEvent.EventType}",
                        Message = $"プロバイダー: {auditEvent.Provider}, ユーザー: {auditEvent.UserId}, 成功: {auditEvent.IsSuccess}",
                        Severity = DetermineAlertSeverity(auditEvent),
                        RelatedUserId = auditEvent.UserId,
                        RelatedIpAddress = auditEvent.IpAddress,
                        Details = auditEvent.AdditionalData
                    });
                }

                _logger.LogInformation("外部認証イベントのログ記録が完了しました");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "外部認証イベントログ記録中にエラーが発生しました");
                throw;
            }
        }

        /// <summary>
        /// セキュリティ監査イベントをログに記録
        /// </summary>
        public async Task LogSecurityEventAsync(SecurityAuditEvent securityEvent)
        {
            try
            {
                _logger.LogInformation("セキュリティ監査イベントをログに記録します。EventType: {EventType}, Severity: {Severity}", 
                    securityEvent.EventType, securityEvent.Severity);

                // TODO: 実際のセキュリティ監査ログテーブルに保存
                // 現在はログ出力のみ
                var logLevel = securityEvent.Severity switch
                {
                    SecuritySeverity.Critical => LogLevel.Critical,
                    SecuritySeverity.High => LogLevel.Error,
                    SecuritySeverity.Medium => LogLevel.Warning,
                    SecuritySeverity.Low => LogLevel.Information,
                    _ => LogLevel.Information
                };

                _logger.Log(logLevel, "セキュリティ監査: {EventType} | ユーザー: {UserId} | リソース: {Resource} | アクション: {Action} | 結果: {Result} | IP: {IpAddress} | 詳細: {Details}", 
                    securityEvent.EventType, securityEvent.UserId, securityEvent.Resource, securityEvent.Action, 
                    securityEvent.Result, securityEvent.IpAddress, securityEvent.Details);

                // 高重要度イベントの場合はアラートを送信
                if (securityEvent.Severity >= SecuritySeverity.High)
                {
                    await SendAdminAlertAsync(new AdminAlert
                    {
                        AlertType = AlertType.SecurityBreach,
                        Title = $"セキュリティイベント: {securityEvent.EventType}",
                        Message = $"アクション: {securityEvent.Action}, 結果: {securityEvent.Result}",
                        Severity = securityEvent.Severity switch
                        {
                            SecuritySeverity.Critical => AlertSeverity.Critical,
                            SecuritySeverity.High => AlertSeverity.Error,
                            SecuritySeverity.Medium => AlertSeverity.Warning,
                            _ => AlertSeverity.Info
                        },
                        RelatedUserId = securityEvent.UserId,
                        RelatedIpAddress = securityEvent.IpAddress,
                        Details = new Dictionary<string, object>
                        {
                            ["EventType"] = securityEvent.EventType.ToString(),
                            ["Resource"] = securityEvent.Resource ?? "",
                            ["Action"] = securityEvent.Action,
                            ["Result"] = securityEvent.Result,
                            ["Details"] = securityEvent.Details ?? ""
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "セキュリティ監査イベントログ記録中にエラーが発生しました");
                throw;
            }
        }

        /// <summary>
        /// 管理者向けアラートを送信
        /// </summary>
        public async Task<bool> SendAdminAlertAsync(AdminAlert alert)
        {
            try
            {
                _logger.LogInformation("管理者アラートを送信します。AlertType: {AlertType}, Severity: {Severity}", 
                    alert.AlertType, alert.Severity);

                // TODO: 実際のアラート送信実装
                // - メール通知
                // - Slack通知
                // - システム内通知
                // - SMS通知（緊急時）

                // 現在はログ出力のみ
                var logLevel = alert.Severity switch
                {
                    AlertSeverity.Critical => LogLevel.Critical,
                    AlertSeverity.Error => LogLevel.Error,
                    AlertSeverity.Warning => LogLevel.Warning,
                    AlertSeverity.Info => LogLevel.Information,
                    _ => LogLevel.Information
                };

                _logger.Log(logLevel, "管理者アラート: {Title} | {Message} | 関連ユーザー: {UserId} | 関連IP: {IpAddress}", 
                    alert.Title, alert.Message, alert.RelatedUserId, alert.RelatedIpAddress);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "管理者アラート送信中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// 監査ログを検索
        /// </summary>
        public async Task<PagedResultDto<AuditLogDto>> SearchAuditLogsAsync(SearchAuditLogsInput input)
        {
            try
            {
                _logger.LogInformation("監査ログを検索します。StartDate: {StartDate}, EndDate: {EndDate}", 
                    input.StartDate, input.EndDate);

                var queryable = await _externalLoginLogRepository.GetQueryableAsync();

                // フィルター適用
                if (input.StartDate.HasValue)
                {
                    queryable = queryable.Where(log => log.CreationTime >= input.StartDate.Value);
                }

                if (input.EndDate.HasValue)
                {
                    queryable = queryable.Where(log => log.CreationTime <= input.EndDate.Value);
                }

                if (input.UserId.HasValue)
                {
                    queryable = queryable.Where(log => log.UserId == input.UserId.Value);
                }

                if (!string.IsNullOrEmpty(input.Provider))
                {
                    queryable = queryable.Where(log => log.Provider == input.Provider);
                }

                if (!string.IsNullOrEmpty(input.IpAddress))
                {
                    queryable = queryable.Where(log => log.IpAddress == input.IpAddress);
                }

                if (input.IsSuccess.HasValue)
                {
                    queryable = queryable.Where(log => log.Success == input.IsSuccess.Value);
                }

                if (!string.IsNullOrEmpty(input.SearchKeyword))
                {
                    queryable = queryable.Where(log => 
                        log.ErrorMessage.Contains(input.SearchKeyword) ||
                        log.UserAgent.Contains(input.SearchKeyword));
                }

                // ソート
                queryable = queryable.OrderByDescending(log => log.CreationTime);

                // ページング
                var totalCount = queryable.Count();
                var logs = queryable
                    .Skip(input.SkipCount)
                    .Take(input.MaxResultCount)
                    .ToList();

                // DTOに変換
                var logDtos = new List<AuditLogDto>();
                foreach (var log in logs)
                {
                    var user = await _userRepository.FindAsync(log.UserId);
                    
                    logDtos.Add(new AuditLogDto
                    {
                        Id = log.Id,
                        EventType = log.Success ? "LoginSuccess" : "LoginFailure",
                        UserId = log.UserId,
                        UserName = user?.UserName,
                        Provider = log.Provider,
                        Action = "ExternalLogin",
                        Result = log.Success ? "Success" : "Failure",
                        IpAddress = log.IpAddress,
                        UserAgent = log.UserAgent,
                        Details = log.ErrorMessage,
                        Severity = log.Success ? SecuritySeverity.Low : SecuritySeverity.Medium,
                        Timestamp = log.CreationTime
                    });
                }

                return new PagedResultDto<AuditLogDto>(totalCount, logDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "監査ログ検索中にエラーが発生しました");
                throw;
            }
        }

        /// <summary>
        /// セキュリティ監査レポートを生成
        /// </summary>
        public async Task<SecurityAuditReport> GenerateSecurityAuditReportAsync(GenerateAuditReportInput input)
        {
            try
            {
                _logger.LogInformation("セキュリティ監査レポートを生成します。ReportType: {ReportType}, Period: {StartDate} - {EndDate}", 
                    input.ReportType, input.StartDate, input.EndDate);

                var queryable = await _externalLoginLogRepository.GetQueryableAsync();
                queryable = queryable.Where(log => 
                    log.CreationTime >= input.StartDate && 
                    log.CreationTime <= input.EndDate);

                var logs = queryable.ToList();

                // サマリー統計を計算
                var summary = new AuditSummary
                {
                    TotalEvents = logs.Count,
                    SuccessfulEvents = logs.Count(l => l.Success),
                    FailedEvents = logs.Count(l => !l.Success),
                    SecurityIncidents = logs.Count(l => !l.Success && l.ErrorMessage?.Contains("security") == true),
                    UniqueUsers = logs.Select(l => l.UserId).Distinct().Count(),
                    UniqueIpAddresses = logs.Select(l => l.IpAddress).Distinct().Count()
                };

                // イベント統計を計算
                var eventStats = logs
                    .GroupBy(l => l.Provider)
                    .Select(g => new EventStatistics
                    {
                        EventType = g.Key,
                        Count = g.Count(),
                        Percentage = (double)g.Count() / logs.Count * 100,
                        LastOccurred = g.Max(l => l.CreationTime)
                    })
                    .ToArray();

                // セキュリティインシデントを抽出
                var securityIncidents = logs
                    .Where(l => !l.Success)
                    .GroupBy(l => new { l.UserId, l.IpAddress })
                    .Where(g => g.Count() > 3) // 3回以上の失敗
                    .Select(g => new SecurityIncident
                    {
                        Id = Guid.NewGuid(),
                        Type = "Multiple Failed Logins",
                        Description = $"{g.Count()}回の連続ログイン失敗",
                        Severity = g.Count() > 10 ? SecuritySeverity.High : SecuritySeverity.Medium,
                        OccurredAt = g.Max(l => l.CreationTime),
                        AffectedUser = g.Key.UserId.ToString(),
                        SourceIp = g.Key.IpAddress
                    })
                    .ToArray();

                // 推奨事項を生成
                var recommendations = GenerateRecommendations(summary, securityIncidents);

                return new SecurityAuditReport
                {
                    ReportId = Guid.NewGuid(),
                    ReportType = input.ReportType,
                    Period = new DateRange { StartDate = input.StartDate, EndDate = input.EndDate },
                    Summary = summary,
                    EventStats = eventStats,
                    SecurityIncidents = securityIncidents,
                    Recommendations = recommendations,
                    GeneratedBy = CurrentUser.UserName ?? "System"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "セキュリティ監査レポート生成中にエラーが発生しました");
                throw;
            }
        }

        /// <summary>
        /// 異常なアクティビティを検出
        /// </summary>
        public async Task<SuspiciousActivity[]> DetectSuspiciousActivitiesAsync(DetectSuspiciousActivitiesInput input)
        {
            try
            {
                _logger.LogInformation("異常なアクティビティを検出します。DetectionWindowHours: {Hours}", 
                    input.DetectionWindowHours);

                var startTime = DateTime.UtcNow.AddHours(-input.DetectionWindowHours);
                var queryable = await _externalLoginLogRepository.GetQueryableAsync();
                var recentLogs = queryable.Where(log => log.CreationTime >= startTime).ToList();

                var suspiciousActivities = new List<SuspiciousActivity>();

                // 複数回のログイン失敗を検出
                var multipleFailures = recentLogs
                    .Where(l => !l.Success)
                    .GroupBy(l => new { l.UserId, l.IpAddress })
                    .Where(g => g.Count() >= 5)
                    .Select(g => new SuspiciousActivity
                    {
                        Id = Guid.NewGuid(),
                        ActivityType = SuspiciousActivityType.MultipleFailedLogins,
                        Description = $"{g.Count()}回の連続ログイン失敗が検出されました",
                        Severity = g.Count() > 10 ? SecuritySeverity.High : SecuritySeverity.Medium,
                        UserId = g.Key.UserId,
                        IpAddress = g.Key.IpAddress,
                        DetectedAt = DateTime.UtcNow,
                        RelatedEventCount = g.Count(),
                        Status = ActivityStatus.New,
                        Details = new Dictionary<string, object>
                        {
                            ["FailureCount"] = g.Count(),
                            ["TimeSpan"] = $"{input.DetectionWindowHours} hours",
                            ["LastFailure"] = g.Max(l => l.CreationTime)
                        }
                    });

                suspiciousActivities.AddRange(multipleFailures);

                // 異常な時間帯のログインを検出
                var unusualTimeLogins = recentLogs
                    .Where(l => l.Success && (l.CreationTime.Hour < 6 || l.CreationTime.Hour > 22))
                    .GroupBy(l => l.UserId)
                    .Where(g => g.Count() > 1)
                    .Select(g => new SuspiciousActivity
                    {
                        Id = Guid.NewGuid(),
                        ActivityType = SuspiciousActivityType.UnusualLoginTime,
                        Description = "異常な時間帯でのログインが検出されました",
                        Severity = SecuritySeverity.Low,
                        UserId = g.Key,
                        DetectedAt = DateTime.UtcNow,
                        RelatedEventCount = g.Count(),
                        Status = ActivityStatus.New,
                        Details = new Dictionary<string, object>
                        {
                            ["LoginTimes"] = g.Select(l => l.CreationTime.ToString("HH:mm")).ToArray(),
                            ["LoginCount"] = g.Count()
                        }
                    });

                suspiciousActivities.AddRange(unusualTimeLogins);

                return suspiciousActivities
                    .Where(a => a.Severity >= input.MinimumSeverity)
                    .OrderByDescending(a => a.Severity)
                    .ThenByDescending(a => a.DetectedAt)
                    .ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "異常アクティビティ検出中にエラーが発生しました");
                throw;
            }
        }

        /// <summary>
        /// 監査ログをエクスポート
        /// </summary>
        public async Task<ExportResult> ExportAuditLogsAsync(ExportAuditLogsInput input)
        {
            try
            {
                _logger.LogInformation("監査ログをエクスポートします。Format: {Format}, Period: {StartDate} - {EndDate}", 
                    input.Format, input.StartDate, input.EndDate);

                // 検索条件を適用してログを取得
                var searchInput = input.Filter ?? new SearchAuditLogsInput();
                searchInput.StartDate = input.StartDate;
                searchInput.EndDate = input.EndDate;
                searchInput.MaxResultCount = int.MaxValue; // 全件取得

                var logsResult = await SearchAuditLogsAsync(searchInput);

                // フォーマットに応じてエクスポート
                byte[] fileData;
                string fileName;
                string contentType;

                switch (input.Format)
                {
                    case ExportFormat.Csv:
                        fileData = ExportToCsv(logsResult.Items, input.IncludeColumns);
                        fileName = $"audit_logs_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv";
                        contentType = "text/csv";
                        break;

                    case ExportFormat.Json:
                        fileData = ExportToJson(logsResult.Items);
                        fileName = $"audit_logs_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json";
                        contentType = "application/json";
                        break;

                    default:
                        throw new NotSupportedException($"エクスポート形式 {input.Format} はサポートされていません");
                }

                return new ExportResult
                {
                    FileName = fileName,
                    FileSize = fileData.Length,
                    FileData = fileData,
                    ContentType = contentType,
                    RecordCount = logsResult.Items.Count
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "監査ログエクスポート中にエラーが発生しました");
                throw;
            }
        }

        /// <summary>
        /// 監査ログの保持期間管理
        /// </summary>
        public async Task<int> ManageLogRetentionAsync(int retentionDays)
        {
            try
            {
                _logger.LogInformation("監査ログの保持期間管理を実行します。RetentionDays: {Days}", retentionDays);

                var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
                var queryable = await _externalLoginLogRepository.GetQueryableAsync();
                var oldLogs = queryable.Where(log => log.CreationTime < cutoffDate).ToList();

                var deletedCount = oldLogs.Count;
                
                foreach (var log in oldLogs)
                {
                    await _externalLoginLogRepository.DeleteAsync(log);
                }

                _logger.LogInformation("古い監査ログを削除しました。削除件数: {Count}", deletedCount);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "監査ログ保持期間管理中にエラーが発生しました");
                throw;
            }
        }

        /// <summary>
        /// アラート送信が必要かどうかを判定
        /// </summary>
        private bool ShouldSendAlert(ExternalAuthAuditEvent auditEvent)
        {
            return auditEvent.EventType == ExternalAuthEventType.LoginFailure ||
                   auditEvent.EventType == ExternalAuthEventType.AccountUnlink ||
                   !auditEvent.IsSuccess;
        }

        /// <summary>
        /// アラート重要度を判定
        /// </summary>
        private AlertSeverity DetermineAlertSeverity(ExternalAuthAuditEvent auditEvent)
        {
            if (!auditEvent.IsSuccess)
            {
                return auditEvent.EventType == ExternalAuthEventType.LoginFailure ? 
                    AlertSeverity.Warning : AlertSeverity.Error;
            }

            return AlertSeverity.Info;
        }

        /// <summary>
        /// 推奨事項を生成
        /// </summary>
        private string[] GenerateRecommendations(AuditSummary summary, SecurityIncident[] incidents)
        {
            var recommendations = new List<string>();

            if (summary.FailedEvents > summary.SuccessfulEvents * 0.1)
            {
                recommendations.Add("ログイン失敗率が高いため、パスワードポリシーの強化を検討してください");
            }

            if (incidents.Length > 0)
            {
                recommendations.Add("複数のセキュリティインシデントが検出されました。アカウントロック機能の有効化を検討してください");
            }

            if (summary.UniqueIpAddresses > summary.UniqueUsers * 3)
            {
                recommendations.Add("IPアドレスの多様性が高いため、地理的制限の実装を検討してください");
            }

            if (recommendations.Count == 0)
            {
                recommendations.Add("現在のセキュリティ状況は良好です。定期的な監視を継続してください");
            }

            return recommendations.ToArray();
        }

        /// <summary>
        /// CSV形式でエクスポート
        /// </summary>
        private byte[] ExportToCsv(IReadOnlyList<AuditLogDto> logs, string[]? includeColumns)
        {
            var csv = new StringBuilder();
            
            // ヘッダー
            csv.AppendLine("Timestamp,EventType,UserId,UserName,Provider,Action,Result,IpAddress,UserAgent,Details,Severity");
            
            // データ
            foreach (var log in logs)
            {
                csv.AppendLine($"{log.Timestamp:yyyy-MM-dd HH:mm:ss},{log.EventType},{log.UserId},{log.UserName},{log.Provider},{log.Action},{log.Result},{log.IpAddress},\"{log.UserAgent}\",\"{log.Details}\",{log.Severity}");
            }

            return Encoding.UTF8.GetBytes(csv.ToString());
        }

        /// <summary>
        /// JSON形式でエクスポート
        /// </summary>
        private byte[] ExportToJson(IReadOnlyList<AuditLogDto> logs)
        {
            var json = System.Text.Json.JsonSerializer.Serialize(logs, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });

            return Encoding.UTF8.GetBytes(json);
        }
    }
}