using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Windows.Storage;

namespace TranscriptCleaner.WinUI.Services
{
    /// <summary>
    /// 認証状態管理サービス実装
    /// </summary>
    public class AuthStateManager : IAuthStateManager
    {
        private readonly IGoogleAuthService _googleAuthService;
        private readonly ILogger<AuthStateManager> _logger;
        
        private AuthenticationStatus _currentStatus = AuthenticationStatus.NotAuthenticated;
        
        // ローカル設定キー
        private const string AUTH_STATUS_KEY = "auth_status";
        private const string LOCAL_AUTH_DATA_KEY = "local_auth_data";
        private const string AUTO_LOGIN_KEY = "auto_login_enabled";
        private const string LAST_ACTIVITY_KEY = "last_activity_time";

        public event EventHandler<AuthStateChangedEventArgs>? AuthStateChanged;

        public AuthStateManager(IGoogleAuthService googleAuthService, ILogger<AuthStateManager> logger)
        {
            _googleAuthService = googleAuthService;
            _logger = logger;
        }

        /// <summary>
        /// 現在の認証状態を取得
        /// </summary>
        public async Task<AuthenticationStatus> GetCurrentAuthStatusAsync()
        {
            try
            {
                // Google認証サービスから状態を取得
                var googleAuthState = await _googleAuthService.GetAuthStateAsync();
                
                var status = googleAuthState switch
                {
                    AuthState.Authenticated => AuthenticationStatus.Authenticated,
                    AuthState.TokenExpired => AuthenticationStatus.TokenExpired,
                    AuthState.Error => AuthenticationStatus.Error,
                    _ => AuthenticationStatus.NotAuthenticated
                };

                if (_currentStatus != status)
                {
                    await UpdateAuthStatusAsync(status);
                }

                return status;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証状態取得中にエラーが発生しました");
                return AuthenticationStatus.Error;
            }
        }

        /// <summary>
        /// 認証状態を更新
        /// </summary>
        public async Task UpdateAuthStatusAsync(AuthenticationStatus status)
        {
            try
            {
                var previousStatus = _currentStatus;
                _currentStatus = status;

                // ローカル設定に保存
                var localSettings = ApplicationData.Current.LocalSettings;
                localSettings.Values[AUTH_STATUS_KEY] = status.ToString();
                localSettings.Values[LAST_ACTIVITY_KEY] = DateTime.UtcNow.ToString("O");

                _logger.LogInformation("認証状態が変更されました: {Previous} -> {Current}", previousStatus, status);

                // イベントを発火
                AuthStateChanged?.Invoke(this, new AuthStateChangedEventArgs(previousStatus, status, "状態更新"));

                // 状態に応じた追加処理
                await HandleStatusChangeAsync(previousStatus, status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証状態更新中にエラーが発生しました");
            }
        }

        /// <summary>
        /// アプリケーション最小化時の処理
        /// </summary>
        public async Task OnApplicationMinimizedAsync()
        {
            try
            {
                _logger.LogInformation("アプリケーションが最小化されました");

                // 現在の認証状態を保存
                await SaveCurrentStateAsync();

                // 最後のアクティビティ時刻を更新
                var localSettings = ApplicationData.Current.LocalSettings;
                localSettings.Values[LAST_ACTIVITY_KEY] = DateTime.UtcNow.ToString("O");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "アプリケーション最小化処理中にエラーが発生しました");
            }
        }

        /// <summary>
        /// アプリケーション復元時の処理
        /// </summary>
        public async Task OnApplicationRestoredAsync()
        {
            try
            {
                _logger.LogInformation("アプリケーションが復元されました");

                // 保存された状態を復元
                await RestoreStateAsync();

                // 認証の有効性を検証
                var isValid = await ValidateAuthenticationAsync();
                if (!isValid)
                {
                    await UpdateAuthStatusAsync(AuthenticationStatus.TokenExpired);
                }

                // 自動ログインが有効な場合は再認証を試行
                var autoLoginEnabled = await GetAutoLoginSettingAsync();
                if (autoLoginEnabled && _currentStatus == AuthenticationStatus.TokenExpired)
                {
                    await TryAutoRefreshAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "アプリケーション復元処理中にエラーが発生しました");
            }
        }

        /// <summary>
        /// ローカル認証情報を管理
        /// </summary>
        public async Task ManageLocalAuthDataAsync()
        {
            try
            {
                var localAuthData = await GetLocalAuthDataAsync();
                
                if (localAuthData != null)
                {
                    if (!localAuthData.IsValid)
                    {
                        _logger.LogInformation("ローカル認証データが期限切れです。クリアします。");
                        await ClearLocalAuthDataAsync();
                        await UpdateAuthStatusAsync(AuthenticationStatus.NotAuthenticated);
                    }
                    else
                    {
                        _logger.LogInformation("ローカル認証データは有効です。UserId: {UserId}", localAuthData.UserId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ローカル認証情報管理中にエラーが発生しました");
            }
        }

        /// <summary>
        /// キャッシュをクリア
        /// </summary>
        public async Task ClearCacheAsync()
        {
            try
            {
                _logger.LogInformation("キャッシュをクリアします");

                // Google認証サービスのデータをクリア
                await _googleAuthService.ClearAuthDataAsync();

                // ローカル認証データをクリア
                await ClearLocalAuthDataAsync();

                // 認証状態をリセット
                await UpdateAuthStatusAsync(AuthenticationStatus.NotAuthenticated);

                _logger.LogInformation("キャッシュクリアが完了しました");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "キャッシュクリア中にエラーが発生しました");
            }
        }

        /// <summary>
        /// 認証の有効性を検証
        /// </summary>
        public async Task<bool> ValidateAuthenticationAsync()
        {
            try
            {
                var authState = await _googleAuthService.GetAuthStateAsync();
                var accessToken = await _googleAuthService.GetAccessTokenAsync();

                return authState == AuthState.Authenticated && !string.IsNullOrEmpty(accessToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証有効性検証中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// 自動ログイン設定を管理
        /// </summary>
        public async Task SetAutoLoginAsync(bool enabled)
        {
            try
            {
                var localSettings = ApplicationData.Current.LocalSettings;
                localSettings.Values[AUTO_LOGIN_KEY] = enabled;
                
                _logger.LogInformation("自動ログイン設定を変更しました: {Enabled}", enabled);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "自動ログイン設定変更中にエラーが発生しました");
            }
        }

        /// <summary>
        /// 自動ログイン設定を取得
        /// </summary>
        public async Task<bool> GetAutoLoginSettingAsync()
        {
            try
            {
                var localSettings = ApplicationData.Current.LocalSettings;
                var value = localSettings.Values[AUTO_LOGIN_KEY];
                
                if (value is bool boolValue)
                {
                    return boolValue;
                }

                return false; // デフォルトは無効
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "自動ログイン設定取得中にエラーが発生しました");
                return false;
            }
        }

        /// <summary>
        /// 状態変更に応じた処理
        /// </summary>
        private async Task HandleStatusChangeAsync(AuthenticationStatus previous, AuthenticationStatus current)
        {
            switch (current)
            {
                case AuthenticationStatus.Authenticated:
                    await SaveAuthenticatedUserDataAsync();
                    break;
                    
                case AuthenticationStatus.NotAuthenticated:
                case AuthenticationStatus.Error:
                    await ClearLocalAuthDataAsync();
                    break;
                    
                case AuthenticationStatus.TokenExpired:
                    await TryAutoRefreshAsync();
                    break;
            }
        }

        /// <summary>
        /// 現在の状態を保存
        /// </summary>
        private async Task SaveCurrentStateAsync()
        {
            var localSettings = ApplicationData.Current.LocalSettings;
            localSettings.Values[AUTH_STATUS_KEY] = _currentStatus.ToString();
        }

        /// <summary>
        /// 状態を復元
        /// </summary>
        private async Task RestoreStateAsync()
        {
            try
            {
                var localSettings = ApplicationData.Current.LocalSettings;
                var statusString = localSettings.Values[AUTH_STATUS_KEY] as string;
                
                if (!string.IsNullOrEmpty(statusString) && 
                    Enum.TryParse<AuthenticationStatus>(statusString, out var status))
                {
                    _currentStatus = status;
                    _logger.LogInformation("認証状態を復元しました: {Status}", status);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "状態復元中にエラーが発生しました");
            }
        }

        /// <summary>
        /// 認証済みユーザーデータを保存
        /// </summary>
        private async Task SaveAuthenticatedUserDataAsync()
        {
            try
            {
                // TODO: 実際のユーザー情報を取得して保存
                var localAuthData = new LocalAuthData
                {
                    UserId = "current_user_id",
                    UserName = "Current User",
                    Email = "<EMAIL>",
                    LastLoginTime = DateTime.UtcNow,
                    Provider = "Google",
                    AutoLoginEnabled = await GetAutoLoginSettingAsync(),
                    ExpiresAt = DateTime.UtcNow.AddDays(7) // 7日間有効
                };

                await SaveLocalAuthDataAsync(localAuthData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "認証済みユーザーデータ保存中にエラーが発生しました");
            }
        }

        /// <summary>
        /// 自動リフレッシュを試行
        /// </summary>
        private async Task TryAutoRefreshAsync()
        {
            try
            {
                var autoLoginEnabled = await GetAutoLoginSettingAsync();
                if (!autoLoginEnabled)
                {
                    return;
                }

                _logger.LogInformation("自動トークンリフレッシュを試行します");
                
                var refreshed = await _googleAuthService.RefreshTokenAsync();
                if (refreshed)
                {
                    await UpdateAuthStatusAsync(AuthenticationStatus.Authenticated);
                    _logger.LogInformation("自動トークンリフレッシュが成功しました");
                }
                else
                {
                    _logger.LogWarning("自動トークンリフレッシュに失敗しました");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "自動リフレッシュ中にエラーが発生しました");
            }
        }

        /// <summary>
        /// ローカル認証データを保存
        /// </summary>
        private async Task SaveLocalAuthDataAsync(LocalAuthData data)
        {
            var json = JsonSerializer.Serialize(data);
            var localSettings = ApplicationData.Current.LocalSettings;
            localSettings.Values[LOCAL_AUTH_DATA_KEY] = json;
        }

        /// <summary>
        /// ローカル認証データを取得
        /// </summary>
        private async Task<LocalAuthData?> GetLocalAuthDataAsync()
        {
            try
            {
                var localSettings = ApplicationData.Current.LocalSettings;
                var json = localSettings.Values[LOCAL_AUTH_DATA_KEY] as string;
                
                if (string.IsNullOrEmpty(json))
                {
                    return null;
                }

                return JsonSerializer.Deserialize<LocalAuthData>(json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ローカル認証データ取得中にエラーが発生しました");
                return null;
            }
        }

        /// <summary>
        /// ローカル認証データをクリア
        /// </summary>
        private async Task ClearLocalAuthDataAsync()
        {
            var localSettings = ApplicationData.Current.LocalSettings;
            localSettings.Values.Remove(LOCAL_AUTH_DATA_KEY);
        }
    }
}