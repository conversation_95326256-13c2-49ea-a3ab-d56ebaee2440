using Microsoft.AspNetCore.Builder;
using TranscriptCleanerMvc;
using Volo.Abp.AspNetCore.TestBase;

var builder = WebApplication.CreateBuilder();
builder.Environment.ContentRootPath = GetWebProjectContentRootPathHelper.Get("TranscriptCleanerMvc.Web.csproj"); 
await builder.RunAbpModuleAsync<TranscriptCleanerMvcWebTestModule>(applicationName: "TranscriptCleanerMvc.Web");

public partial class Program
{
}
