# 要件書

## 概要

TranscriptCleanerMvc アプリケーション（ABP vNext のデフォルトポート 44396 で動作）とそのクライアントアプリケーション（MAUI、WinUI）にソーシャルアカウント認証機能を追加し、ユーザーが Google、Microsoft、GitHub アカウントを使用してログインできるようにする。既存の ABP フレームワークの Identity 機能を拡張し、外部認証プロバイダーとの統合を実現する。

新しい Google Cloud Console アプリケーションを登録し、TranscriptCleanerMvc 用の認証設定を構成する。

**対象アプリケーション:**

- TranscriptCleanerMvc（ABP Framework Web API）- メイン認証サーバー
- TranscriptCleaner.Maui（.NET MAUI クライアント）- クロスプラットフォーム対応
- TranscriptCleaner.WinUI（WinUI クライアント）- Windows 専用最適化

## 要件

### 要件 1: Google 認証統合

**ユーザーストーリー:** ユーザーとして、Google アカウントを使用して TranscriptCleanerMvc にログインしたい。これにより、新しいアカウントを作成する手間を省き、既存の Google アカウントで素早くアクセスできるようになる。

#### 受け入れ基準

1. WHEN ユーザーがログインページにアクセスした時 THEN システムは「Google でログイン」ボタンを表示する
2. WHEN ユーザーが「Google でログイン」ボタンをクリックした時 THEN システムは Google の認証ページにリダイレクトする
3. WHEN ユーザーが Google 認証を完了した時 THEN システムはユーザーをアプリケーションにログインさせる
4. IF ユーザーが初回 Google ログインの場合 THEN システムは新しいユーザーアカウントを自動作成する
5. WHEN Google 認証が失敗した時 THEN システムは適切なエラーメッセージを表示する
6. WHEN システムが Google OAuth 設定を読み込む時 THEN システムは appsettings.json または環境変数から Client ID と Client Secret を取得する
7. WHEN Google 認証のリダイレクト URI が設定される時 THEN システムは localhost:44396（HTTPS）ベースの URI を使用する

### 要件 2: Microsoft 認証統合（TODO - 将来実装）

**ユーザーストーリー:** ユーザーとして、Microsoft アカウント（Azure AD）を使用して TranscriptCleanerMvc にログインしたい。これにより、企業環境でのシングルサインオン体験を提供できる。

#### 受け入れ基準（将来実装予定）

1. WHEN ユーザーがログインページにアクセスした時 THEN システムは「Microsoft でログイン」ボタンを表示する
2. WHEN ユーザーが「Microsoft でログイン」ボタンをクリックした時 THEN システムは Microsoft 認証ページにリダイレクトする
3. WHEN ユーザーが Microsoft 認証を完了した時 THEN システムはユーザーをアプリケーションにログインさせる
4. IF ユーザーが初回 Microsoft ログインの場合 THEN システムは新しいユーザーアカウントを自動作成する
5. WHEN Microsoft 認証が失敗した時 THEN システムは適切なエラーメッセージを表示する

### 要件 3: GitHub 認証統合（TODO - 将来実装）

**ユーザーストーリー:** 開発者として、GitHub アカウントを使用して TranscriptCleanerMvc にログインしたい。これにより、開発者コミュニティにとって馴染みのある認証方法を提供できる。

#### 受け入れ基準（将来実装予定）

1. WHEN ユーザーがログインページにアクセスした時 THEN システムは「GitHub でログイン」ボタンを表示する
2. WHEN ユーザーが「GitHub でログイン」ボタンをクリックした時 THEN システムは GitHub 認証ページにリダイレクトする
3. WHEN ユーザーが GitHub 認証を完了した時 THEN システムはユーザーをアプリケーションにログインさせる
4. IF ユーザーが初回 GitHub ログインの場合 THEN システムは新しいユーザーアカウントを自動作成する
5. WHEN GitHub 認証が失敗した時 THEN システムは適切なエラーメッセージを表示する

### 要件 4: Google アカウント連携機能

**ユーザーストーリー:** 既存のローカルアカウントを持つユーザーとして、Google アカウントを既存アカウントに連携させたい。これにより、複数の認証方法を使用して同じアカウントにアクセスできる。

#### 受け入れ基準

1. WHEN ログイン済みユーザーがプロフィール設定にアクセスした時 THEN システムは「Google アカウント連携」セクションを表示する
2. WHEN ユーザーが未連携の Google アカウント連携ボタンをクリックした時 THEN システムは Google 認証ページにリダイレクトする
3. WHEN Google 認証が成功した時 THEN システムは Google アカウントを現在のユーザーアカウントに連携する
4. WHEN ユーザーが連携済みの Google アカウントを解除したい時 THEN システムは連携解除機能を提供する
5. IF ユーザーが既に他のアカウントに連携済みの Google アカウントで連携を試みた時 THEN システムは適切なエラーメッセージを表示する

### 要件 5: Google プロフィール情報同期

**ユーザーストーリー:** ユーザーとして、Google アカウントからの基本情報（名前、メールアドレス、プロフィール画像）が自動的に同期されることを期待する。これにより、手動でプロフィール情報を入力する手間を省ける。

#### 受け入れ基準

1. WHEN ユーザーが初回 Google ログインした時 THEN システムは Google から取得した名前とメールアドレスでアカウントを作成する
2. WHEN ユーザーが Google ログインした時 THEN システムは Google からプロフィール画像 URL を取得して保存する
3. IF Google からメールアドレスが取得できない場合 THEN システムはユーザーにメールアドレス入力を求める
4. WHEN ユーザーがプロフィール同期を有効にした時 THEN システムは次回ログイン時に Google から最新情報を取得する
5. WHEN Google からの情報取得が失敗した時 THEN システムは既存の情報を保持し、エラーログを記録する

### 要件 6: セキュリティとプライバシー

**ユーザーストーリー:** ユーザーとして、ソーシャルアカウント認証が安全で、個人情報が適切に保護されることを期待する。

#### 受け入れ基準

1. WHEN システムが外部認証を処理する時 THEN システムは OAuth 2.0/OpenID Connect の標準プロトコルを使用する
2. WHEN 外部プロバイダーからアクセストークンを受け取った時 THEN システムは必要最小限の情報のみを取得する
3. WHEN ユーザーが外部アカウント連携を解除した時 THEN システムは関連するトークンとデータを削除する
4. WHEN 外部認証エラーが発生した時 THEN システムは詳細なエラー情報をログに記録し、ユーザーには一般的なエラーメッセージを表示する
5. IF 外部プロバイダーから取得したメールアドレスが既存ユーザーと重複する場合 THEN システムは適切な重複処理ロジックを実行する

### 要件 7: Google 認証管理機能

**ユーザーストーリー:** 管理者として、Google 認証の設定を管理し、ユーザーの Google アカウント連携状況を確認したい。

#### 受け入れ基準

1. WHEN 管理者が設定画面にアクセスした時 THEN システムは Google 認証の有効/無効設定を表示する
2. WHEN 管理者が Google 認証を無効にした時 THEN システムは Google でのログインを無効にする
3. WHEN 管理者がユーザー管理画面にアクセスした時 THEN システムは各ユーザーの Google アカウント連携状況を表示する
4. WHEN 管理者がユーザーの Google アカウント連携を強制解除した時 THEN システムは該当連携を削除し、ユーザーに通知する
5. WHEN システムが Google 認証関連のエラーを検出した時 THEN システムは管理者向けのログとアラートを生成する

### 要件 8: Google 認証設定管理

**ユーザーストーリー:** 開発者として、Google 認証プロバイダーの設定を環境変数や appsettings.json で管理したい。これにより、異なる環境（開発、ステージング、本番）で適切な設定を使用できる。

#### 受け入れ基準

1. WHEN システムが起動する時 THEN システムは appsettings.json から Google 認証プロバイダーの設定を読み込む
2. WHEN 環境変数が設定されている時 THEN システムは環境変数の値を appsettings.json の値より優先する
3. WHEN Google OAuth 設定が読み込まれる時 THEN システムは GOOGLE_OAUTH_CLIENT_ID と GOOGLE_OAUTH_CLIENT_SECRET を使用する
4. WHEN リダイレクト URI が構成される時 THEN システムは現在のホスト（localhost:44396）を基準に URI を生成する
5. IF 必要な設定値が不足している場合 THEN システムは起動時に適切なエラーメッセージを表示する

### 要件 9: MAUI クライアント対応

**ユーザーストーリー:** MAUI アプリケーションのユーザーとして、ソーシャルアカウントを使用してログインしたい。これにより、モバイルデバイスやデスクトップで一貫した認証体験を得られる。

#### 受け入れ基準

1. WHEN MAUI アプリのログイン画面にアクセスした時 THEN システムは Google ログインボタンを表示する
2. WHEN ユーザーが Google ログインボタンをタップした時 THEN システムはシステムブラウザーで Google 認証ページを開く
3. WHEN 外部認証が完了した時 THEN システムは MAUI アプリにリダイレクトしてログイン状態にする
4. WHEN MAUI アプリがバックグラウンドから復帰した時 THEN システムは認証状態を適切に処理する
5. IF ネットワーク接続が不安定な場合 THEN システムは適切なエラーハンドリングを提供する
6. WHEN ユーザーがログアウトした時 THEN システムはローカルの認証情報をクリアする

### 要件 10: WinUI クライアント対応

**ユーザーストーリー:** WinUI アプリケーションのユーザーとして、Google アカウントを使用してログインしたい。これにより、Windows 環境で最適化された認証体験を得られる。

#### 受け入れ基準

1. WHEN WinUI アプリのログイン画面にアクセスした時 THEN システムは Google ログインボタンを表示する
2. WHEN ユーザーが Google ログインボタンをクリックした時 THEN システムは WebView2 またはシステムブラウザーで Google 認証ページを開く
3. WHEN Google 認証が完了した時 THEN システムは WinUI アプリにリダイレクトしてログイン状態にする
4. WHEN WinUI アプリが最小化/復元された時 THEN システムは認証状態を維持する
5. WHEN ユーザーがログアウトした時 THEN システムはローカルの認証情報とキャッシュをクリアする

### 要件 11: クライアント・サーバー認証連携

**ユーザーストーリー:** 開発者として、クライアントアプリケーション（MAUI/WinUI）とサーバー（ABP Framework）間で一貫した認証フローを実現したい。

#### 受け入れ基準

1. WHEN クライアントアプリが起動した時 THEN システムは ABP Framework 認証サーバーとの接続を確認する
2. WHEN Google 認証が完了した時 THEN システムは ABP Framework の JWT トークンを取得する
3. WHEN クライアントが API 呼び出しを行う時 THEN システムは有効な JWT トークンを使用する
4. WHEN トークンが期限切れになった時 THEN システムは自動的にリフレッシュトークンを使用して更新する
5. IF リフレッシュトークンも期限切れの場合 THEN システムはユーザーに再ログインを求める
6. WHEN 複数のクライアントアプリが同じアカウントでログインした時 THEN システムは一貫した認証状態を維持する

### 要件 12: オフライン対応とキャッシュ

**ユーザーストーリー:** モバイルユーザーとして、一時的にネットワーク接続が不安定でも基本的な機能を使用したい。

#### 受け入れ基準

1. WHEN ネットワーク接続が利用できない時 THEN システムはキャッシュされた認証情報を使用する
2. WHEN オフライン状態でアプリを起動した時 THEN システムは最後の認証状態を復元する
3. WHEN ネットワークが復旧した時 THEN システムは認証状態を自動的に同期する
4. IF キャッシュされた認証情報が期限切れの場合 THEN システムはネットワーク復旧時に再認証を求める
5. WHEN セキュリティ上の理由でキャッシュをクリアする必要がある時 THEN システムは適切にローカルデータを削除する
