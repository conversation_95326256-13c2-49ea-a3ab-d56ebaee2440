# TranscriptCleaner (.NET MAUI版)

日本語

## 概要

Microsoft Teams の自動生成トランスクリプト（議事録）は、しばしば漏字・脱字や誤字が含まれます。本ツール「TranscriptCleaner」は、アップロードされた議事録テキストと誤字脱字一覧（CSV）をもとに、OpenAI API を活用して高精度な訂正を行う **クロスプラットフォーム アプリケーション**です。

**🎯 開発完成度: 90% - 商用利用レベル**

**.NET MAUI版の特徴:**
- 🖥️ **クロスプラットフォーム対応**: Windows、macOS、iOS、Androidで動作
- 🚀 **.NET MAUI**: 最新の.NETマルチプラットフォームフレームワーク
- 🔒 **SQL Server統合**: 企業環境に適したエンタープライズデータベース
- 🤖 **AI駆動の高精度訂正**: OpenAI GPTモデルによる自然な文章訂正
- 👥 **ユーザー管理**: ロールベースアクセス制御による安全な運用
- 🔐 **権限管理**: 細かい権限設定による柔軟なアクセス制御
- 📊 **使用状況追跡**: コストとトークン使用量の詳細な記録
- 🎯 **多機能対応**: 誤字脱字修正、文法訂正、要約機能

---

## 主な機能

### 📝 トランスクリプト訂正機能

#### 1. 誤字脱字修正モード
- CSVファイルで定義された単語集を使用して、よくある誤字脱字を自動修正
- カスタム単語リストの編集・管理機能
- Excel風のグリッド表示による直感的な編集

#### 2. 文法訂正モード
- OpenAI APIを使用して文法的な改善を実施
- 自然な日本語表現への変換
- ビジネス文書に適した文体調整

#### 3. 要約モード
- 長い議事録を要点を抑えた要約に変換
- 重要なポイントを見逃さない構造化された要約
- 会議の決定事項と次のアクションを明確化

### ⚙️ 管理機能（管理者専用）

#### 1. ユーザー管理
- **ユーザーアカウント管理**: 作成・編集・削除・有効化/無効化
- **ロール割り当て**: 各ユーザーに適切な権限レベルを設定
- **ステータス監視**: アクティブ/非アクティブユーザーの管理
- **一覧表示**: 検索・フィルタリング機能付きのユーザー一覧
- **ドロワー式UI**: 直感的なナビゲーションによる管理画面

#### 2. ロール管理
- **システムロール**: ADMIN、USER、GUEST、PREMIUMの事前定義ロール
- **カスタムロール**: 組織のニーズに応じたカスタムロール作成
- **権限設定**: 機能レベルでの細かい権限制御
- **ユーザー数表示**: 各ロールに割り当てられたユーザー数の確認
- **分割画面**: 上半分でユーザー管理、下半分でロール管理

#### 3. 権限制御システム
- **ロールベースアクセス制御（RBAC）**: ABP Framework + ASP.NET Core Identityによる堅牢な認証・認可
- **機能レベル権限**: ユーザー管理、システム設定、データアクセス等の細かい制御
- **セッション管理**: 安全なログイン状態の維持と自動ログアウト
- **API保護**: 管理者専用エンドポイントの適切な保護

---

## 操作手順

本ツール「TranscriptCleaner」を使用して議事録を訂正する基本的な流れは以下の通りです。

1. **アプリケーションの起動:**

   * バックエンド（ABP Framework）を起動します。
     - コマンドラインで `cd TranscriptCleanerMvc` → `dotnet run --project src/TranscriptCleanerMvc.Web` を実行します。
   * フロントエンド（.NET MAUI）を起動します。
     - `frontend-maui/TranscriptCleaner.Maui` ディレクトリで `dotnet run -f net9.0-windows10.0.19041.0` を実行します。
2. **APIキーの設定:**

   * 環境変数でOpenAI APIキーを設定します：`export OPENAI_API_KEY="your-api-key"`
3. **処理モードの選択:**

   * 画面上で「誤字脱字修正」「文法訂正」「要約」などの処理モードを選択します。
4. **議事録ファイルのアップロード:**

   * UIのファイルアップロードボタンから、訂正したいトランスクリプトのテキストファイルをアップロードします。
5. **（誤字脱字修正モードの場合）誤字脱字リストの準備:**

   * CSVファイルをアップロード、またはテキストエリアで直接編集できます。
6. **モデルとカスタムプロンプトの設定（任意）:**

   * 使用するAIモデルや追加指示を設定できます。
7. **訂正の実行:**

   * 「訂正実行」ボタンをクリックし、処理を開始します。
8. **結果の確認と手修正:**

   * 訂正後のテキストが表示され、必要に応じて手修正が可能です。
   * 差分表示機能も利用できます。
9. **（任意）訂正結果を元に再訂正:**

   * 訂正後テキストを再度訂正前として利用できます。
10. **最終結果のダウンロード:**

    * 訂正済みの議事録をダウンロードできます。

**コストについて:**
OpenAI APIの利用にはコストが発生します。画面上で使用状況やコスト履歴を確認できます。

---

## 技術スタック

### フロントエンド (.NET MAUI)
- **.NET MAUI**: マルチプラットフォームアプリケーションフレームワーク
- **XAML**: 宣言的UI記述言語
- **C# 12**: 最新のC#言語機能
- **MVVM パターン**: Model-View-ViewModel アーキテクチャ
- **CommunityToolkit.Mvvm**: MVVM実装の簡素化
- **Microsoft.Extensions.DependencyInjection**: 依存性注入
- **System.Text.Json**: JSON処理
- **HttpClient**: HTTP通信

### バックエンド (.NET Web API)
- **ABP Framework 9.2**: .NET アプリケーションフレームワーク
- **ASP.NET Core 9.0**: 高性能Webフレームワーク
- **Entity Framework Core**: ORM（Object-Relational Mapping）
- **ASP.NET Core Identity**: 認証・認可システム
- **SQL Server**: エンタープライズデータベース
- **OpenAI API**: AI駆動のテキスト処理
  - OpenAI .NET SDK
- **AutoMapper**: オブジェクトマッピング
- **Serilog**: 構造化ログ記録

### データベース設計 (SQL Server)
```sql
-- 主要テーブル（ABP Framework標準テーブル + カスタムテーブル）
AbpUsers                -- ユーザー管理（ABP標準）
AbpRoles                -- ロール定義（ABP標準）
AbpUserRoles           -- ユーザー・ロール関連（ABP標準）
CorrectionHistories    -- 訂正履歴とコスト追跡
WordListHistories      -- 単語リスト履歴
```

---

## アーキテクチャ

```
┌─────────────────┐    HTTP/REST    ┌─────────────────────┐
│   Frontend      │ ◄──────────────► │     Backend         │
│   (.NET MAUI)   │                 │ (ABP Framework +    │
│                 │                 │  TranscriptCleanerMvc)│
│ ├─ User Mgmt    │                 │ ├─ Auth Service     │
│ ├─ Role Mgmt    │                 │ ├─ User Service     │
│ ├─ Transcript   │                 │ ├─ Role Service     │
│ └─ Admin Panel  │                 │ └─ OpenAI API       │
└─────────────────┘                 └─────────────────────┘
                                              │
                                              ▼
                                    ┌─────────────────┐
                                    │   SQL Server    │
                                    │   Database      │
                                    └─────────────────┘
```

---

## フォルダ構成

```
TranscriptCleanerWin/
├── TranscriptCleanerMvc/          # ABP Framework バックエンド
│   ├── src/
│   │   ├── TranscriptCleanerMvc.Web/
│   │   ├── TranscriptCleanerMvc.Application/
│   │   ├── TranscriptCleanerMvc.Domain/
│   │   ├── TranscriptCleanerMvc.Domain.Shared/
│   │   ├── TranscriptCleanerMvc.EntityFrameworkCore/
│   │   └── TranscriptCleanerMvc.HttpApi/
│   └── test/
├── frontend-maui/
│   └── TranscriptCleaner.Maui/    # .NET MAUI クライアント
├── frontend-winui/
│   └── TranscriptCleaner.WinUI/   # .NET WinUI クライアント
├── scripts/                       # ユーティリティスクリプト
├── CLAUDE.md
└── README.md
```

---

## 要件整理

### 基本要件

- 入力：トランスクリプト（テキスト）、訂正単語集（CSV）
- 出力：訂正済み議事録
- UI：ファイルアップロード、訂正実行、結果表示・ダウンロード
- 訂正方法：OpenAI API ＋単語集
- セットアップ・実行手順・API キー設定方法・サンプルデータ利用方法も含めること

### モジュール要件

- フロントエンドとバックエンドの分離
- REST APIによる通信
- 明確なデータ構造定義（例：JSON）
- テストコード実装：重要ロジックにはユニットテスト

---

## セットアップ手順

### 1. 前提条件
- **.NET 9.0 SDK**: 最新の.NET開発キット
- **Visual Studio 2022**: 推奨IDE（またはVS Code + C# Dev Kit）
- **SQL Server**: Express版以上（LocalDB可）
- **SQL Server Management Studio (SSMS)**: データベース管理ツール
- **OpenAI API キー**: AI機能の利用
- **Git**: バージョン管理

### 2. データベースセットアップ
```powershell
# SQL Server LocalDBを使用する場合
sqllocaldb create MSSQLLocalDB
sqllocaldb start MSSQLLocalDB

# データベース作成（SSMSまたはsqlcmdで実行）
CREATE DATABASE TranscriptCleaner
COLLATE Japanese_CI_AS;

# Entity Framework Coreによるマイグレーション実行
dotnet ef database update
```

### 3. バックエンドセットアップ
```powershell
cd TranscriptCleanerMvc

# appsettings.jsonでデータベース接続情報を設定
# "ConnectionStrings": {
#   "Default": "Server=(localdb)\\MSSQLLocalDB;Database=TranscriptCleaner;Trusted_Connection=true;MultipleActiveResultSets=true"
# }

# 環境変数でOpenAI APIキーを設定（セキュリティ重要）
$env:OPENAI_API_KEY="your-openai-api-key-here"

# NuGetパッケージの復元
dotnet restore

# データベースマイグレーション
dotnet ef database update

# アプリケーション起動
dotnet run --project src/TranscriptCleanerMvc.Web
```

**⚠️ セキュリティ注意事項:**
- OpenAI APIキーは必ず環境変数で設定してください
- APIキーをソースコードに直接記述しないでください
- 本番環境では適切な環境変数管理システムを使用してください
- データベースパスワードも環境変数での管理を推奨します

### 4. MAUIアプリケーションセットアップ
```powershell
cd frontend-maui/TranscriptCleaner.Maui

# NuGetパッケージの復元
dotnet restore

# Windows向けビルド
dotnet build -f net9.0-windows10.0.19041.0

# アプリケーション実行
dotnet run -f net9.0-windows10.0.19041.0
```

### 5. 初期ユーザー
以下のユーザーでログインできます：
- **管理者**: `admin` / `admin123`
- **一般ユーザー**: `testuser` / `test123`
- **ゲスト**: `demo` / `demo123`

**⚠️ セキュリティ注意事項:**
- 本番環境では必ず初期パスワードを変更してください
- 強力なパスワードポリシーを設定してください

---

## 実行方法

1. **SQL Serverの起動**: LocalDBまたはSQL Server Expressを起動
2. **バックエンド（ABP Framework）の起動**: `cd TranscriptCleanerMvc` → `dotnet run --project src/TranscriptCleanerMvc.Web`でWeb APIを起動
3. **MAUIアプリケーションの実行**: Visual Studioから実行またはコマンドラインから`dotnet run -f net9.0-windows10.0.19041.0`
4. **接続確認**: アプリケーション起動時にバックエンドAPIとの接続を自動確認
5. **操作開始**: UIからファイルアップロードや訂正実行などを操作

---

## UI 詳細要件

### 基本レイアウト
- **ドロワー式サイドバー**: 左側に設定・操作パネル
- **メインコンテンツエリア**: 右側に結果表示・編集エリア
- **管理機能**: 上半分ユーザー管理、下半分ロール管理の分割表示

### サイドバー機能
- **処理モード選択**: 誤字脱字修正、文法訂正、要約モードの切り替え
- **OpenAI モデル選択**: gpt-4o, gpt-4-turbo, gpt-4, gpt-3.5-turbo等の選択
- **カスタムプロンプト入力**: 処理モードに応じた追加指示
- **ファイルアップロード**:
  - 議事録テキストファイル（TXT形式）
  - 誤字脱字一覧（CSV形式）
- **単語リスト編集**: CSV形式での直接編集（プレースホルダ：「誤,正」）
- **訂正実行ボタン**: ファイルアップロード完了後に有効化

### メインコンテンツ機能
- **2列表示**: 左列（訂正前・編集不可）、右列（訂正後・編集可能）
- **差分表示**: チェックボックスで詳細差分の表示/非表示切り替え
- **操作ボタン**:
  - 訂正後テキストを訂正前にコピー
  - 最終修正結果のダウンロード
- **再訂正対応**: 複数回のOpenAI API呼び出しに対応

---

## 構造設計

- フロントエンド（.NET MAUI）：
  - ファイルアップロード、設定、実行ボタン、結果表示、差分表示、ダウンロードなどのUI
  - REST APIクライアント
- バックエンド（ABP Framework / TranscriptCleanerMvc）：
  - ファイル受信、OpenAI API連携、訂正処理、CSV解析、コスト管理などの業務ロジック
  - REST APIエンドポイント
  - ドメイン駆動設計（DDD）アーキテクチャ

---

## 開発ロードマップ

### ✅ 完了済み機能
- ✅ **基本的な議事録訂正機能**: 誤字脱字修正、文法訂正、要約モード
- ✅ **ユーザー認証システム**: ASP.NET Core Identity による安全な認証
- ✅ **ロールベースアクセス制御**: 細かい権限管理システム
- ✅ **ユーザー管理機能**: 管理者による包括的なユーザー管理
- ✅ **ロール管理機能**: カスタムロールと権限設定
- ✅ **ドロワー式UI**: 直感的なナビゲーションシステム
- ✅ **REST API基盤**: フロントエンドとバックエンドの基本的な連携
- ✅ **データベース設計**: ユーザー、ロール、履歴管理のテーブル構造
- ✅ **OpenAI API統合**: GPTモデルによるテキスト処理
- ✅ **CSV処理機能**: 誤字脱字リストの管理
- ✅ **セキュリティ設定**: CORS、認証、認可の基本設定

### 🚧 開発中・計画中
- 🔄 **フロントエンド・バックエンド完全統合**: 全機能の連携テスト
- 🔄 **差分表示機能**: 訂正前後の詳細な比較表示
- 📊 **統計・レポート機能**: 使用状況とコスト分析ダッシュボード
- 🔔 **通知システム**: 処理完了やエラーの通知機能
- 📱 **レスポンシブデザイン**: モバイル対応の改善
- 🌐 **多言語対応**: 英語・日本語以外の言語サポート
- 🔒 **セキュリティ強化**: 2FA、監査ログ、セッション管理の改善
- 🧪 **テストコード**: ユニットテスト・統合テストの充実

### 🎯 将来の拡張計画
- **バッチ処理機能**: 大量ファイルの一括処理
- **API外部連携**: Teams、Slack等との直接連携
- **カスタムAIモデル**: 組織専用の訂正モデル学習
- **ワークフロー機能**: 承認プロセスと段階的処理
- **クラウド対応**: AWS/Azure等でのスケーラブル運用

---

## ライセンス

このプロジェクトはMITライセンスの下で公開されています。

## トラブルシューティング

### よくある問題

1. **データベース接続エラー**
   - SQL Serverサーバーが起動していることを確認
   - `appsettings.json`の接続情報を確認
   - データベースの作成とマイグレーションを確認

2. **OpenAI API エラー**
   - 環境変数`OPENAI_API_KEY`が正しく設定されているか確認
   - APIキーの有効性を確認
   - API利用制限に達していないか確認

3. **MAUI アプリケーション起動エラー**
   - .NET 9 SDKがインストールされていることを確認
   - Visual Studio 2022の最新版を使用していることを確認
   - 必要なワークロードがインストールされていることを確認

4. **CORS エラー**
   - バックエンドが指定ポートで起動していることを確認
   - `appsettings.json`のCORS設定を確認

## 貢献

プルリクエストやイシューの報告を歓迎します。開発に参加される場合は、以下のガイドラインに従ってください：

1. フォークしてブランチを作成
2. 変更を実装してテストを追加
3. コミットメッセージは明確に記述
4. プルリクエストを作成

## サポート

質問や問題がある場合は、GitHubのIssuesページでお知らせください。

---

## 📚 ドキュメント

- [📖 プロジェクト概要](./README.md) - このファイル
- [🌐 English Documentation](./CLAUDE.md) - English version