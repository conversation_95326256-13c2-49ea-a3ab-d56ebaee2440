@page "/admin/settings"
@model TranscriptCleanerMvc.Web.Pages.Admin.SettingsModel
@{
    ViewData["Title"] = "管理者設定";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs"></i>
                        管理者設定
                    </h3>
                </div>
                <div class="card-body">
                    <!-- タブナビゲーション -->
                    <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="google-auth-tab" data-bs-toggle="tab" data-bs-target="#google-auth" type="button" role="tab">
                                <i class="fab fa-google"></i>
                                Google認証
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                                <i class="fas fa-server"></i>
                                システム設定
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="statistics-tab" data-bs-toggle="tab" data-bs-target="#statistics" type="button" role="tab">
                                <i class="fas fa-chart-bar"></i>
                                統計情報
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">
                                <i class="fas fa-history"></i>
                                変更履歴
                            </button>
                        </li>
                    </ul>

                    <!-- タブコンテンツ -->
                    <div class="tab-content" id="settingsTabContent">
                        <!-- Google認証設定 -->
                        <div class="tab-pane fade show active" id="google-auth" role="tabpanel">
                            <div class="mt-3">
                                <form id="googleAuthForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="googleAuthEnabled">
                                                    <label class="form-check-label" for="googleAuthEnabled">
                                                        Google認証を有効にする
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="form-group mb-3">
                                                <label for="googleClientId" class="form-label">クライアントID</label>
                                                <input type="text" class="form-control" id="googleClientId" placeholder="Google Cloud ConsoleのクライアントID">
                                            </div>

                                            <div class="form-group mb-3">
                                                <label for="googleClientSecret" class="form-label">クライアントシークレット</label>
                                                <input type="password" class="form-control" id="googleClientSecret" placeholder="Google Cloud Consoleのクライアントシークレット">
                                                <small class="form-text text-muted">セキュリティのため、既存の値はマスクされています</small>
                                            </div>

                                            <div class="form-group mb-3">
                                                <label for="googleRedirectUri" class="form-label">リダイレクトURI</label>
                                                <input type="url" class="form-control" id="googleRedirectUri" placeholder="https://yourdomain.com/signin-google">
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="googleScopes" class="form-label">スコープ</label>
                                                <textarea class="form-control" id="googleScopes" rows="3" placeholder="openid,email,profile"></textarea>
                                                <small class="form-text text-muted">カンマ区切りで入力してください</small>
                                            </div>

                                            <div class="form-group mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="googleAutoCreateAccount">
                                                    <label class="form-check-label" for="googleAutoCreateAccount">
                                                        自動アカウント作成
                                                    </label>
                                                </div>
                                                <small class="form-text text-muted">初回ログイン時に自動でアカウントを作成します</small>
                                            </div>

                                            <div class="form-group mb-3">
                                                <label for="googleDefaultRoles" class="form-label">デフォルトロール</label>
                                                <input type="text" class="form-control" id="googleDefaultRoles" placeholder="user,member">
                                                <small class="form-text text-muted">新規ユーザーに付与するロール（カンマ区切り）</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i>
                                            Google認証設定を保存
                                        </button>
                                        <button type="button" class="btn btn-secondary ms-2" onclick="loadGoogleAuthSettings()">
                                            <i class="fas fa-undo"></i>
                                            リセット
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- システム設定 -->
                        <div class="tab-pane fade" id="system" role="tabpanel">
                            <div class="mt-3">
                                <form id="systemSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="sessionTimeout" class="form-label">セッションタイムアウト（分）</label>
                                                <input type="number" class="form-control" id="sessionTimeout" min="5" max="1440" value="30">
                                            </div>

                                            <div class="form-group mb-3">
                                                <label for="maxConcurrentSessions" class="form-label">最大同時セッション数</label>
                                                <input type="number" class="form-control" id="maxConcurrentSessions" min="1" max="20" value="5">
                                            </div>

                                            <div class="form-group mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="passwordPolicyEnabled">
                                                    <label class="form-check-label" for="passwordPolicyEnabled">
                                                        パスワードポリシーを有効にする
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="form-group mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="requireTwoFactorAuth">
                                                    <label class="form-check-label" for="requireTwoFactorAuth">
                                                        2要素認証を必須にする
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="logRetentionDays" class="form-label">ログ保持期間（日）</label>
                                                <input type="number" class="form-control" id="logRetentionDays" min="1" max="365" value="90">
                                            </div>

                                            <div class="form-group mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                                    <label class="form-check-label" for="maintenanceMode">
                                                        メンテナンスモード
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="form-group mb-3">
                                                <label for="maintenanceMessage" class="form-label">メンテナンスメッセージ</label>
                                                <textarea class="form-control" id="maintenanceMessage" rows="3" placeholder="システムメンテナンス中です。しばらくお待ちください。"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i>
                                            システム設定を保存
                                        </button>
                                        <button type="button" class="btn btn-secondary ms-2" onclick="loadSystemSettings()">
                                            <i class="fas fa-undo"></i>
                                            リセット
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 統計情報 -->
                        <div class="tab-pane fade" id="statistics" role="tabpanel">
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 id="totalUsers">-</h4>
                                                        <p class="mb-0">総ユーザー数</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-users fa-2x"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 id="googleLinkedUsers">-</h4>
                                                        <p class="mb-0">Google連携ユーザー</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fab fa-google fa-2x"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 id="todayLogins">-</h4>
                                                        <p class="mb-0">今日のログイン</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-sign-in-alt fa-2x"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 id="todayErrors">-</h4>
                                                        <p class="mb-0">今日のエラー</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title">週間ログイン数</h5>
                                            </div>
                                            <div class="card-body">
                                                <p>今週: <span id="weeklyLogins" class="badge bg-primary">-</span></p>
                                                <p>今月: <span id="monthlyLogins" class="badge bg-success">-</span></p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title">アクティブセッション</h5>
                                            </div>
                                            <div class="card-body">
                                                <p>現在のアクティブセッション: <span id="activeSessions" class="badge bg-info">-</span></p>
                                                <p>最終更新: <span id="lastUpdated" class="text-muted">-</span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mt-3">
                                    <button type="button" class="btn btn-primary" onclick="loadStatistics()">
                                        <i class="fas fa-sync"></i>
                                        統計情報を更新
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 変更履歴 -->
                        <div class="tab-pane fade" id="history" role="tabpanel">
                            <div class="mt-3">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="historyTable">
                                        <thead>
                                            <tr>
                                                <th>設定名</th>
                                                <th>変更前</th>
                                                <th>変更後</th>
                                                <th>変更者</th>
                                                <th>変更日時</th>
                                                <th>理由</th>
                                                <th>IPアドレス</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- 動的に生成 -->
                                        </tbody>
                                    </table>
                                </div>

                                <div class="form-group mt-3">
                                    <button type="button" class="btn btn-primary" onclick="loadHistory()">
                                        <i class="fas fa-sync"></i>
                                        履歴を更新
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成功/エラーメッセージ用のトースト -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert">
        <div class="toast-header bg-success text-white">
            <i class="fas fa-check-circle me-2"></i>
            <strong class="me-auto">成功</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="successMessage">
        </div>
    </div>

    <div id="errorToast" class="toast" role="alert">
        <div class="toast-header bg-danger text-white">
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong class="me-auto">エラー</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="errorMessage">
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/admin-settings.js"></script>
}