using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace TranscriptCleanerMvc.Entities;

public class WordList : FullAuditedEntity<Guid>
{
    [Required]
    [MaxLength(200)]
    public string IncorrectWord { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(200)]
    public string CorrectWord { get; set; } = string.Empty;
    
    [MaxLength(10)]
    public string Language { get; set; } = "ja";
    
    [MaxLength(50)]
    public string Category { get; set; } = "general";
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public int UsageCount { get; set; } = 0;
    
    public DateTime? LastUsedAt { get; set; }
    
    protected WordList()
    {
    }
    
    public WordList(
        Guid id,
        string incorrectWord,
        string correctWord,
        string language = "ja",
        string category = "general",
        string? description = null
    ) : base(id)
    {
        IncorrectWord = incorrectWord.Trim();
        CorrectWord = correctWord.Trim();
        Language = language;
        Category = category;
        Description = description;
        IsActive = true;
        UsageCount = 0;
    }
    
    public void IncrementUsage()
    {
        UsageCount++;
        LastUsedAt = DateTime.UtcNow;
    }
    
    public void UpdateCorrection(string correctWord, string? description = null)
    {
        CorrectWord = correctWord.Trim();
        if (description != null)
        {
            Description = description;
        }
    }
    
    public void Activate()
    {
        IsActive = true;
    }
    
    public void Deactivate()
    {
        IsActive = false;
    }
}