using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace TranscriptCleaner.Maui.Services
{
    public class TranscriptCorrectionService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl = "https://localhost:44396"; // TranscriptCleanerMvc Backend URL

        public TranscriptCorrectionService()
        {
            // For development: Skip HTTPS certificate validation
            var handler = new HttpClientHandler();
            handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
            
            _httpClient = new HttpClient(handler);
            _httpClient.Timeout = TimeSpan.FromMinutes(5); // Long processing timeout
        }

        public async Task<string> CorrectTranscriptAsync(string originalText, string wordListContent = "")
        {
            try
            {
                // Send as JSON matching TranscriptCleanerMvc API specification
                var requestData = new
                {
                    Text = originalText,
                    Language = "ja",
                    Model = "gpt-4",
                    Mode = "comprehensive",
                    CustomPrompt = "Auto processing from MAUI app",
                    WordList = !string.IsNullOrWhiteSpace(wordListContent) ? wordListContent : null
                };

                var jsonContent = new StringContent(
                    JsonSerializer.Serialize(requestData),
                    Encoding.UTF8,
                    "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/manual/transcript/correct", jsonContent);

                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<TranscriptCorrectionResponse>(responseJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return result?.CorrectedText ?? "Could not retrieve correction result.";
                }
                else
                {
                    return $"Error occurred: {response.StatusCode}";
                }
            }
            catch (HttpRequestException)
            {
                // Return mock data if backend is not available
                return await GetMockCorrectionAsync(originalText);
            }
            catch (Exception ex)
            {
                return $"Error during processing: {ex.Message}";
            }
        }

        private async Task<string> GetMockCorrectionAsync(string originalText)
        {
            // Mock processing: Simulate actual AI correction
            await Task.Delay(2000); // Simulate processing time

            if (string.IsNullOrWhiteSpace(originalText))
                return "No text provided.";

            // Apply simple correction rules
            var corrected = originalText
                .Replace("um", "")
                .Replace("uh", "")
                .Replace("...", ".")
                .Replace(",,", ",")
                .Replace("  ", " ")
                .Trim();

            // Clean up line breaks properly
            var lines = corrected.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            var cleanedLines = lines.Select(line => line.Trim()).Where(line => !string.IsNullOrEmpty(line));

            var result = string.Join("\n", cleanedLines);

            return $"[AI Corrected - MAUI]\n{result}\n\nNote: This result is from mock processing. To use actual AI correction, please connect to the backend server.";
        }
    }

    public class TranscriptCorrectionResponse
    {
        public bool Success { get; set; }
        public string CorrectedText { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
        public int OriginalLength { get; set; }
        public int CorrectedLength { get; set; }
        public int ProcessingTime { get; set; }
    }
}