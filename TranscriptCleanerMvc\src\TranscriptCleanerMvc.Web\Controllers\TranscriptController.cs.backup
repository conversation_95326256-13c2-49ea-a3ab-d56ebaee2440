using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TranscriptCleanerMvc.Localization;
using TranscriptCleanerMvc.Transcripts;
using TranscriptCleanerMvc.Transcripts.Dtos;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace TranscriptCleanerMvc.Web.Controllers
{
    [ApiController]
    [Route("api/manual/transcript")]
    public class TranscriptController : ControllerBase
    {
        private readonly IStringLocalizer<TranscriptCleanerMvcResource> _localizer;
        private readonly ITranscriptAppService _transcriptAppService;
        private readonly ILogger<TranscriptController> _logger;

        public TranscriptController(
            IStringLocalizer<TranscriptCleanerMvcResource> localizer,
            ITranscriptAppService transcriptAppService,
            ILogger<TranscriptController> logger)
        {
            _localizer = localizer;
            _transcriptAppService = transcriptAppService;
            _logger = logger;
        }

        [HttpPost("correct")]
        [AllowAnonymous] // 開発時のみ - 本番では削除
        public async Task<IActionResult> CorrectTranscript([FromBody] TranscriptCorrectionRequestModel request)
        {
            try
            {
                _logger.LogInformation("Received correction request for text length: {TextLength}", 
                    request?.Text?.Length ?? 0);

                // リクエスト検証
                if (request == null)
                {
                    _logger.LogWarning("Request object is null");
                    return BadRequest(new { 
                        success = false, 
                        errorMessage = "Request data is missing" 
                    });
                }

                if (string.IsNullOrWhiteSpace(request.Text))
                {
                    var errorMsg = request.Language == "ja" 
                        ? "テキストが入力されていません。" 
                        : "No text provided.";
                    
                    _logger.LogWarning("Empty text provided");
                    return BadRequest(new { 
                        success = false, 
                        errorMessage = errorMsg 
                    });
                }

                // ABP TranscriptAppServiceを使用
                var correctionRequest = new TranscriptCorrectionRequestDto
                {
                    Text = request.Text,
                    Title = $"Correction_{DateTime.Now:yyyyMMdd_HHmmss}",
                    Language = request.Language ?? "ja",
                    CorrectionType = MapCorrectionType(request.Mode ?? "comprehensive"),
                    CustomPrompt = request.CustomPrompt,
                    WordList = request.WordList
                };

                _logger.LogInformation("Calling TranscriptAppService.CorrectTranscriptAsync");
                var result = await _transcriptAppService.CorrectTranscriptAsync(correctionRequest);

                _logger.LogInformation("Correction completed successfully. Processing time: {ProcessingTime}ms", 
                    result.ProcessingTimeMs);

                return Ok(new {
                    success = true,
                    correctedText = result.CorrectedText,
                    originalLength = result.OriginalLength,
                    correctedLength = result.CorrectedLength,
                    processingTime = result.ProcessingTimeMs,
                    cost = result.Cost
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during transcript correction");
                
                var errorMessage = request?.Language == "ja" 
                    ? $"処理中にエラーが発生しました: {ex.Message}" 
                    : $"An error occurred during processing: {ex.Message}";
                    
                return StatusCode(500, new { 
                    success = false, 
                    errorMessage = errorMessage
                });
            }
        }

        private string MapCorrectionType(string mode)
        {
            return mode?.ToLower() switch
            {
                "grammar" => "grammar",
                "formatting" => "formatting", 
                "comprehensive" => "comprehensive",
                "summarize" => "summarize",
                _ => "comprehensive"
            };
        }
    }

    // フロントエンド用のリクエストモデル
    public class TranscriptCorrectionRequestModel
    {
        [Required]
        public string Text { get; set; } = string.Empty;

        public string? Language { get; set; } = "ja";

        public string? Model { get; set; } = "gpt-4";

        public string? Mode { get; set; } = "comprehensive";

        public string? CustomPrompt { get; set; }

        public string? WordList { get; set; }
    }
}