using System;
using System.Threading.Tasks;
using Shouldly;
using Xunit;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Identity
{
    public class ExternalLoginServiceTests : TranscriptCleanerMvcApplicationTestBase<TranscriptCleanerMvcApplicationTestModule>
    {
        private readonly IExternalLoginService _externalLoginService;

        public ExternalLoginServiceTests()
        {
            _externalLoginService = GetRequiredService<IExternalLoginService>();
        }

        [Fact]
        public async Task HandleGoogleCallbackAsync_ValidCode_ShouldReturnResult()
        {
            // Arrange
            var code = "test_auth_code";
            var state = "test_state";

            // Act
            var result = await _externalLoginService.HandleGoogleCallbackAsync(code, state);

            // Assert
            result.ShouldNotBeNull();
        }

        [Fact]
        public async Task LinkGoogleAccountAsync_ValidParameters_ShouldReturnTrue()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var googleId = "google_test_123";

            // Act
            var result = await _externalLoginService.LinkGoogleAccountAsync(userId, googleId);

            // Assert
            result.ShouldBeOfType<bool>();
        }
    }
}