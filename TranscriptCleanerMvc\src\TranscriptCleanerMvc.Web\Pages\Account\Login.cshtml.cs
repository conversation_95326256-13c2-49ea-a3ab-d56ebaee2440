using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using Volo.Abp.Identity;
using Microsoft.Extensions.Localization;
using TranscriptCleanerMvc.Localization;

namespace TranscriptCleanerMvc.Web.Pages.Account
{
    public class LoginPageModel : PageModel
    {
        private readonly SignInManager<Volo.Abp.Identity.IdentityUser> _signInManager;
        private readonly UserManager<Volo.Abp.Identity.IdentityUser> _userManager;
        private readonly ILogger<LoginPageModel> _logger;
        private readonly IStringLocalizer<TranscriptCleanerMvcResource> _localizer;

        public LoginPageModel(
            SignInManager<Volo.Abp.Identity.IdentityUser> signInManager,
            UserManager<Volo.Abp.Identity.IdentityUser> userManager,
            ILogger<LoginPageModel> logger,
            IStringLocalizer<TranscriptCleanerMvcResource> localizer)
        {
            _signInManager = signInManager;
            _userManager = userManager;
            _logger = logger;
            _localizer = localizer;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public string? ErrorMessage { get; set; }

        public string? ReturnUrl { get; set; }

        public class InputModel
        {
            [Required(ErrorMessage = "PleaseEnterUserName")]
            [Display(Name = "UserNameOrEmailAddress")]
            public string UserNameOrEmailAddress { get; set; } = string.Empty;

            [Required(ErrorMessage = "PleaseEnterPassword")]
            [DataType(DataType.Password)]
            [Display(Name = "Password")]
            public string Password { get; set; } = string.Empty;

            [Display(Name = "RememberMe")]
            public bool RememberMe { get; set; }
        }

        public async Task OnGetAsync(string? returnUrl = null)
        {
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                ModelState.AddModelError(string.Empty, ErrorMessage);
            }

            returnUrl ??= Url.Content("~/");

            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

            ReturnUrl = returnUrl;
        }

        public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
        {
            returnUrl ??= Url.Content("~/");

            if (ModelState.IsValid)
            {
                // Try to find user by username or email
                var user = await _userManager.FindByNameAsync(Input.UserNameOrEmailAddress) ??
                          await _userManager.FindByEmailAsync(Input.UserNameOrEmailAddress);

                if (user != null)
                {
                    var result = await _signInManager.PasswordSignInAsync(
                        user.UserName!, 
                        Input.Password, 
                        Input.RememberMe, 
                        lockoutOnFailure: false);

                    if (result.Succeeded)
                    {
                        _logger.LogInformation("User {UserName} logged in.", user.UserName);
                        return LocalRedirect(returnUrl);
                    }
                    
                    if (result.RequiresTwoFactor)
                    {
                        return RedirectToPage("./LoginWith2fa", new { ReturnUrl = returnUrl, RememberMe = Input.RememberMe });
                    }
                    
                    if (result.IsLockedOut)
                    {
                        _logger.LogWarning("User {UserName} account locked out.", user.UserName);
                        ErrorMessage = _localizer["AccountLocked"];
                    }
                    else
                    {
                        ErrorMessage = _localizer["InvalidUserNameOrPassword"];
                    }
                }
                else
                {
                    ErrorMessage = _localizer["InvalidUserNameOrPassword"];
                }
            }

            // If we got this far, something failed, redisplay form
            return Page();
        }
    }
}