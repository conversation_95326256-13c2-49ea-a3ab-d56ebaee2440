﻿{
  "applications": {
    "TranscriptCleanerMvc.Web": {
      "type": "dotnet-project",
      "launchUrl": "https://localhost:44396",
      "path": "../../../src/TranscriptCleanerMvc.Web/TranscriptCleanerMvc.Web.csproj",
      "kubernetesService": ".*-web$",
      "healthCheckEndpoint": "/health-status",
      "healthUiEndpoint": "/health-ui",
      "execution": {
        "order": 2
      }
    }
  },
  "containers": {
    "serviceName": "TranscriptCleanerMvc-Containers",
  }
}