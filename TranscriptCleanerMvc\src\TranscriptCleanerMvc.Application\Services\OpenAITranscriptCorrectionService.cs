using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using OpenAI;
using OpenAI.Chat;
using TranscriptCleanerMvc.Entities;
using TranscriptCleanerMvc.Repositories;
using Volo.Abp.DependencyInjection;

namespace TranscriptCleanerMvc.Services;

public class OpenAITranscriptCorrectionService : ITransientDependency
{
    private readonly ILogger<OpenAITranscriptCorrectionService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IWordListRepository _wordListRepository;
    private readonly OpenAIClient? _openAIClient;

    public OpenAITranscriptCorrectionService(
        ILogger<OpenAITranscriptCorrectionService> logger,
        IConfiguration configuration,
        IWordListRepository wordListRepository)
    {
        _logger = logger;
        _configuration = configuration;
        _wordListRepository = wordListRepository;
        
        // 環境変数OPENAI_API_KEYからAPIキーを取得
        var apiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY");
        
        if (!string.IsNullOrEmpty(apiKey))
        {
            _openAIClient = new OpenAIClient(apiKey);
            _logger.LogInformation("OpenAI client initialized successfully");
        }
        else
        {
            _logger.LogWarning("OpenAI API key not found. Set OPENAI_API_KEY environment variable to enable AI corrections");
        }
    }

    public async Task<(string correctedText, int processingTimeMs, decimal? cost)> CorrectTextAsync(
        string originalText,
        string language = "ja",
        string correctionType = "comprehensive",
        string? customPrompt = null,
        string? wordListCsv = null)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (_openAIClient == null)
            {
                _logger.LogWarning("OpenAI API key not configured, using fallback correction");
                return await FallbackCorrectionAsync(originalText, language, stopwatch);
            }

            var correctedText = await CallOpenAIAsync(originalText, language, correctionType, customPrompt, wordListCsv);
            var processingTime = (int)stopwatch.ElapsedMilliseconds;
            var cost = CalculateCost(originalText, correctedText);
            
            _logger.LogInformation("OpenAI correction completed in {ProcessingTime}ms", processingTime);
            
            return (correctedText, processingTime, cost);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during OpenAI correction, falling back to dictionary-based correction");
            return await FallbackCorrectionAsync(originalText, language, stopwatch);
        }
    }

    private async Task<string> CallOpenAIAsync(string text, string language, string correctionType, string? customPrompt = null, string? wordListCsv = null)
    {
        try
        {
            var systemPrompt = GenerateSystemPrompt(language, correctionType, customPrompt, wordListCsv);

            var chatCompletion = await _openAIClient!.GetChatClient("gpt-4o-mini").CompleteChatAsync(
                [
                    new SystemChatMessage(systemPrompt),
                    new UserChatMessage(text)
                ],
                new ChatCompletionOptions
                {
                    Temperature = 0,
                    TopP = 1,
                    PresencePenalty = 0,
                    FrequencyPenalty = 0,
                    MaxOutputTokenCount = Math.Min(8192, text.Length * 3)
                });

            var result = chatCompletion.Value.Content.FirstOrDefault()?.Text ?? text;
            _logger.LogDebug("OpenAI API call successful. Input length: {InputLength}, Output length: {OutputLength}", 
                text.Length, result.Length);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OpenAI API call failed. Falling back to dictionary-based correction");
            throw; // Re-throw to trigger fallback in calling method
        }
    }

    private async Task<(string correctedText, int processingTimeMs, decimal? cost)> FallbackCorrectionAsync(
        string originalText, 
        string language, 
        Stopwatch stopwatch)
    {
        var wordList = await _wordListRepository.GetActiveByLanguageAsync(language);
        var correctedText = originalText;

        foreach (var word in wordList)
        {
            correctedText = correctedText.Replace(word.IncorrectWord, word.CorrectWord);
            word.IncrementUsage();
            await _wordListRepository.UpdateAsync(word);
        }

        var processingTime = (int)stopwatch.ElapsedMilliseconds;
        
        return (correctedText, processingTime, null);
    }

    private string GenerateSystemPrompt(string language, string correctionType, string? customPrompt = null, string? wordListCsv = null)
    {
        var systemContent = "";
        var correctionWords = ParseWordListCsv(wordListCsv);

        // 処理モードに応じたプロンプト生成
        switch (correctionType.ToLower())
        {
            case "misspelling":
            case "comprehensive":
                systemContent = GetStrictSystemPrompt(correctionWords);
                break;
            case "grammar":
                systemContent = GetGrammarSystemPrompt(correctionWords);
                break;
            case "summarize":
                systemContent = GetSummarizeSystemPrompt();
                break;
            default:
                systemContent = GetStrictSystemPrompt(correctionWords);
                break;
        }

        // カスタムプロンプトを追加
        if (!string.IsNullOrWhiteSpace(customPrompt))
        {
            if (correctionType.ToLower() == "summarize")
            {
                systemContent += $"\n\nユーザーからの具体的な指示: {customPrompt}";
            }
            else
            {
                systemContent += $"\n\nユーザー補足: {customPrompt}";
            }
        }

        return systemContent.Trim();
    }

    private string GetStrictSystemPrompt(List<(string incorrect, string correct)> correctionWords)
    {
        var prompt = "あなたは誤字脱字訂正専用のAIです。絶対に要約、追加、削除、言い換え、書式変更、文体変更、内容の修正、段落の統合・分割、語順の変更、その他の編集は行わず、" +
                    "誤字脱字リストに基づく訂正のみを行ってください。誤字脱字リストに該当しない箇所は一切変更せず、元のテキストをそのまま残してください。" +
                    "訂正リストにない箇所は絶対に修正しないでください。人間による最終確認・修正が必須です。";

        if (correctionWords.Any())
        {
            prompt += "\n\n以下の誤字脱字リストを優先的に適用してください：\n";
            foreach (var word in correctionWords)
            {
                prompt += $"「{word.incorrect}」→「{word.correct}」\n";
            }
        }

        return prompt;
    }

    private string GetGrammarSystemPrompt(List<(string incorrect, string correct)> correctionWords)
    {
        var prompt = "あなたは日本語の文章を自然で文法的に正しく校正するAIです。\n" +
                    "以下の指示に従って、提供されたテキストを修正してください。\n" +
                    "1. 文法的な誤りを修正します。\n" +
                    "2. 不自然な表現をより自然な日本語に修正します。\n" +
                    "3. 誤字脱字があれば修正します。";

        if (correctionWords.Any())
        {
            prompt += "\n特に、以下のリストにある単語は優先的に修正してください：\n";
            foreach (var word in correctionWords)
            {
                prompt += $"「{word.incorrect}」→「{word.correct}」\n";
            }
        }

        prompt += "\n4. 元の文章の意味や主要な情報を保持し、勝手に内容を追加したり削除したりしないでください。\n" +
                 "5. 文体は元のテキストに合わせてください。";

        return prompt;
    }

    private string GetSummarizeSystemPrompt()
    {
        return "あなたは提供された日本語のテキストを要約するAIです。\n" +
               "以下の指示に従って、テキストの要点をまとめてください。\n" +
               "1. テキスト全体の主要なトピックと結論を把握します。\n" +
               "2. 重要な情報を抽出し、冗長な部分や詳細は省略します。\n" +
               "3. 元のテキストの意図を正確に反映した要約を作成します。";
    }

    private List<(string incorrect, string correct)> ParseWordListCsv(string? csvContent)
    {
        var result = new List<(string incorrect, string correct)>();
        
        if (string.IsNullOrWhiteSpace(csvContent))
            return result;

        try
        {
            var lines = csvContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in lines)
            {
                // ヘッダー行をスキップ
                if (line.Contains("誤") && line.Contains("正"))
                    continue;

                var parts = line.Split(',');
                if (parts.Length >= 2)
                {
                    var incorrect = parts[0].Trim().Trim('"');
                    var correct = parts[1].Trim().Trim('"');
                    
                    if (!string.IsNullOrWhiteSpace(incorrect) && !string.IsNullOrWhiteSpace(correct))
                    {
                        result.Add((incorrect, correct));
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse word list CSV");
        }

        return result;
    }

    private decimal? CalculateCost(string input, string output)
    {
        var inputTokens = EstimateTokens(input);
        var outputTokens = EstimateTokens(output);
        
        // GPT-4o-mini pricing (approximate) - configurable via settings
        var inputCostPerToken = _configuration.GetValue("OpenAI:InputCostPerToken", 0.00015m) / 1000;
        var outputCostPerToken = _configuration.GetValue("OpenAI:OutputCostPerToken", 0.0006m) / 1000;
        
        var totalCost = (inputTokens * inputCostPerToken) + (outputTokens * outputCostPerToken);
        
        _logger.LogDebug("Cost calculation: Input tokens: {InputTokens}, Output tokens: {OutputTokens}, Total cost: ${TotalCost:F6}", 
            inputTokens, outputTokens, totalCost);
            
        return totalCost;
    }

    private int EstimateTokens(string text)
    {
        // Rough estimation: 1 token ≈ 0.75 words for English, 1 token ≈ 1 character for Japanese
        return (int)(text.Length * 0.75);
    }
}