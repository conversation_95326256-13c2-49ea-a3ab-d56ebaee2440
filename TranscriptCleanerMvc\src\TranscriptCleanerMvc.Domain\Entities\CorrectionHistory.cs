using System;
using System.ComponentModel.DataAnnotations;
using TranscriptCleanerMvc.Transcripts;
using Volo.Abp.Domain.Entities.Auditing;

namespace TranscriptCleanerMvc.Entities;

public class CorrectionHistory : CreationAuditedEntity<Guid>
{
    public Guid TranscriptId { get; set; }
    
    [Required]
    public string BeforeText { get; set; } = string.Empty;
    
    [Required]
    public string AfterText { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string CorrectionType { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public int StartPosition { get; set; }
    
    public int EndPosition { get; set; }
    
    public CorrectionSource Source { get; set; } = CorrectionSource.AI;
    
    public decimal? Cost { get; set; }
    
    protected CorrectionHistory()
    {
    }
    
    public CorrectionHistory(
        Guid id,
        Guid transcriptId,
        string beforeText,
        string afterText,
        string correctionType,
        int startPosition,
        int endPosition,
        CorrectionSource source = CorrectionSource.AI,
        string? description = null
    ) : base(id)
    {
        TranscriptId = transcriptId;
        BeforeText = beforeText;
        AfterText = afterText;
        CorrectionType = correctionType;
        StartPosition = startPosition;
        EndPosition = endPosition;
        Source = source;
        Description = description;
    }
}