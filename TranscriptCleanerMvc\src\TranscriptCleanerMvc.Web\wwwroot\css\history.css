.history-container {
    padding: 20px;
}

.content-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    text-align: center;
}

.content-header h2 {
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
}

.content-header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.filter-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 25px;
}

.filter-section label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.history-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 25px;
}

.history-table {
    margin: 0;
}

.history-table thead {
    background: #f8f9fa;
}

.history-table th {
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding: 15px;
}

.history-table td {
    padding: 12px 15px;
    vertical-align: middle;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.success {
    background: #d4edda;
    color: #155724;
}

.status-badge.failed {
    background: #f8d7da;
    color: #721c24;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    margin-right: 5px;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.text-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
}

.result-comparison h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 10px;
}

.modal-lg {
    max-width: 900px;
}

@media (max-width: 768px) {
    .history-table-container {
        overflow-x: auto;
    }
    
    .filter-section .row > div {
        margin-bottom: 15px;
    }
}