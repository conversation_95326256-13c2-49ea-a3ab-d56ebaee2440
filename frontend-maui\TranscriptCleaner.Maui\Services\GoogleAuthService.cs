using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace TranscriptCleaner.Maui.Services;

/// <summary>
/// Google認証サービス実装
/// </summary>
public class GoogleAuthService : IGoogleAuthService
{
    private readonly HttpClient _httpClient;
    private readonly ISecureStorage _secureStorage;
    private readonly ILogger<GoogleAuthService> _logger;
    private readonly string _baseUrl = "https://localhost:44396";

    private GoogleUserInfo? _currentUser;
    private string? _accessToken;

    public bool IsAuthenticated => !string.IsNullOrEmpty(_accessToken) && _currentUser != null;
    public GoogleUserInfo? CurrentUser => _currentUser;

    public GoogleAuthService(ILogger<GoogleAuthService> logger)
    {
        _logger = logger;
        _secureStorage = SecureStorage.Default;

        // HTTPS証明書検証をスキップ（開発環境用）
        var handler = new HttpClientHandler();
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
        
        _httpClient = new HttpClient(handler);
        _httpClient.Timeout = TimeSpan.FromMinutes(2);
    }

    /// <summary>
    /// Googleアカウントでログイン
    /// </summary>
    public async Task<GoogleAuthResult> LoginWithGoogleAsync()
    {
        try
        {
            _logger.LogInformation("Google認証を開始します");

            // システムブラウザーでGoogle認証を開始
            var authUrl = $"{_baseUrl}/api/auth/external/google/login?returnUrl=maui://authenticated";
            
            // WebAuthenticatorを使用してシステムブラウザーで認証
            var authResult = await WebAuthenticator.AuthenticateAsync(
                new Uri(authUrl),
                new Uri("maui://authenticated"));

            if (authResult != null)
            {
                // 認証成功時の処理
                var accessToken = authResult.Properties.GetValueOrDefault("access_token");
                var userInfoJson = authResult.Properties.GetValueOrDefault("user");

                if (!string.IsNullOrEmpty(accessToken) && !string.IsNullOrEmpty(userInfoJson))
                {
                    var userInfo = JsonSerializer.Deserialize<GoogleUserInfo>(userInfoJson);
                    if (userInfo != null)
                    {
                        // 認証情報を保存
                        await SaveAuthenticationAsync(accessToken, userInfo);

                        _logger.LogInformation("Google認証が成功しました。ユーザー: {Email}", userInfo.Email);
                        return GoogleAuthResult.CreateSuccess(userInfo, false, accessToken);
                    }
                }
            }

            return GoogleAuthResult.CreateFailure("認証情報の取得に失敗しました");
        }
        catch (TaskCanceledException)
        {
            _logger.LogInformation("Google認証がキャンセルされました");
            return GoogleAuthResult.CreateFailure("認証がキャンセルされました");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Google認証中にエラーが発生しました");
            return GoogleAuthResult.CreateFailure($"認証エラー: {ex.Message}");
        }
    }

    /// <summary>
    /// Googleアカウントが連携されているかチェック
    /// </summary>
    public async Task<bool> IsGoogleLinkedAsync()
    {
        try
        {
            if (!IsAuthenticated)
                return false;

            var response = await _httpClient.GetAsync($"{_baseUrl}/api/auth/external/status");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var status = JsonSerializer.Deserialize<ExternalAccountStatus>(json);
                return status?.Google?.IsLinked == true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Google連携状態の確認中にエラーが発生しました");
            return false;
        }
    }

    /// <summary>
    /// 現在のユーザーにGoogleアカウントを連携
    /// </summary>
    public async Task<bool> LinkGoogleAccountAsync()
    {
        try
        {
            if (!IsAuthenticated)
                return false;

            // システムブラウザーでGoogle認証を開始（連携用）
            var authUrl = $"{_baseUrl}/api/auth/external/google/login?returnUrl=maui://linked&mode=link";
            
            var authResult = await WebAuthenticator.AuthenticateAsync(
                new Uri(authUrl),
                new Uri("maui://linked"));

            if (authResult != null)
            {
                var success = authResult.Properties.GetValueOrDefault("success");
                return success == "true";
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Googleアカウント連携中にエラーが発生しました");
            return false;
        }
    }

    /// <summary>
    /// Googleアカウント連携を解除
    /// </summary>
    public async Task<bool> UnlinkGoogleAccountAsync()
    {
        try
        {
            if (!IsAuthenticated)
                return false;

            var response = await _httpClient.DeleteAsync($"{_baseUrl}/api/auth/external/google/unlink");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Googleアカウント連携解除中にエラーが発生しました");
            return false;
        }
    }

    /// <summary>
    /// ログアウト
    /// </summary>
    public async Task LogoutAsync()
    {
        try
        {
            // サーバーからログアウト
            if (!string.IsNullOrEmpty(_accessToken))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _accessToken);
                
                await _httpClient.PostAsync($"{_baseUrl}/api/auth/logout", null);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "サーバーログアウト中にエラーが発生しました");
        }
        finally
        {
            // ローカル認証情報をクリア
            await ClearAuthenticationAsync();
        }
    }

    /// <summary>
    /// 認証情報を保存
    /// </summary>
    private async Task SaveAuthenticationAsync(string accessToken, GoogleUserInfo userInfo)
    {
        _accessToken = accessToken;
        _currentUser = userInfo;

        // セキュアストレージに保存
        await _secureStorage.SetAsync("google_access_token", accessToken);
        await _secureStorage.SetAsync("google_user_info", JsonSerializer.Serialize(userInfo));

        // HTTPクライアントにAuthorizationヘッダーを設定
        _httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
    }

    /// <summary>
    /// 認証情報をクリア
    /// </summary>
    private Task ClearAuthenticationAsync()
    {
        _accessToken = null;
        _currentUser = null;

        // セキュアストレージからクリア
        _secureStorage.Remove("google_access_token");
        _secureStorage.Remove("google_user_info");

        // HTTPクライアントのAuthorizationヘッダーをクリア
        _httpClient.DefaultRequestHeaders.Authorization = null;
        
        return Task.CompletedTask;
    }

    /// <summary>
    /// 保存された認証情報を復元
    /// </summary>
    public async Task<bool> RestoreAuthenticationAsync()
    {
        try
        {
            var accessToken = await _secureStorage.GetAsync("google_access_token");
            var userInfoJson = await _secureStorage.GetAsync("google_user_info");

            if (!string.IsNullOrEmpty(accessToken) && !string.IsNullOrEmpty(userInfoJson))
            {
                var userInfo = JsonSerializer.Deserialize<GoogleUserInfo>(userInfoJson);
                if (userInfo != null)
                {
                    await SaveAuthenticationAsync(accessToken, userInfo);
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "認証情報の復元中にエラーが発生しました");
            return false;
        }
    }
}

/// <summary>
/// 外部アカウント状態
/// </summary>
public class ExternalAccountStatus
{
    public GoogleLinkInfo? Google { get; set; }
}

/// <summary>
/// Google連携情報
/// </summary>
public class GoogleLinkInfo
{
    public bool IsLinked { get; set; }
    public string? Email { get; set; }
}