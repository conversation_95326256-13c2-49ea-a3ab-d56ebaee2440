using System.Collections.Generic;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// アカウント連携検証結果DTO
    /// </summary>
    public class AccountLinkValidationResultDto
    {
        /// <summary>
        /// 検証成功フラグ
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// エラーメッセージ一覧
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// 警告メッセージ一覧
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// 連携可能フラグ
        /// </summary>
        public bool CanLink { get; set; }

        /// <summary>
        /// 重複アカウント情報
        /// </summary>
        public DuplicateAccountInfoDto? DuplicateAccount { get; set; }

        /// <summary>
        /// 成功結果を作成
        /// </summary>
        public static AccountLinkValidationResultDto CreateSuccess()
        {
            return new AccountLinkValidationResultDto
            {
                IsValid = true,
                CanLink = true
            };
        }

        /// <summary>
        /// エラー結果を作成
        /// </summary>
        public static AccountLinkValidationResultDto CreateError(string error)
        {
            return new AccountLinkValidationResultDto
            {
                IsValid = false,
                CanLink = false,
                Errors = new List<string> { error }
            };
        }

        /// <summary>
        /// 重複エラー結果を作成
        /// </summary>
        public static AccountLinkValidationResultDto CreateDuplicateError(DuplicateAccountInfoDto duplicateAccount)
        {
            return new AccountLinkValidationResultDto
            {
                IsValid = false,
                CanLink = false,
                Errors = new List<string> { "このアカウントは既に他のユーザーに連携されています" },
                DuplicateAccount = duplicateAccount
            };
        }
    }

    /// <summary>
    /// 重複アカウント情報DTO
    /// </summary>
    public class DuplicateAccountInfoDto
    {
        /// <summary>
        /// 重複しているユーザーID
        /// </summary>
        public System.Guid UserId { get; set; }

        /// <summary>
        /// 重複しているユーザー名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 重複しているメールアドレス
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 連携日時
        /// </summary>
        public System.DateTime LinkedAt { get; set; }
    }
}