using System;
using System.Threading.Tasks;

namespace TranscriptCleaner.WinUI.Services
{
    /// <summary>
    /// WinUI用Google認証サービスインターフェース
    /// </summary>
    public interface IGoogleAuthService
    {
        /// <summary>
        /// Googleアカウントでログイン
        /// </summary>
        /// <returns>認証結果</returns>
        Task<AuthResult> LoginWithGoogleAsync();

        /// <summary>
        /// Googleアカウントが連携されているかチェック
        /// </summary>
        /// <returns>連携状況</returns>
        Task<bool> IsGoogleLinkedAsync();

        /// <summary>
        /// 現在のユーザーにGoogleアカウントを連携
        /// </summary>
        /// <returns>連携成功フラグ</returns>
        Task<bool> LinkGoogleAccountAsync();

        /// <summary>
        /// ログアウト
        /// </summary>
        /// <returns>ログアウト処理</returns>
        Task LogoutAsync();

        /// <summary>
        /// 現在の認証状態を取得
        /// </summary>
        /// <returns>認証状態</returns>
        Task<AuthState> GetAuthStateAsync();

        /// <summary>
        /// アクセストークンを取得
        /// </summary>
        /// <returns>アクセストークン</returns>
        Task<string?> GetAccessTokenAsync();

        /// <summary>
        /// トークンをリフレッシュ
        /// </summary>
        /// <returns>リフレッシュ成功フラグ</returns>
        Task<bool> RefreshTokenAsync();

        /// <summary>
        /// 認証情報をクリア
        /// </summary>
        /// <returns>クリア処理</returns>
        Task ClearAuthDataAsync();
    }

    /// <summary>
    /// 認証結果
    /// </summary>
    public class AuthResult
    {
        /// <summary>
        /// 成功フラグ
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// アクセストークン
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// リフレッシュトークン
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// トークン有効期限
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// ユーザー情報
        /// </summary>
        public GoogleUserInfo? UserInfo { get; set; }

        /// <summary>
        /// エラーメッセージ
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 新規ユーザーフラグ
        /// </summary>
        public bool IsNewUser { get; set; }

        /// <summary>
        /// 成功結果を作成
        /// </summary>
        public static AuthResult Success(string accessToken, GoogleUserInfo userInfo, bool isNewUser = false)
        {
            return new AuthResult
            {
                IsSuccess = true,
                AccessToken = accessToken,
                UserInfo = userInfo,
                IsNewUser = isNewUser,
                ExpiresAt = DateTime.UtcNow.AddHours(1)
            };
        }

        /// <summary>
        /// 失敗結果を作成
        /// </summary>
        public static AuthResult Failure(string errorMessage)
        {
            return new AuthResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 認証状態
    /// </summary>
    public enum AuthState
    {
        /// <summary>
        /// 未認証
        /// </summary>
        NotAuthenticated,

        /// <summary>
        /// 認証済み
        /// </summary>
        Authenticated,

        /// <summary>
        /// トークン期限切れ
        /// </summary>
        TokenExpired,

        /// <summary>
        /// エラー
        /// </summary>
        Error
    }

    /// <summary>
    /// Googleユーザー情報
    /// </summary>
    public class GoogleUserInfo
    {
        /// <summary>
        /// Google ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// メールアドレス
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 表示名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 名前
        /// </summary>
        public string GivenName { get; set; } = string.Empty;

        /// <summary>
        /// 姓
        /// </summary>
        public string FamilyName { get; set; } = string.Empty;

        /// <summary>
        /// プロフィール画像URL
        /// </summary>
        public string? Picture { get; set; }

        /// <summary>
        /// メール確認済みフラグ
        /// </summary>
        public bool EmailVerified { get; set; }
    }
}