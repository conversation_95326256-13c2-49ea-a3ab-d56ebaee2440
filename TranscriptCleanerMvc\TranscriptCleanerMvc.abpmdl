{"template": "app", "imports": {"Volo.Abp.LeptonXLiteTheme": {"version": "4.2.0", "isInstalled": true}, "Volo.Abp.Account": {"version": "9.2.0", "isInstalled": true}, "Volo.Abp.OpenIddict": {"version": "9.2.0", "isInstalled": true}, "Volo.Abp.Identity": {"version": "9.2.0", "isInstalled": true}, "Volo.Abp.TenantManagement": {"version": "9.2.0", "isInstalled": true}, "Volo.Abp.SettingManagement": {"version": "9.2.0", "isInstalled": true}, "Volo.Abp.PermissionManagement": {"version": "9.2.0", "isInstalled": true}, "Volo.Abp.FeatureManagement": {"version": "9.2.0", "isInstalled": true}}, "folders": {"items": {"src": {}, "test": {}}}, "packages": {"TranscriptCleanerMvc.Application": {"path": "src/TranscriptCleanerMvc.Application/TranscriptCleanerMvc.Application.abppkg", "folder": "src"}, "TranscriptCleanerMvc.Application.Tests": {"path": "test/TranscriptCleanerMvc.Application.Tests/TranscriptCleanerMvc.Application.Tests.abppkg", "folder": "test"}, "TranscriptCleanerMvc.Domain.Shared": {"path": "src/TranscriptCleanerMvc.Domain.Shared/TranscriptCleanerMvc.Domain.Shared.abppkg", "folder": "src"}, "TranscriptCleanerMvc.Application.Contracts": {"path": "src/TranscriptCleanerMvc.Application.Contracts/TranscriptCleanerMvc.Application.Contracts.abppkg", "folder": "src"}, "TranscriptCleanerMvc.HttpApi": {"path": "src/TranscriptCleanerMvc.HttpApi/TranscriptCleanerMvc.HttpApi.abppkg", "folder": "src"}, "TranscriptCleanerMvc.HttpApi.Client": {"path": "src/TranscriptCleanerMvc.HttpApi.Client/TranscriptCleanerMvc.HttpApi.Client.abppkg", "folder": "src"}, "TranscriptCleanerMvc.Web": {"path": "src/TranscriptCleanerMvc.Web/TranscriptCleanerMvc.Web.abppkg", "folder": "src"}, "TranscriptCleanerMvc.Web.Tests": {"path": "test/TranscriptCleanerMvc.Web.Tests/TranscriptCleanerMvc.Web.Tests.abppkg", "folder": "test"}, "TranscriptCleanerMvc.EntityFrameworkCore.Tests": {"path": "test/TranscriptCleanerMvc.EntityFrameworkCore.Tests/TranscriptCleanerMvc.EntityFrameworkCore.Tests.abppkg", "folder": "test"}, "TranscriptCleanerMvc.EntityFrameworkCore": {"path": "src/TranscriptCleanerMvc.EntityFrameworkCore/TranscriptCleanerMvc.EntityFrameworkCore.abppkg", "folder": "src"}, "TranscriptCleanerMvc.TestBase": {"path": "test/TranscriptCleanerMvc.TestBase/TranscriptCleanerMvc.TestBase.abppkg", "folder": "test"}, "TranscriptCleanerMvc.Domain.Tests": {"path": "test/TranscriptCleanerMvc.Domain.Tests/TranscriptCleanerMvc.Domain.Tests.abppkg", "folder": "test"}, "TranscriptCleanerMvc.HttpApi.Client.ConsoleTestApp": {"path": "test/TranscriptCleanerMvc.HttpApi.Client.ConsoleTestApp/TranscriptCleanerMvc.HttpApi.Client.ConsoleTestApp.abppkg", "folder": "test"}, "TranscriptCleanerMvc.DbMigrator": {"path": "src/TranscriptCleanerMvc.DbMigrator/TranscriptCleanerMvc.DbMigrator.abppkg", "folder": "src"}, "TranscriptCleanerMvc.Domain": {"path": "src/TranscriptCleanerMvc.Domain/TranscriptCleanerMvc.Domain.abppkg", "folder": "src"}}}