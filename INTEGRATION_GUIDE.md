# フロントエンド・バックエンド連携ガイド

このガイドでは、React フロントエンドと ABP Framework バックエンドの連携手順を説明します。

## 📋 前提条件

### システム要件
- **.NET 9.0 SDK**: バックエンド開発
- **Node.js 18+**: React フロントエンド
- **SQL Server**: データベース（LocalDB可）
- **Visual Studio 2022** または **VS Code**: 推奨IDE

### ポート設定
- **バックエンド**: `https://localhost:44363`
- **フロントエンド**: `http://localhost:3000`

## 🚀 起動手順

### ステップ1: データベースの準備

```powershell
# SQL Server LocalDB を起動
sqllocaldb start MSSQLLocalDB

# データベース接続を確認
sqlcmd -S "(localdb)\MSSQLLocalDB" -E -Q "SELECT @@VERSION"
```

### ステップ2: バックエンド（ABP Framework）の起動

```powershell
# 1. バックエンドディレクトリに移動


# 2. NuGetパッケージを復元
dotnet restore

# 3. データベースマイグレーション実行
dotnet ef database update --project src/TranscriptCleaner.EntityFrameworkCore --startup-project src/TranscriptCleaner.Web

# 4. アプリケーション起動
dotnet run --project src/TranscriptCleaner.Web
```

**起動確認**:
- ブラウザで `https://localhost:44363` にアクセス
- Swagger UI: `https://localhost:44363/swagger`

### ステップ3: フロントエンド（React）の起動

#### 方法1: 自動インストールスクリプトを使用

```powershell
# Windows PowerShell
.\install-dependencies.ps1

# または Linux/Mac
./install-dependencies.sh
```

#### 方法2: 手動インストール

```powershell
# 1. フロントエンドディレクトリに移動
cd frontend-web/transcript-cleaner-web

# 2. 依存関係をインストール
npm install

# 3. 開発サーバー起動
npm start
```

**起動確認**:
- ブラウザで `http://localhost:3000` にアクセス
- ログイン画面が表示されることを確認

## 🔐 初期ログインユーザー

以下のアカウントでテストできます：

| ロール | ユーザー名 | パスワード | 権限 |
|---------|-----------|-----------|------|
| 管理者 | `admin` | `admin123` | 全権限 |
| 一般ユーザー | `testuser` | `test123` | 基本機能 |
| ゲスト | `demo` | `demo123` | 制限付き |

## 🔧 設定ファイル

### バックエンド設定

**appsettings.json**:
```json
{
  "App": {
    "SelfUrl": "https://localhost:44363",
    "CorsOrigins": "http://localhost:3000,https://localhost:3000"
  },
  "ConnectionStrings": {
    "Default": "Server=(LocalDb)\\MSSQLLocalDB;Database=TranscriptCleaner;Trusted_Connection=True;TrustServerCertificate=true"
  }
}
```

### フロントエンド設定

**.env**:
```
REACT_APP_API_URL=https://localhost:44363
REACT_APP_VERSION=1.0.0
REACT_APP_TITLE=TranscriptCleaner
PORT=3000
```

## 🌐 API エンドポイント

### 認証 API
- **POST** `/api/account/login` - ログイン
- **POST** `/api/account/logout` - ログアウト

### ユーザー管理 API
- **GET** `/api/identity/users` - ユーザー一覧取得
- **POST** `/api/identity/users` - ユーザー作成
- **PUT** `/api/identity/users/{id}` - ユーザー更新
- **DELETE** `/api/identity/users/{id}` - ユーザー削除

### ロール管理 API
- **GET** `/api/identity/roles` - ロール一覧取得
- **POST** `/api/identity/roles` - ロール作成
- **PUT** `/api/identity/roles/{id}` - ロール更新
- **DELETE** `/api/identity/roles/{id}` - ロール削除

### トランスクリプト訂正 API（実装予定）
- **POST** `/api/transcript/correct` - トランスクリプト訂正
- **GET** `/api/transcript/history` - 訂正履歴取得
- **POST** `/api/transcript/upload` - ファイルアップロード

## 🔒 CORS 設定

以下のオリジンが許可されています：
- `http://localhost:3000`
- `https://localhost:3000`
- `http://localhost:4200`
- `https://localhost:4200`

## 🛠️ トラブルシューティング

### よくある問題と解決方法

#### 1. CORS エラー
**症状**: フロントエンドからAPIリクエストが失敗
**解決方法**:
```powershell
# バックエンドが正しいポートで起動していることを確認
netstat -an | findstr :44363

# appsettings.jsonのCorsOriginsを確認
```

#### 2. データベース接続エラー
**症状**: Entity Framework の接続エラー
**解決方法**:
```powershell
# LocalDB の状態確認
sqllocaldb info MSSQLLocalDB

# データベース再作成
dotnet ef database drop --force --project src/TranscriptCleaner.EntityFrameworkCore --startup-project src/TranscriptCleaner.Web
dotnet ef database update --project src/TranscriptCleaner.EntityFrameworkCore --startup-project src/TranscriptCleaner.Web
```

#### 3. SSL 証明書エラー
**症状**: HTTPS 接続時の証明書エラー
**解決方法**:
```powershell
# 開発証明書を信頼
dotnet dev-certs https --trust
```

#### 4. Node.js 依存関係エラー
**症状**: `Module not found: Error: Can't resolve '@mui/material'` のようなエラー

**即座に解決する方法**:
```powershell
# Windows PowerShell
.\fix-dependencies.ps1

# または Linux/Mac
./fix-dependencies.sh
```

**手動での解決方法**:
```powershell
cd frontend-web/transcript-cleaner-web

# npmプロセスを停止
taskkill /F /IM node.exe 2>nul
taskkill /F /IM npm.exe 2>nul

# キャッシュクリアと依存関係再インストール
npm cache clean --force
rmdir /s /q node_modules 2>nul
del package-lock.json 2>nul
npm install

# 上記で解決しない場合は個別インストール
npm install @mui/material@^6.1.0 @mui/icons-material@^6.1.0
npm install react-router-dom@^6.20.1 react-i18next@^14.1.0
npm install axios@^1.6.7
```

#### 5. ESLint 警告
**症状**: 未使用の変数やHookの依存関係に関する警告
**解決方法**: これらは警告であり、アプリケーションの動作には影響しません。開発が完了したら修正することを推奨します。

## 📊 開発フロー

### 1. フロントエンド開発
```powershell
# 新機能の開発
cd frontend-web/transcript-cleaner-web
npm start

# テスト実行
npm test

# ビルド
npm run build
```

### 2. バックエンド開発
```powershell
# 新しいマイグレーション作成
dotnet ef migrations add AddNewFeature --project src/TranscriptCleaner.EntityFrameworkCore --startup-project src/TranscriptCleaner.Web

# マイグレーション適用
dotnet ef database update --project src/TranscriptCleaner.EntityFrameworkCore --startup-project src/TranscriptCleaner.Web

# テスト実行
dotnet test
```

## 🚢 本番環境デプロイ

### 環境変数の設定
```bash
# バックエンド
export ConnectionStrings__Default="[本番データベース接続文字列]"
export App__SelfUrl="https://your-domain.com"
export App__CorsOrigins="https://your-frontend-domain.com"

# フロントエンド
export REACT_APP_API_URL="https://your-api-domain.com"
```

### ビルドコマンド
```powershell
# バックエンド
dotnet publish src/TranscriptCleaner.Web -c Release -o ./publish

# フロントエンド
npm run build
```

## 📝 チェックリスト

連携作業完了時の確認項目：

- [ ] バックエンドが `https://localhost:44363` で起動する
- [ ] フロントエンドが `http://localhost:3000` で起動する
- [ ] ログイン画面が表示される
- [ ] 管理者アカウントでログインできる
- [ ] ユーザー管理画面が表示される
- [ ] ロール管理画面が表示される
- [ ] APIエラーがブラウザコンソールに出ていない
- [ ] CORS エラーが発生しない

## 🆘 サポート

問題が発生した場合：
1. このガイドのトラブルシューティングを確認
2. ブラウザの開発者ツールでエラーメッセージを確認
3. バックエンドのログファイルを確認
4. GitHubのIssuesで報告

---

## 🔄 更新履歴

- **2025-01-14**: 初版作成
- フロントエンド・バックエンド連携の基本設定完了
- CORS 設定追加
- 環境変数設定追加