# Google Cloud Console 設定ガイド

このガイドでは、TranscriptCleaner アプリケーションでGoogle認証を使用するために必要なGoogle Cloud Console の設定手順を説明します。

## 目次

1. [前提条件](#前提条件)
2. [Google Cloud プロジェクトの作成](#google-cloud-プロジェクトの作成)
3. [OAuth 2.0 認証情報の設定](#oauth-20-認証情報の設定)
4. [リダイレクト URI の設定](#リダイレクト-uri-の設定)
5. [環境別設定](#環境別設定)
6. [セキュリティ設定](#セキュリティ設定)
7. [トラブルシューティング](#トラブルシューティング)

## 前提条件

- Googleアカウント
- 管理者権限（組織で使用する場合）
- 基本的なOAuth 2.0の知識

## Google Cloud プロジェクトの作成

### 1. Google Cloud Console にアクセス

1. [Google Cloud Console](https://console.cloud.google.com/) にアクセス
2. Googleアカウントでサインイン

### 2. 新しいプロジェクトの作成

1. 画面上部の「プロジェクトを選択」をクリック
2. 「新しいプロジェクト」をクリック
3. プロジェクト情報を入力：
   - **プロジェクト名**: `TranscriptCleaner-Auth`
   - **組織**: 適切な組織を選択（個人の場合は「組織なし」）
   - **場所**: 適切な場所を選択
4. 「作成」をクリック

### 3. プロジェクトの選択

作成したプロジェクトが自動的に選択されることを確認します。

## OAuth 2.0 認証情報の設定

### 1. Google+ API の有効化

1. 左側のメニューから「APIとサービス」→「ライブラリ」を選択
2. 「Google+ API」を検索
3. 「Google+ API」をクリックして「有効にする」

### 2. OAuth 同意画面の設定

1. 左側のメニューから「APIとサービス」→「OAuth同意画面」を選択
2. ユーザータイプを選択：
   - **内部**: 組織内のユーザーのみ
   - **外部**: 一般ユーザー向け（推奨）
3. 「作成」をクリック

#### OAuth同意画面の詳細設定

**アプリ情報**
- **アプリ名**: `TranscriptCleaner`
- **ユーザーサポートメール**: あなたのメールアドレス
- **アプリのロゴ**: アプリケーションのロゴ（オプション）

**アプリドメイン**
- **アプリのホームページ**: `https://your-domain.com`
- **プライバシーポリシーのリンク**: `https://your-domain.com/privacy`
- **利用規約のリンク**: `https://your-domain.com/terms`

**承認済みドメイン**
- 本番環境のドメインを追加
- 例: `your-domain.com`

**デベロッパーの連絡先情報**
- あなたのメールアドレスを入力

4. 「保存して次へ」をクリック

### 3. スコープの設定

1. 「スコープを追加または削除」をクリック
2. 以下のスコープを選択：
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
   - `openid`
3. 「更新」をクリック
4. 「保存して次へ」をクリック

### 4. テストユーザーの追加（外部アプリの場合）

1. 「テストユーザーを追加」をクリック
2. 開発・テスト用のGoogleアカウントを追加
3. 「保存して次へ」をクリック

## OAuth 2.0 認証情報の作成

### 1. 認証情報の作成

1. 左側のメニューから「APIとサービス」→「認証情報」を選択
2. 「認証情報を作成」→「OAuth 2.0 クライアント ID」をクリック

### 2. アプリケーションタイプの選択

各プラットフォーム用に以下の認証情報を作成します：

#### Web アプリケーション用

1. **アプリケーションの種類**: `ウェブ アプリケーション`
2. **名前**: `TranscriptCleaner-Web`
3. **承認済みの JavaScript 生成元**:
   ```
   http://localhost:44300
   https://your-domain.com
   ```
4. **承認済みのリダイレクト URI**:
   ```
   http://localhost:44300/signin-google
   https://your-domain.com/signin-google
   ```

#### デスクトップアプリケーション用（WinUI）

1. **アプリケーションの種類**: `デスクトップ アプリケーション`
2. **名前**: `TranscriptCleaner-WinUI`

#### モバイルアプリケーション用（MAUI）

1. **アプリケーションの種類**: `iOS` または `Android`
2. **名前**: `TranscriptCleaner-MAUI-iOS` / `TranscriptCleaner-MAUI-Android`
3. **バンドル ID** (iOS): `com.transcriptcleaner.maui`
4. **パッケージ名** (Android): `com.transcriptcleaner.maui`

### 3. 認証情報のダウンロード

1. 作成した各認証情報の「ダウンロード」ボタンをクリック
2. JSONファイルを安全な場所に保存
3. ファイル名を分かりやすく変更：
   - `google-credentials-web.json`
   - `google-credentials-winui.json`
   - `google-credentials-maui.json`

## リダイレクト URI の設定

### Web アプリケーション

#### 開発環境
```
http://localhost:44300/signin-google
http://localhost:44300/ExternalAuth/Callback/Google
```

#### 本番環境
```
https://your-domain.com/signin-google
https://your-domain.com/ExternalAuth/Callback/Google
```

### デスクトップアプリケーション（WinUI）

```
http://localhost:8080/callback
ms-app://s-1-15-2-1234567890-1234567890-1234567890-1234567890-1234567890-1234567890-1234567890/
```

### モバイルアプリケーション（MAUI）

#### iOS
```
com.transcriptcleaner.maui://oauth/callback
```

#### Android
```
com.transcriptcleaner.maui://oauth/callback
```

## 環境別設定

### 開発環境

1. **ドメイン制限**: なし
2. **テストユーザー**: 開発チームのアカウントを追加
3. **リダイレクト URI**: localhost を含める

### ステージング環境

1. **ドメイン制限**: ステージングドメインのみ
2. **テストユーザー**: QAチームとステークホルダーを追加
3. **リダイレクト URI**: ステージングドメインを設定

### 本番環境

1. **ドメイン制限**: 本番ドメインのみ
2. **公開ステータス**: 「本番環境に公開」に設定
3. **リダイレクト URI**: 本番ドメインのみ

## セキュリティ設定

### 1. ドメイン制限の設定

1. OAuth同意画面で「承認済みドメイン」を設定
2. 本番環境では必ず制限を有効にする

### 2. スコープの最小化

必要最小限のスコープのみを要求：
- `openid`: OpenID Connect用
- `email`: メールアドレス取得用
- `profile`: 基本プロフィール情報用

### 3. リフレッシュトークンの管理

1. リフレッシュトークンの有効期限を適切に設定
2. 不要になったトークンは無効化

### 4. 監査ログの有効化

1. Google Cloud Console で監査ログを有効化
2. 認証関連のイベントを監視

## アプリケーション設定

### Web アプリケーション (appsettings.json)

```json
{
  "Authentication": {
    "Google": {
      "ClientId": "your-web-client-id.apps.googleusercontent.com",
      "ClientSecret": "your-web-client-secret"
    }
  }
}
```

### WinUI アプリケーション

```csharp
public class GoogleAuthOptions
{
    public string ClientId { get; set; } = "your-winui-client-id.apps.googleusercontent.com";
    public string RedirectUri { get; set; } = "http://localhost:8080/callback";
}
```

### MAUI アプリケーション

```csharp
public class GoogleAuthConfig
{
    public string ClientId { get; set; } = "your-maui-client-id.apps.googleusercontent.com";
    public string RedirectUri { get; set; } = "com.transcriptcleaner.maui://oauth/callback";
}
```

## 本番環境への公開

### 1. アプリの検証

1. OAuth同意画面で「アプリを公開」をクリック
2. Googleの審査プロセスを完了
3. 必要に応じて追加の検証を実施

### 2. 検証に必要な情報

- プライバシーポリシー
- 利用規約
- アプリの説明
- スクリーンショット
- 動画デモ（オプション）

## トラブルシューティング

### よくある問題と解決方法

#### 1. "redirect_uri_mismatch" エラー

**原因**: リダイレクトURIが一致しない

**解決方法**:
1. Google Cloud Console で設定されているリダイレクトURIを確認
2. アプリケーションで使用しているURIと完全に一致させる
3. プロトコル（http/https）、ポート番号も含めて正確に設定

#### 2. "invalid_client" エラー

**原因**: クライアントIDまたはクライアントシークレットが間違っている

**解決方法**:
1. 認証情報を再確認
2. 環境変数や設定ファイルの値を確認
3. 必要に応じて新しい認証情報を作成

#### 3. "access_denied" エラー

**原因**: ユーザーが認証を拒否した、またはアプリが未承認

**解決方法**:
1. OAuth同意画面の設定を確認
2. テストユーザーに追加されているか確認
3. 必要なスコープが正しく設定されているか確認

#### 4. "invalid_scope" エラー

**原因**: 要求したスコープが無効または未承認

**解決方法**:
1. OAuth同意画面でスコープを確認
2. アプリケーションで要求しているスコープを確認
3. 必要に応じてスコープを追加または修正

### デバッグ用ツール

#### 1. Google OAuth 2.0 Playground

- URL: https://developers.google.com/oauthplayground/
- 認証フローのテストに使用

#### 2. JWT.io

- URL: https://jwt.io/
- JWTトークンのデコードと検証

#### 3. Google API Explorer

- URL: https://developers.google.com/apis-explorer/
- Google APIの動作確認

## セキュリティベストプラクティス

### 1. 認証情報の管理

- クライアントシークレットは環境変数で管理
- 本番環境とテスト環境で異なる認証情報を使用
- 定期的な認証情報のローテーション

### 2. スコープの最小化

- 必要最小限のスコープのみを要求
- 不要になったスコープは削除

### 3. トークンの管理

- アクセストークンの適切な有効期限設定
- リフレッシュトークンの安全な保存
- トークンの暗号化

### 4. 監査とログ

- 認証イベントのログ記録
- 異常なアクセスパターンの監視
- 定期的なセキュリティ監査

## 参考資料

- [Google Identity Platform Documentation](https://developers.google.com/identity)
- [OAuth 2.0 for Web Server Applications](https://developers.google.com/identity/protocols/oauth2/web-server)
- [OAuth 2.0 for Mobile & Desktop Apps](https://developers.google.com/identity/protocols/oauth2/native-app)
- [OpenID Connect](https://developers.google.com/identity/protocols/oauth2/openid-connect)

## サポート

設定に関する問題や質問がある場合は、以下のリソースを参照してください：

- [Google Cloud Support](https://cloud.google.com/support)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/google-oauth)
- [GitHub Issues](https://github.com/your-repo/issues)

---

**注意**: このガイドは定期的に更新されます。最新の情報については、Google Cloud Console の公式ドキュメントを参照してください。