using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Xunit;
using TranscriptCleanerMvc.Identity;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// エラーハンドリングサービスの単体テスト
    /// </summary>
    public class ErrorHandlingServiceTests : TranscriptCleanerMvcApplicationTestBase<TranscriptCleanerMvcApplicationTestModule>
    {
        private readonly IErrorHandlingService _errorHandlingService;

        public ErrorHandlingServiceTests()
        {
            _errorHandlingService = GetRequiredService<IErrorHandlingService>();
        }

        [Fact]
        public async Task HandleExceptionAsync_UserFriendlyException_ShouldReturnUserFriendlyResponse()
        {
            // Arrange
            var exception = new UserFriendlyException("ユーザーフレンドリーなエラーメッセージ");
            var context = new ErrorContext
            {
                UserId = Guid.NewGuid(),
                RequestId = "req_123",
                IpAddress = "***********",
                UserLocale = "ja"
            };

            // Act
            var result = await _errorHandlingService.HandleExceptionAsync(exception, context);

            // Assert
            result.ShouldNotBeNull();
        }
    }
}