using TranscriptCleaner.Maui.ViewModels;
using TranscriptCleaner.Maui.Services;

namespace TranscriptCleaner.Maui
{
    public partial class LoginPage : ContentPage
    {
        private readonly LoginPageViewModel _viewModel;
        private readonly IGoogleAuthService _googleAuthService;

        public LoginPage(AuthenticationService authService, IGoogleAuthService googleAuthService, IOfflineAuthService offlineAuthService, INetworkService networkService)
        {
            InitializeComponent();
            _viewModel = new LoginPageViewModel(authService, googleAuthService, offlineAuthService, networkService);
            _googleAuthService = googleAuthService;
            BindingContext = _viewModel;
        }

        private async void OnLoginClicked(object sender, EventArgs e)
        {
            try
            {
                // ViewModelのLoginAsyncメソッドを使用
                var result = await _viewModel.LoginAsync();
                
                if (result.Success)
                {
                    // ログイン成功 - メイン画面に遷移
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"遷移開始 - ユーザー名: {_viewModel.Username}");
                        await Shell.Current.GoToAsync("//MainPage", new Dictionary<string, object>
                        {
                            {"Username", _viewModel.Username},
                            {"IsLoggedIn", "true"}
                        });
                        System.Diagnostics.Debug.WriteLine("遷移完了");
                    }
                    catch (Exception navEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"ナビゲーションエラー詳細: {navEx}");
                        await DisplayAlert("ナビゲーションエラー", $"画面遷移に失敗しました：\n{navEx.Message}", "OK");
                    }
                }
                else
                {
                    await DisplayAlert("ログインエラー", result.Message, "OK");
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("エラー", $"ログイン処理でエラーが発生しました：\n{ex.Message}", "OK");
            }
        }

        private async void OnGoogleLoginClicked(object sender, EventArgs e)
        {
            try
            {
                // Google認証を開始
                _viewModel.IsProcessing = true;
                _viewModel.StatusMessage = "Googleアカウントで認証中...";

                var result = await _googleAuthService.LoginWithGoogleAsync();

                if (result.Success && result.User != null)
                {
                    // Google認証成功 - メイン画面に遷移
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"Google認証成功 - ユーザー: {result.User.Email}");
                        await Shell.Current.GoToAsync("//MainPage", new Dictionary<string, object>
                        {
                            {"Username", result.User.Email},
                            {"IsLoggedIn", "true"},
                            {"AuthMethod", "Google"}
                        });
                        System.Diagnostics.Debug.WriteLine("Google認証後の遷移完了");
                    }
                    catch (Exception navEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"Google認証後のナビゲーションエラー: {navEx}");
                        await DisplayAlert("ナビゲーションエラー", $"画面遷移に失敗しました：\n{navEx.Message}", "OK");
                    }
                }
                else
                {
                    await DisplayAlert("Google認証エラー", result.ErrorMessage ?? "認証に失敗しました", "OK");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Google認証エラー: {ex}");
                await DisplayAlert("エラー", $"Google認証でエラーが発生しました：\n{ex.Message}", "OK");
            }
            finally
            {
                _viewModel.IsProcessing = false;
                _viewModel.StatusMessage = string.Empty;
            }
        }

    }
}