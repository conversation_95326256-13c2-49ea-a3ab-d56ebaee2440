﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="TranscriptCleaner.Maui.LoginPage"
             Title="ログイン"
             BackgroundColor="#f8f9fa">

    <ContentPage.Resources>
        <Shadow x:Key="CommonShadow"
                Brush="Black"
                Offset="0,2"
                Radius="4"
                Opacity="0.1" />
    </ContentPage.Resources>

    <Grid>
        <!-- グラデーション背景 -->
        <Rectangle>
            <Rectangle.Fill>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667eea" Offset="0.0" />
                    <GradientStop Color="#764ba2" Offset="1.0" />
                </LinearGradientBrush>
            </Rectangle.Fill>
        </Rectangle>

        <!-- メインコンテンツ -->
        <Grid VerticalOptions="Center" HorizontalOptions="Center">
            <ScrollView>
                <StackLayout Padding="40" Spacing="30" WidthRequest="400">
                
                <!-- ログインカード -->
                <Border BackgroundColor="White" 
                        Stroke="Transparent"
                        StrokeThickness="0"
                        StrokeShape="RoundRectangle 15"
                        Padding="30"
                        Shadow="{StaticResource CommonShadow}">
                    
                    <StackLayout Spacing="25">
                        
                        <!-- ヘッダー -->
                        <StackLayout Spacing="10">
                            <Label Text="🔐" 
                                   FontSize="48" 
                                   HorizontalOptions="Center" />
                            <Label Text="TranscriptCleaner" 
                                   FontSize="24" 
                                   FontAttributes="Bold" 
                                   HorizontalOptions="Center"
                                   TextColor="#4472c4" />
                            <Label Text="Microsoft Teams 議事録自動訂正システム" 
                                   FontSize="14" 
                                   HorizontalOptions="Center"
                                   TextColor="#6c757d" />
                        </StackLayout>

                        <!-- ユーザー名入力 -->
                        <StackLayout Spacing="8">
                            <Label Text="ユーザー名" 
                                   FontSize="14" 
                                   TextColor="#495057"
                                   FontAttributes="Bold" />
                            <Border Stroke="#dee2e6" 
                                    BackgroundColor="#fafbfc"
                                    StrokeThickness="1"
                                    StrokeShape="RoundRectangle 8"
                                    Padding="0">
                                <Entry x:Name="UsernameEntry"
                                       Placeholder="ユーザー名を入力してください"
                                       BackgroundColor="Transparent"
                                       HeightRequest="45"
                                       Text="{Binding Username}" />
                            </Border>
                        </StackLayout>

                        <!-- パスワード入力 -->
                        <StackLayout Spacing="8">
                            <Label Text="パスワード" 
                                   FontSize="14" 
                                   TextColor="#495057"
                                   FontAttributes="Bold" />
                            <Border Stroke="#dee2e6" 
                                    BackgroundColor="#fafbfc"
                                    StrokeThickness="1"
                                    StrokeShape="RoundRectangle 8"
                                    Padding="0">
                                <Entry x:Name="PasswordEntry"
                                       Placeholder="パスワードを入力してください"
                                       IsPassword="True"
                                       BackgroundColor="Transparent"
                                       HeightRequest="45"
                                       Text="{Binding Password}" />
                            </Border>
                        </StackLayout>

                        <!-- ログインボタン -->
                        <Button x:Name="LoginButton"
                                Text="ログイン" 
                                BackgroundColor="#4472c4" 
                                TextColor="White"
                                FontSize="16"
                                FontAttributes="Bold"
                                HeightRequest="50"
                                CornerRadius="25"
                                Clicked="OnLoginClicked"
                                IsEnabled="{Binding CanLogin}" />

                        <!-- 区切り線 -->
                        <StackLayout Orientation="Horizontal" Spacing="10" Margin="0,10">
                            <BoxView BackgroundColor="#dee2e6" HeightRequest="1" VerticalOptions="Center" HorizontalOptions="FillAndExpand" />
                            <Label Text="または" FontSize="12" TextColor="#6c757d" VerticalOptions="Center" />
                            <BoxView BackgroundColor="#dee2e6" HeightRequest="1" VerticalOptions="Center" HorizontalOptions="FillAndExpand" />
                        </StackLayout>

                        <!-- Googleログインボタン -->
                        <Button x:Name="GoogleLoginButton"
                                Text="🔍 Googleでログイン"
                                BackgroundColor="White"
                                TextColor="#333333"
                                FontSize="16"
                                FontAttributes="Bold"
                                HeightRequest="50"
                                CornerRadius="25"
                                Clicked="OnGoogleLoginClicked"
                                IsEnabled="{Binding CanLogin}" />

                        <!-- デモユーザー情報 -->
                        <Border BackgroundColor="#e3f2fd" 
                                Stroke="#bbdefb"
                                StrokeThickness="1"
                                StrokeShape="RoundRectangle 8"
                                Padding="15">
                            <StackLayout Spacing="8">
                                <Label Text="💡 デモアカウント" 
                                       FontSize="14" 
                                       FontAttributes="Bold"
                                       TextColor="#1976d2" />
                                <Label Text="ユーザー名: admin / パスワード: admin123" 
                                       FontSize="12" 
                                       TextColor="#1565c0" />
                                <Label Text="ユーザー名: testuser / パスワード: test123" 
                                       FontSize="12" 
                                       TextColor="#1565c0" />
                            </StackLayout>
                        </Border>

                        <!-- ステータス表示 -->
                        <StackLayout Orientation="Horizontal" 
                                     Spacing="10"
                                     IsVisible="{Binding IsProcessing}">
                            <ActivityIndicator IsVisible="{Binding IsProcessing}" 
                                               IsRunning="{Binding IsProcessing}"
                                               Color="#4472c4" />
                            <Label Text="{Binding StatusMessage}" 
                                   FontSize="14" 
                                   TextColor="#6c757d" 
                                   VerticalOptions="Center" />
                        </StackLayout>

                    </StackLayout>
                </Border>

                <!-- フッター -->
                <Label Text="© 2024 TranscriptCleaner - AI駆動の議事録訂正ツール" 
                       FontSize="12" 
                       HorizontalOptions="Center"
                       TextColor="White"
                       Opacity="0.8" />

                </StackLayout>
            </ScrollView>
        </Grid>

    </Grid>

</ContentPage>