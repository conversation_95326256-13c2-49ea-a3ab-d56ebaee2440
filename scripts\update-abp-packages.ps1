# ABP Framework パッケージ更新スクリプト
# Google認証機能実装のためのパッケージ確認・更新

param(
    [switch]$CheckOnly = $false,
    [switch]$Update = $false
)

Write-Host "=== ABP Framework パッケージ確認 ===" -ForegroundColor Green

# プロジェクトファイルのパス
$projects = @(
    "TranscriptCleanerMvc/src/TranscriptCleanerMvc.Web/TranscriptCleanerMvc.Web.csproj",
    "TranscriptCleanerMvc/src/TranscriptCleanerMvc.Domain/TranscriptCleanerMvc.Domain.csproj",
    "TranscriptCleanerMvc/src/TranscriptCleanerMvc.Application/TranscriptCleanerMvc.Application.csproj",
    "TranscriptCleanerMvc/src/TranscriptCleanerMvc.EntityFrameworkCore/TranscriptCleanerMvc.EntityFrameworkCore.csproj"
)

# 必要なパッケージとバージョン
$requiredPackages = @{
    "Volo.Abp.Account.Web.OpenIddict" = "9.2.0"
    "Volo.Abp.Identity.Web" = "9.2.0"
    "Volo.Abp.Identity.AspNetCore" = "9.2.0"
    "Microsoft.AspNetCore.Authentication.Google" = "9.0.0"
    "Microsoft.AspNetCore.Authentication.OAuth" = "9.0.0"
}

function Check-PackageVersion {
    param($projectPath, $packageName, $expectedVersion)
    
    if (-not (Test-Path $projectPath)) {
        Write-Host "❌ プロジェクトファイルが見つかりません: $projectPath" -ForegroundColor Red
        return $false
    }
    
    $content = Get-Content $projectPath -Raw
    $pattern = "<PackageReference Include=`"$packageName`" Version=`"([^`"]+)`""
    
    if ($content -match $pattern) {
        $currentVersion = $matches[1]
        if ($currentVersion -eq $expectedVersion) {
            Write-Host "✅ $packageName : $currentVersion (最新)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️  $packageName : $currentVersion → $expectedVersion (更新推奨)" -ForegroundColor Yellow
            return $false
        }
    } else {
        Write-Host "❌ $packageName : 未インストール" -ForegroundColor Red
        return $false
    }
}

# 各プロジェクトのパッケージ確認
foreach ($project in $projects) {
    if (Test-Path $project) {
        Write-Host "`n--- $(Split-Path $project -Leaf) ---" -ForegroundColor Cyan
        
        foreach ($package in $requiredPackages.GetEnumerator()) {
            Check-PackageVersion $project $package.Key $package.Value
        }
    }
}

# Google認証に必要な追加パッケージの確認
Write-Host "`n=== Google認証関連パッケージ確認 ===" -ForegroundColor Green

$webProject = "TranscriptCleanerMvc/src/TranscriptCleanerMvc.Web/TranscriptCleanerMvc.Web.csproj"
if (Test-Path $webProject) {
    $content = Get-Content $webProject -Raw
    
    # Google認証パッケージの確認
    if ($content -match 'Microsoft\.AspNetCore\.Authentication\.Google') {
        Write-Host "✅ Google認証パッケージが既にインストールされています" -ForegroundColor Green
    } else {
        Write-Host "❌ Google認証パッケージが必要です" -ForegroundColor Red
        
        if ($Update) {
            Write-Host "Google認証パッケージを追加しています..." -ForegroundColor Yellow
            Set-Location "TranscriptCleanerMvc/src/TranscriptCleanerMvc.Web"
            dotnet add package Microsoft.AspNetCore.Authentication.Google --version 9.0.0
            Set-Location "../../../"
        }
    }
}

if ($Update -and -not $CheckOnly) {
    Write-Host "`n=== パッケージ更新実行 ===" -ForegroundColor Green
    
    foreach ($project in $projects) {
        if (Test-Path $project) {
            $projectDir = Split-Path $project -Parent
            Write-Host "更新中: $project" -ForegroundColor Yellow
            
            Set-Location $projectDir
            
            foreach ($package in $requiredPackages.GetEnumerator()) {
                dotnet add package $package.Key --version $package.Value
            }
            
            Set-Location (Resolve-Path "../../../").Path
        }
    }
    
    Write-Host "✅ パッケージ更新完了" -ForegroundColor Green
}

Write-Host "`n=== 確認完了 ===" -ForegroundColor Green

if (-not $CheckOnly -and -not $Update) {
    Write-Host "`nオプション:" -ForegroundColor Yellow
    Write-Host "  -CheckOnly : パッケージ確認のみ実行" -ForegroundColor Gray
    Write-Host "  -Update    : パッケージを最新版に更新" -ForegroundColor Gray
}