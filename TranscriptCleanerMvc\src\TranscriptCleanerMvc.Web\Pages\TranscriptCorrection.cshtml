@page
@model TranscriptCleanerMvc.Web.Pages.TranscriptCorrectionPageModel
@using Microsoft.AspNetCore.Mvc.Localization
@using TranscriptCleanerMvc.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IHtmlLocalizer<TranscriptCleanerMvcResource> L
@inject IPageLayout PageLayout
@{
    ViewBag.PageTitle = L["TranscriptCorrection"];
    PageLayout.Content.MenuItemName = "TranscriptCorrection";
}

@section styles {
    <style>
        .transcript-layout {
            display: flex;
            height: calc(100vh - 120px);
            overflow: hidden;
        }
        
        .transcript-sidebar {
            width: 320px;
            background: #343a40;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        
        .transcript-main-container {
            flex: 1;
            padding: 20px;
            overflow: hidden;
        }
        
        .transcript-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .transcript-header h4 {
            margin: 0;
            color: #495057;
        }
        
        .transcript-header .fas {
            margin-right: 10px;
            color: #007bff;
        }
        
        .comparison-container {
            display: flex;
            gap: 20px;
            height: calc(100vh - 300px);
            min-height: 400px;
        }
        
        .comparison-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .comparison-header {
            background: #f8f9fa;
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .comparison-header h6 {
            margin: 0;
            font-weight: 500;
            color: #495057;
        }
        
        .header-actions {
            display: flex;
            gap: 5px;
        }
        
        .btn-header-action {
            background: none;
            border: none;
            color: #6c757d;
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .btn-header-action:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .comparison-content {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background: #fff;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            line-height: 1.5;
        }
        
        .comparison-content.editable {
            outline: none;
        }
        
        .comparison-content.editable:focus {
            background: #fffbf0;
        }
        
        .placeholder-text {
            color: #6c757d;
            font-style: italic;
            text-align: center;
            padding-top: 50px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #6c757d;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            z-index: 1000;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding: 10px 0;
        }
        
        .btn-action {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
        }
        
        .btn-action.btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-action.btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-action:hover {
            opacity: 0.9;
        }
        
        .diff-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }
        
        .diff-highlight {
            background-color: #fff3cd !important;
            border: 1px solid #ffeaa7;
            padding: 2px 4px;
            border-radius: 3px;
            margin: 1px;
        }
        
        .diff-added {
            background-color: #d4edda !important;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .diff-removed {
            background-color: #f8d7da !important;
            border: 1px solid #f5c6cb;
            color: #721c24;
            text-decoration: line-through;
        }
        
        .diff-unchanged {
            background-color: transparent;
            border: none;
        }
    </style>
}

<div class="transcript-layout">
    <!-- カスタムサイドバー -->
    <div class="transcript-sidebar">
        @await Component.InvokeAsync("TranscriptSidebar")
    </div>
    
    <!-- メインコンテンツ -->
    <div class="transcript-main-container">
    <!-- ヘッダー -->
    <div class="transcript-header">
        <h4><i class="fas fa-edit"></i>@L["TranscriptCorrection"]</h4>
    </div>
    
    <!-- 読み込み中表示 -->
    <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>@L["Processing"]</p>
    </div>
    
    <!-- 比較表示エリア -->
    <div class="comparison-container">
        <div class="comparison-column">
            <div class="comparison-header">
                <h6>訂正前テキスト（編集不可）</h6>
                <div class="header-actions">
                    <button type="button" class="btn-header-action" onclick="clearOriginalText()" title="クリア">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div id="originalPanel" class="comparison-content">
                <div class="placeholder-text" id="originalPlaceholder">
                    ここに訂正前のテキストが表示されます。<br>
                    左側のサイドバーからファイルをアップロードしてください。
                </div>
            </div>
        </div>
        
        <div class="comparison-column">
            <div class="comparison-header">
                <h6>訂正後テキスト（手修正可）</h6>
                <div class="header-actions">
                    <button type="button" class="btn-header-action" onclick="copyToOriginal()" title="左側にコピー">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button type="button" class="btn-header-action" onclick="copyResult()" title="クリップボードにコピー">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button type="button" class="btn-header-action" onclick="downloadResult()" title="ダウンロード">
                        <i class="fas fa-download"></i>
                    </button>
                    <button type="button" class="btn-header-action" onclick="clearCorrectedText()" title="クリア">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div id="correctedPanel" class="comparison-content editable" contenteditable="true">
                <div class="placeholder-text" id="correctedPlaceholder">
                    ここに訂正結果が表示されます。<br>
                    訂正後に手動で編集できます。
                </div>
            </div>
        </div>
    </div>
    
    <!-- アクションバー -->
    <div class="action-bar">
        <div class="action-left">
            <label class="diff-toggle">
                <input type="checkbox" id="showDiff" onchange="toggleDiffHighlight()">
                <span>差分をハイライト</span>
            </label>
        </div>
        <div class="action-right">
            <button type="button" class="btn-action btn-secondary" onclick="resetAll()">
                <i class="fas fa-undo"></i> リセット
            </button>
            <button type="button" class="btn-action btn-primary" onclick="reprocessCorrectedText()">
                <i class="fas fa-redo"></i> 再訂正
            </button>
        </div>
    </div>
</div>

@section scripts {
    <script>
        // サイドバーとの連携関数
        window.displayOriginalTextInMainPanel = function(text) {
            const originalPanel = document.getElementById('originalPanel');
            const placeholder = document.getElementById('originalPlaceholder');
            
            if (placeholder) {
                placeholder.style.display = 'none';
            }
            originalPanel.textContent = text;
        };
        
        window.displayCorrectedTextInMainPanel = function(text) {
            const correctedPanel = document.getElementById('correctedPanel');
            const placeholder = document.getElementById('correctedPlaceholder');
            
            if (placeholder) {
                placeholder.style.display = 'none';
            }
            correctedPanel.textContent = text;
            
            // 差分表示が有効な場合は更新
            const showDiff = document.getElementById('showDiff');
            if (showDiff && showDiff.checked) {
                setTimeout(() => toggleDiffHighlight(), 100);
            }
        };
        
        window.showMainPageLoading = function() {
            document.getElementById('loading').style.display = 'block';
        };
        
        window.hideMainPageLoading = function() {
            document.getElementById('loading').style.display = 'none';
        };
        
        // パネル操作関数
        function clearOriginalText() {
            if (window.sidebarOriginalText !== undefined) {
                window.sidebarOriginalText = '';
            }
            const originalPanel = document.getElementById('originalPanel');
            const placeholder = document.getElementById('originalPlaceholder');
            
            originalPanel.textContent = '';
            if (placeholder) {
                placeholder.style.display = 'block';
            }
        }
        
        function clearCorrectedText() {
            if (window.sidebarCorrectedText !== undefined) {
                window.sidebarCorrectedText = '';
            }
            const correctedPanel = document.getElementById('correctedPanel');
            const placeholder = document.getElementById('correctedPlaceholder');
            
            correctedPanel.textContent = '';
            if (placeholder) {
                placeholder.style.display = 'block';
            }
        }
        
        function copyToOriginal() {
            const correctedText = document.getElementById('correctedPanel').textContent;
            if (correctedText) {
                if (window.sidebarOriginalText !== undefined) {
                    window.sidebarOriginalText = correctedText;
                }
                const originalPanel = document.getElementById('originalPanel');
                const placeholder = document.getElementById('originalPlaceholder');
                
                if (placeholder) {
                    placeholder.style.display = 'none';
                }
                originalPanel.textContent = correctedText;
                
                // 差分表示が有効な場合は更新
                const showDiff = document.getElementById('showDiff');
                if (showDiff && showDiff.checked) {
                    setTimeout(() => toggleDiffHighlight(), 100);
                }
            }
        }
        
        function copyResult() {
            const textToCopy = document.getElementById('correctedPanel').textContent;
            if (textToCopy) {
                navigator.clipboard.writeText(textToCopy).then(() => {
                    alert('コピーしました');
                });
            }
        }
        
        function downloadResult() {
            const textToDownload = document.getElementById('correctedPanel').textContent;
            if (textToDownload) {
                const blob = new Blob([textToDownload], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `corrected_transcript_${new Date().toISOString().slice(0,10)}.txt`;
                a.click();
                URL.revokeObjectURL(url);
            }
        }
        
        function resetAll() {
            if (confirm('すべての内容をリセットしますか？')) {
                // 差分表示をリセット
                const showDiff = document.getElementById('showDiff');
                if (showDiff.checked) {
                    showDiff.checked = false;
                    restoreOriginalText(document.getElementById('originalPanel'), document.getElementById('correctedPanel'));
                }
                
                if (window.sidebarOriginalText !== undefined) {
                    window.sidebarOriginalText = '';
                }
                if (window.sidebarCorrectedText !== undefined) {
                    window.sidebarCorrectedText = '';
                }
                if (window.sidebarWordListCsv !== undefined) {
                    window.sidebarWordListCsv = '';
                }
                
                clearOriginalText();
                clearCorrectedText();
                
                // サイドバーのリセット
                const txtUploadText = document.getElementById('sidebarTxtUploadText');
                const csvUploadText = document.getElementById('sidebarCsvUploadText');
                const customPrompt = document.getElementById('sidebarCustomPrompt');
                
                if (txtUploadText) txtUploadText.textContent = '議事録ファイルを選択';
                if (csvUploadText) csvUploadText.textContent = 'CSVファイルを選択';
                if (customPrompt) customPrompt.value = '';
                
                document.querySelectorAll('.sidebar-upload-area').forEach(area => {
                    area.classList.remove('has-file');
                });
            }
        }
        
        function reprocessCorrectedText() {
            const currentCorrected = document.getElementById('correctedPanel').textContent;
            if (currentCorrected && currentCorrected.trim()) {
                if (window.sidebarOriginalText !== undefined) {
                    window.sidebarOriginalText = currentCorrected;
                }
                const originalPanel = document.getElementById('originalPanel');
                const placeholder = document.getElementById('originalPlaceholder');
                
                if (placeholder) {
                    placeholder.style.display = 'none';
                }
                originalPanel.textContent = currentCorrected;
                
                // サイドバーの実行関数を呼び出し
                if (typeof sidebarExecuteCorrection === 'function') {
                    sidebarExecuteCorrection();
                }
            } else {
                alert('再訂正するテキストがありません。');
            }
        }
        
        function toggleDiffHighlight() {
            const showDiff = document.getElementById('showDiff').checked;
            const originalPanel = document.getElementById('originalPanel');
            const correctedPanel = document.getElementById('correctedPanel');
            
            if (showDiff) {
                // 差分を計算して表示
                const originalText = getPlainText(originalPanel);
                const correctedText = getPlainText(correctedPanel);
                
                if (originalText && correctedText) {
                    const diffs = computeDiff(originalText, correctedText);
                    displayDiffs(originalPanel, correctedPanel, diffs);
                }
            } else {
                // 差分表示を解除
                restoreOriginalText(originalPanel, correctedPanel);
            }
        }
        
        function getPlainText(element) {
            // プレースホルダーがある場合は空文字を返す
            const placeholder = element.querySelector('.placeholder-text');
            if (placeholder && placeholder.style.display !== 'none') {
                return '';
            }
            return element.textContent || '';
        }
        
        function computeDiff(text1, text2) {
            // シンプルな単語レベルの差分計算
            const words1 = text1.split(/(\s+)/);
            const words2 = text2.split(/(\s+)/);
            
            const diffs = [];
            let i = 0, j = 0;
            
            while (i < words1.length || j < words2.length) {
                if (i >= words1.length) {
                    // text2に追加された部分
                    diffs.push({ type: 'added', text: words2[j] });
                    j++;
                } else if (j >= words2.length) {
                    // text1から削除された部分
                    diffs.push({ type: 'removed', text: words1[i] });
                    i++;
                } else if (words1[i] === words2[j]) {
                    // 変更なし
                    diffs.push({ type: 'unchanged', text: words1[i] });
                    i++;
                    j++;
                } else {
                    // 変更された部分を検出
                    let found = false;
                    
                    // text2の先を見て一致する部分を探す
                    for (let k = j + 1; k < Math.min(j + 10, words2.length); k++) {
                        if (words1[i] === words2[k]) {
                            // text2に追加された部分
                            for (let l = j; l < k; l++) {
                                diffs.push({ type: 'added', text: words2[l] });
                            }
                            j = k;
                            found = true;
                            break;
                        }
                    }
                    
                    if (!found) {
                        // text1の先を見て一致する部分を探す
                        for (let k = i + 1; k < Math.min(i + 10, words1.length); k++) {
                            if (words1[k] === words2[j]) {
                                // text1から削除された部分
                                for (let l = i; l < k; l++) {
                                    diffs.push({ type: 'removed', text: words1[l] });
                                }
                                i = k;
                                found = true;
                                break;
                            }
                        }
                    }
                    
                    if (!found) {
                        // 単純な置換
                        diffs.push({ type: 'removed', text: words1[i] });
                        diffs.push({ type: 'added', text: words2[j] });
                        i++;
                        j++;
                    }
                }
            }
            
            return diffs;
        }
        
        function displayDiffs(originalPanel, correctedPanel, diffs) {
            // 元のテキストを保存（復元用）
            if (!originalPanel.dataset.originalText) {
                originalPanel.dataset.originalText = originalPanel.textContent;
            }
            if (!correctedPanel.dataset.originalText) {
                correctedPanel.dataset.originalText = correctedPanel.textContent;
            }
            
            // 差分を表示
            let originalHtml = '';
            let correctedHtml = '';
            
            diffs.forEach(diff => {
                const escapedText = escapeHtml(diff.text);
                
                switch (diff.type) {
                    case 'unchanged':
                        originalHtml += `<span class="diff-unchanged">${escapedText}</span>`;
                        correctedHtml += `<span class="diff-unchanged">${escapedText}</span>`;
                        break;
                    case 'removed':
                        originalHtml += `<span class="diff-highlight diff-removed">${escapedText}</span>`;
                        break;
                    case 'added':
                        correctedHtml += `<span class="diff-highlight diff-added">${escapedText}</span>`;
                        break;
                }
            });
            
            // HTMLを設定
            originalPanel.innerHTML = originalHtml || originalPanel.dataset.originalText;
            correctedPanel.innerHTML = correctedHtml || correctedPanel.dataset.originalText;
        }
        
        function restoreOriginalText(originalPanel, correctedPanel) {
            // 元のテキストを復元
            if (originalPanel.dataset.originalText) {
                originalPanel.textContent = originalPanel.dataset.originalText;
                delete originalPanel.dataset.originalText;
            }
            if (correctedPanel.dataset.originalText) {
                correctedPanel.textContent = correctedPanel.dataset.originalText;
                delete correctedPanel.dataset.originalText;
            }
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
}
