/*!
 * jQuery Form Plugin
 * version: 4.3.0
 * Requires jQuery v1.7.2 or later
 * Project repository: https://github.com/jquery-form/form

 * Copyright 2017 <PERSON>
 * Copyright 2006 M. Alsup

 * Dual licensed under the LGPL-2.1+ or MIT licenses
 * https://github.com/jquery-form/form#license

 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=function(t,r){return void 0===r&&(r="undefined"!=typeof window?require("jquery"):require("jquery")(t)),e(r),r}:e(jQuery)}(function(e){"use strict";var t=/\r?\n/g,r={};r.fileapi=void 0!==e('<input type="file">').get(0).files,r.formdata=void 0!==window.FormData;var a=!!e.fn.prop;function n(t){var r=t.data;t.isDefaultPrevented()||(t.preventDefault(),e(t.target).closest("form").ajaxSubmit(r))}function i(t){var r=t.target,a=e(r);if(!a.is("[type=submit],[type=image]")){var n=a.closest("[type=submit]");if(0===n.length)return;r=n[0]}var i=r.form;if(i.clk=r,"image"===r.type)if(void 0!==t.offsetX)i.clk_x=t.offsetX,i.clk_y=t.offsetY;else if("function"==typeof e.fn.offset){var o=a.offset();i.clk_x=t.pageX-o.left,i.clk_y=t.pageY-o.top}else i.clk_x=t.pageX-r.offsetLeft,i.clk_y=t.pageY-r.offsetTop;setTimeout(function(){i.clk=i.clk_x=i.clk_y=null},100)}function o(){if(e.fn.ajaxSubmit.debug){var t="[jquery.form] "+Array.prototype.join.call(arguments,"");window.console&&window.console.log?window.console.log(t):window.opera&&window.opera.postError&&window.opera.postError(t)}}e.fn.attr2=function(){if(!a)return this.attr.apply(this,arguments);var e=this.prop.apply(this,arguments);return e&&e.jquery||"string"==typeof e?e:this.attr.apply(this,arguments)},e.fn.ajaxSubmit=function(t,n,i,s){if(!this.length)return o("ajaxSubmit: skipping submit process - no element selected"),this;var u,c,l,f,d=this;"function"==typeof t?t={success:t}:"string"==typeof t||!1===t&&arguments.length>0?(t={url:t,data:n,dataType:i},"function"==typeof s&&(t.success=s)):void 0===t&&(t={}),u=t.method||t.type||this.attr2("method"),(l=(l="string"==typeof(c=t.url||this.attr2("action"))?e.trim(c):"")||window.location.href||"")&&(l=(l.match(/^([^#]+)/)||[])[1]),f=/(MSIE|Trident)/.test(navigator.userAgent||"")&&/^https/i.test(window.location.href||"")?"javascript:false":"about:blank",t=e.extend(!0,{url:l,success:e.ajaxSettings.success,type:u||e.ajaxSettings.type,iframeSrc:f},t);var m={};if(this.trigger("form-pre-serialize",[this,t,m]),m.veto)return o("ajaxSubmit: submit vetoed via form-pre-serialize trigger"),this;if(t.beforeSerialize&&!1===t.beforeSerialize(this,t))return o("ajaxSubmit: submit aborted via beforeSerialize callback"),this;var p=t.traditional;void 0===p&&(p=e.ajaxSettings.traditional);var h,v=[],g=this.formToArray(t.semantic,v,t.filtering);if(t.data){var x=e.isFunction(t.data)?t.data(g):t.data;t.extraData=x,h=e.param(x,p)}if(t.beforeSubmit&&!1===t.beforeSubmit(g,this,t))return o("ajaxSubmit: submit aborted via beforeSubmit callback"),this;if(this.trigger("form-submit-validate",[g,this,t,m]),m.veto)return o("ajaxSubmit: submit vetoed via form-submit-validate trigger"),this;var y=e.param(g,p);h&&(y=y?y+"&"+h:h),"GET"===t.type.toUpperCase()?(t.url+=(t.url.indexOf("?")>=0?"&":"?")+y,t.data=null):t.data=y;var b=[];if(t.resetForm&&b.push(function(){d.resetForm()}),t.clearForm&&b.push(function(){d.clearForm(t.includeHidden)}),!t.dataType&&t.target){var T=t.success||function(){};b.push(function(r,a,n){var i=arguments,o=t.replaceTarget?"replaceWith":"html";"html"==o&&(r=e.parseHTML(e("<div>").text(r).html())),e(t.target)[o](r).each(function(){T.apply(this,i)})})}else t.success&&(e.isArray(t.success)?e.merge(b,t.success):b.push(t.success));if(t.success=function(e,r,a){for(var n=t.context||this,i=0,o=b.length;i<o;i++)b[i].apply(n,[e,r,a||d,d])},t.error){var j=t.error;t.error=function(e,r,a){var n=t.context||this;j.apply(n,[e,r,a,d])}}if(t.complete){var w=t.complete;t.complete=function(e,r){var a=t.context||this;w.apply(a,[e,r,d])}}var S=e("input[type=file]:enabled",this).filter(function(){return""!==e(this).val()}).length>0,k="multipart/form-data",D=d.attr("enctype")===k||d.attr("encoding")===k,A=r.fileapi&&r.formdata;o("fileAPI :"+A);var F,L=(S||D)&&!A;!1!==t.iframe&&(t.iframe||L)?t.closeKeepAlive?e.get(t.closeKeepAlive,function(){F=M(g)}):F=M(g):F=(S||D)&&A?function(r){for(var a=new FormData,n=0;n<r.length;n++)a.append(r[n].name,r[n].value);if(t.extraData){var i=function(r){var a,n,i=e.param(r,t.traditional).split("&"),o=i.length,s=[];for(a=0;a<o;a++)i[a]=i[a].replace(/\+/g," "),n=i[a].split("="),s.push([decodeURIComponent(n[0]),decodeURIComponent(n[1])]);return s}(t.extraData);for(n=0;n<i.length;n++)i[n]&&a.append(i[n][0],i[n][1])}t.data=null;var o=e.extend(!0,{},e.ajaxSettings,t,{contentType:!1,processData:!1,cache:!1,type:u||"POST"});t.uploadProgress&&(o.xhr=function(){var r=e.ajaxSettings.xhr();return r.upload&&r.upload.addEventListener("progress",function(e){var r=0,a=e.loaded||e.position,n=e.total;e.lengthComputable&&(r=Math.ceil(a/n*100)),t.uploadProgress(e,a,n,r)},!1),r});o.data=null;var s=o.beforeSend;return o.beforeSend=function(e,r){t.formData?r.data=t.formData:r.data=a,s&&s.call(this,e,r)},e.ajax(o)}(g):e.ajax(t),d.removeData("jqxhr").data("jqxhr",F);for(var E=0;E<v.length;E++)v[E]=null;return this.trigger("form-submit-notify",[this,t]),this;function M(r){var n,i,s,c,l,f,m,p,h,g,x,y,b=d[0],T=e.Deferred();if(T.abort=function(e){p.abort(e)},r)for(i=0;i<v.length;i++)n=e(v[i]),a?n.prop("disabled",!1):n.removeAttr("disabled");(s=e.extend(!0,{},e.ajaxSettings,t)).context=s.context||s,l="jqFormIO"+(new Date).getTime();var j=b.ownerDocument,w=d.closest("body");if(s.iframeTarget?(g=(f=e(s.iframeTarget,j)).attr2("name"))?l=g:f.attr2("name",l):(f=e('<iframe name="'+l+'" src="'+s.iframeSrc+'" />',j)).css({position:"absolute",top:"-1000px",left:"-1000px"}),m=f[0],p={aborted:0,responseText:null,responseXML:null,status:0,statusText:"n/a",getAllResponseHeaders:function(){},getResponseHeader:function(){},setRequestHeader:function(){},abort:function(t){var r="timeout"===t?"timeout":"aborted";o("aborting upload... "+r),this.aborted=1;try{m.contentWindow.document.execCommand&&m.contentWindow.document.execCommand("Stop")}catch(e){}f.attr("src",s.iframeSrc),p.error=r,s.error&&s.error.call(s.context,p,r,t),c&&e.event.trigger("ajaxError",[p,s,r]),s.complete&&s.complete.call(s.context,p,r)}},(c=s.global)&&0==e.active++&&e.event.trigger("ajaxStart"),c&&e.event.trigger("ajaxSend",[p,s]),s.beforeSend&&!1===s.beforeSend.call(s.context,p,s))return s.global&&e.active--,T.reject(),T;if(p.aborted)return T.reject(),T;(h=b.clk)&&(g=h.name)&&!h.disabled&&(s.extraData=s.extraData||{},s.extraData[g]=h.value,"image"===h.type&&(s.extraData[g+".x"]=b.clk_x,s.extraData[g+".y"]=b.clk_y));var S=1,k=2;function D(e){var t=null;try{e.contentWindow&&(t=e.contentWindow.document)}catch(e){o("cannot get iframe.contentWindow document: "+e)}if(t)return t;try{t=e.contentDocument?e.contentDocument:e.document}catch(r){o("cannot get iframe.contentDocument: "+r),t=e.document}return t}var A=e("meta[name=csrf-token]").attr("content"),F=e("meta[name=csrf-param]").attr("content");function L(){var t=d.attr2("target"),r=d.attr2("action"),a=d.attr("enctype")||d.attr("encoding")||"multipart/form-data";b.setAttribute("target",l),u&&!/post/i.test(u)||b.setAttribute("method","POST"),r!==s.url&&b.setAttribute("action",s.url),s.skipEncodingOverride||u&&!/post/i.test(u)||d.attr({encoding:"multipart/form-data",enctype:"multipart/form-data"}),s.timeout&&(y=setTimeout(function(){x=!0,C(S)},s.timeout));var n=[];try{if(s.extraData)for(var i in s.extraData)s.extraData.hasOwnProperty(i)&&(e.isPlainObject(s.extraData[i])&&s.extraData[i].hasOwnProperty("name")&&s.extraData[i].hasOwnProperty("value")?n.push(e('<input type="hidden" name="'+s.extraData[i].name+'">',j).val(s.extraData[i].value).appendTo(b)[0]):n.push(e('<input type="hidden" name="'+i+'">',j).val(s.extraData[i]).appendTo(b)[0]));s.iframeTarget||f.appendTo(w),m.attachEvent?m.attachEvent("onload",C):m.addEventListener("load",C,!1),setTimeout(function e(){try{var t=D(m).readyState;o("state = "+t),t&&"uninitialized"===t.toLowerCase()&&setTimeout(e,50)}catch(e){o("Server abort: ",e," (",e.name,")"),C(k),y&&clearTimeout(y),y=void 0}},15);try{b.submit()}catch(e){document.createElement("form").submit.apply(b)}}finally{b.setAttribute("action",r),b.setAttribute("enctype",a),t?b.setAttribute("target",t):d.removeAttr("target"),e(n).remove()}}F&&A&&(s.extraData=s.extraData||{},s.extraData[F]=A),s.forceSync?L():setTimeout(L,10);var E,M,O,X=50;function C(t){if(!p.aborted&&!O){if((M=D(m))||(o("cannot access response document"),t=k),t===S&&p)return p.abort("timeout"),void T.reject(p,"timeout");if(t===k&&p)return p.abort("server abort"),void T.reject(p,"error","server abort");if(M&&M.location.href!==s.iframeSrc||x){m.detachEvent?m.detachEvent("onload",C):m.removeEventListener("load",C,!1);var r,a="success";try{if(x)throw"timeout";var n="xml"===s.dataType||M.XMLDocument||e.isXMLDoc(M);if(o("isXml="+n),!n&&window.opera&&(null===M.body||!M.body.innerHTML)&&--X)return o("requeing onLoad callback, DOM not available"),void setTimeout(C,250);var i=M.body?M.body:M.documentElement;p.responseText=i?i.innerHTML:null,p.responseXML=M.XMLDocument?M.XMLDocument:M,n&&(s.dataType="xml"),p.getResponseHeader=function(e){return{"content-type":s.dataType}[e.toLowerCase()]},i&&(p.status=Number(i.getAttribute("status"))||p.status,p.statusText=i.getAttribute("statusText")||p.statusText);var u=(s.dataType||"").toLowerCase(),l=/(json|script|text)/.test(u);if(l||s.textarea){var d=M.getElementsByTagName("textarea")[0];if(d)p.responseText=d.value,p.status=Number(d.getAttribute("status"))||p.status,p.statusText=d.getAttribute("statusText")||p.statusText;else if(l){var h=M.getElementsByTagName("pre")[0],v=M.getElementsByTagName("body")[0];h?p.responseText=h.textContent?h.textContent:h.innerText:v&&(p.responseText=v.textContent?v.textContent:v.innerText)}}else"xml"===u&&!p.responseXML&&p.responseText&&(p.responseXML=q(p.responseText));try{E=_(p,u,s)}catch(e){a="parsererror",p.error=r=e||a}}catch(e){o("error caught: ",e),a="error",p.error=r=e||a}p.aborted&&(o("upload aborted"),a=null),p.status&&(a=p.status>=200&&p.status<300||304===p.status?"success":"error"),"success"===a?(s.success&&s.success.call(s.context,E,"success",p),T.resolve(p.responseText,"success",p),c&&e.event.trigger("ajaxSuccess",[p,s])):a&&(void 0===r&&(r=p.statusText),s.error&&s.error.call(s.context,p,a,r),T.reject(p,"error",r),c&&e.event.trigger("ajaxError",[p,s,r])),c&&e.event.trigger("ajaxComplete",[p,s]),c&&!--e.active&&e.event.trigger("ajaxStop"),s.complete&&s.complete.call(s.context,p,a),O=!0,s.timeout&&clearTimeout(y),setTimeout(function(){s.iframeTarget?f.attr("src",s.iframeSrc):f.remove(),p.responseXML=null},100)}}}var q=e.parseXML||function(e,t){return window.ActiveXObject?((t=new ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e)):t=(new DOMParser).parseFromString(e,"text/xml"),t&&t.documentElement&&"parsererror"!==t.documentElement.nodeName?t:null},N=e.parseJSON||function(e){return window.console.error("jquery.parseJSON is undefined"),null},_=function(t,r,a){var n=t.getResponseHeader("content-type")||"",i=("xml"===r||!r)&&n.indexOf("xml")>=0,o=i?t.responseXML:t.responseText;return i&&"parsererror"===o.documentElement.nodeName&&e.error&&e.error("parsererror"),a&&a.dataFilter&&(o=a.dataFilter(o,r)),"string"==typeof o&&(("json"===r||!r)&&n.indexOf("json")>=0?o=N(o):("script"===r||!r)&&n.indexOf("javascript")>=0&&e.globalEval(o)),o};return T}},e.fn.ajaxForm=function(t,r,a,s){if(("string"==typeof t||!1===t&&arguments.length>0)&&(t={url:t,data:r,dataType:a},"function"==typeof s&&(t.success=s)),(t=t||{}).delegation=t.delegation&&e.isFunction(e.fn.on),!t.delegation&&0===this.length){var u={s:this.selector,c:this.context};return!e.isReady&&u.s?(o("DOM not ready, queuing ajaxForm"),e(function(){e(u.s,u.c).ajaxForm(t)}),this):(o("terminating; zero elements found by selector"+(e.isReady?"":" (DOM not ready)")),this)}return t.delegation?(e(document).off("submit.form-plugin",this.selector,n).off("click.form-plugin",this.selector,i).on("submit.form-plugin",this.selector,t,n).on("click.form-plugin",this.selector,t,i),this):(t.beforeFormUnbind&&t.beforeFormUnbind(this,t),this.ajaxFormUnbind().on("submit.form-plugin",t,n).on("click.form-plugin",t,i))},e.fn.ajaxFormUnbind=function(){return this.off("submit.form-plugin click.form-plugin")},e.fn.formToArray=function(t,a,n){var i=[];if(0===this.length)return i;var o,s,u,c,l,f,d,m,p=this[0],h=this.attr("id"),v=t||void 0===p.elements?p.getElementsByTagName("*"):p.elements;if(v&&(v=e.makeArray(v)),h&&(t||/(Edge|Trident)\//.test(navigator.userAgent))&&(o=e(':input[form="'+h+'"]').get()).length&&(v=(v||[]).concat(o)),!v||!v.length)return i;for(e.isFunction(n)&&(v=e.map(v,n)),s=0,d=v.length;s<d;s++)if((c=(f=v[s]).name)&&!f.disabled)if(t&&p.clk&&"image"===f.type)p.clk===f&&(i.push({name:c,value:e(f).val(),type:f.type}),i.push({name:c+".x",value:p.clk_x},{name:c+".y",value:p.clk_y}));else if((l=e.fieldValue(f,!0))&&l.constructor===Array)for(a&&a.push(f),u=0,m=l.length;u<m;u++)i.push({name:c,value:l[u]});else if(r.fileapi&&"file"===f.type){a&&a.push(f);var g=f.files;if(g.length)for(u=0;u<g.length;u++)i.push({name:c,value:g[u],type:f.type});else i.push({name:c,value:"",type:f.type})}else null!=l&&(a&&a.push(f),i.push({name:c,value:l,type:f.type,required:f.required}));if(!t&&p.clk){var x=e(p.clk),y=x[0];(c=y.name)&&!y.disabled&&"image"===y.type&&(i.push({name:c,value:x.val()}),i.push({name:c+".x",value:p.clk_x},{name:c+".y",value:p.clk_y}))}return i},e.fn.formSerialize=function(t){return e.param(this.formToArray(t))},e.fn.fieldSerialize=function(t){var r=[];return this.each(function(){var a=this.name;if(a){var n=e.fieldValue(this,t);if(n&&n.constructor===Array)for(var i=0,o=n.length;i<o;i++)r.push({name:a,value:n[i]});else null!=n&&r.push({name:this.name,value:n})}}),e.param(r)},e.fn.fieldValue=function(t){for(var r=[],a=0,n=this.length;a<n;a++){var i=this[a],o=e.fieldValue(i,t);null==o||o.constructor===Array&&!o.length||(o.constructor===Array?e.merge(r,o):r.push(o))}return r},e.fieldValue=function(r,a){var n=r.name,i=r.type,o=r.tagName.toLowerCase();if(void 0===a&&(a=!0),a&&(!n||r.disabled||"reset"===i||"button"===i||("checkbox"===i||"radio"===i)&&!r.checked||("submit"===i||"image"===i)&&r.form&&r.form.clk!==r||"select"===o&&-1===r.selectedIndex))return null;if("select"===o){var s=r.selectedIndex;if(s<0)return null;for(var u=[],c=r.options,l="select-one"===i,f=l?s+1:c.length,d=l?s:0;d<f;d++){var m=c[d];if(m.selected&&!m.disabled){var p=m.value;if(p||(p=m.attributes&&m.attributes.value&&!m.attributes.value.specified?m.text:m.value),l)return p;u.push(p)}}return u}return e(r).val().replace(t,"\r\n")},e.fn.clearForm=function(t){return this.each(function(){e("input,select,textarea",this).clearFields(t)})},e.fn.clearFields=e.fn.clearInputs=function(t){var r=/^(?:color|date|datetime|email|month|number|password|range|search|tel|text|time|url|week)$/i;return this.each(function(){var a=this.type,n=this.tagName.toLowerCase();r.test(a)||"textarea"===n?this.value="":"checkbox"===a||"radio"===a?this.checked=!1:"select"===n?this.selectedIndex=-1:"file"===a?/MSIE/.test(navigator.userAgent)?e(this).replaceWith(e(this).clone(!0)):e(this).val(""):t&&(!0===t&&/hidden/.test(a)||"string"==typeof t&&e(this).is(t))&&(this.value="")})},e.fn.resetForm=function(){return this.each(function(){var t=e(this),r=this.tagName.toLowerCase();switch(r){case"input":this.checked=this.defaultChecked;case"textarea":return this.value=this.defaultValue,!0;case"option":case"optgroup":var a=t.parents("select");return a.length&&a[0].multiple?"option"===r?this.selected=this.defaultSelected:t.find("option").resetForm():a.resetForm(),!0;case"select":return t.find("option").each(function(e){if(this.selected=this.defaultSelected,this.defaultSelected&&!t[0].multiple)return t[0].selectedIndex=e,!1}),!0;case"label":var n=e(t.attr("for")),i=t.find("input,select,textarea");return n[0]&&i.unshift(n[0]),i.resetForm(),!0;case"form":return("function"==typeof this.reset||"object"==typeof this.reset&&!this.reset.nodeType)&&this.reset(),!0;default:return t.find("form,input,label,select,textarea").resetForm(),!0}})},e.fn.enable=function(e){return void 0===e&&(e=!0),this.each(function(){this.disabled=!e})},e.fn.selected=function(t){return void 0===t&&(t=!0),this.each(function(){var r=this.type;if("checkbox"===r||"radio"===r)this.checked=t;else if("option"===this.tagName.toLowerCase()){var a=e(this).parent("select");t&&a[0]&&"select-one"===a[0].type&&a.find("option").selected(!1),this.selected=t}})},e.fn.ajaxSubmit.debug=!1});