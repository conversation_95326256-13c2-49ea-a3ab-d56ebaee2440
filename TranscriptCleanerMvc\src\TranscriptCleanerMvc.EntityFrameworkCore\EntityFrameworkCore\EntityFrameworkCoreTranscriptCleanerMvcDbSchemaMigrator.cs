﻿using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using TranscriptCleanerMvc.Data;
using Volo.Abp.DependencyInjection;

namespace TranscriptCleanerMvc.EntityFrameworkCore;

public class EntityFrameworkCoreTranscriptCleanerMvcDbSchemaMigrator
    : ITranscriptCleanerMvcDbSchemaMigrator, ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public EntityFrameworkCoreTranscriptCleanerMvcDbSchemaMigrator(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        /* We intentionally resolving the TranscriptCleanerMvcDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<TranscriptCleanerMvcDbContext>()
            .Database
            .MigrateAsync();
    }
}
