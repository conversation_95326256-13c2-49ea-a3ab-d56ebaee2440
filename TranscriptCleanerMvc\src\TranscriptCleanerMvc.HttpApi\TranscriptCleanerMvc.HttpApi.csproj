﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TranscriptCleanerMvc</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\TranscriptCleanerMvc.Application.Contracts\TranscriptCleanerMvc.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="9.2.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="9.2.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="9.2.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.HttpApi" Version="9.2.0" />
    <PackageReference Include="Volo.Abp.Account.HttpApi" Version="9.2.0" />
    <PackageReference Include="Volo.Abp.TenantManagement.HttpApi" Version="9.2.0" />
  </ItemGroup>

</Project>
