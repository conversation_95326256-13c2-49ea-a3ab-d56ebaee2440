using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TranscriptCleanerMvc.WordLists.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TranscriptCleanerMvc.WordLists;

public interface IWordListAppService : ICrudAppService<
    WordListDto,
    Guid,
    WordListFilterDto,
    CreateWordListDto,
    UpdateWordListDto>
{
    Task<ListResultDto<WordListDto>> GetActiveByLanguageAsync(string language);
    
    Task<ListResultDto<WordListDto>> GetMostUsedAsync(int count = 10, string? language = null);
    
    Task<ListResultDto<string>> GetCategoriesAsync(string? language = null);
    
    Task<WordListDto> ImportFromCsvAsync(ImportWordListDto input);
    
    Task<string> ExportToCsvAsync(string? language = null, string? category = null);
    
    Task ActivateAsync(Guid id);
    
    Task DeactivateAsync(Guid id);
    
    Task<bool> CheckDuplicateAsync(string incorrectWord, string language);
}