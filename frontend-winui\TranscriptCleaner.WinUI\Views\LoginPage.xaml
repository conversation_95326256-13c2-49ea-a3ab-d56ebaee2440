<Page
    x:Class="TranscriptCleaner.WinUI.Views.LoginPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <Grid>
        <!-- グラデーション背景 -->
        <Rectangle>
            <Rectangle.Fill>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667eea" Offset="0.0" />
                    <GradientStop Color="#764ba2" Offset="1.0" />
                </LinearGradientBrush>
            </Rectangle.Fill>
        </Rectangle>

        <!-- メインコンテンツ -->
        <ScrollViewer HorizontalAlignment="Center" VerticalAlignment="Center">
            <StackPanel MaxWidth="400" Margin="40" Spacing="30">
                
                <!-- ログインカード -->
                <Border Background="White" 
                        CornerRadius="15"
                        Padding="30">
                    <Border.Shadow>
                        <ThemeShadow />
                    </Border.Shadow>
                    
                    <StackPanel Spacing="25">
                        
                        <!-- ヘッダー -->
                        <StackPanel Spacing="10">
                            <TextBlock Text="🔐" 
                                       FontSize="48" 
                                       HorizontalAlignment="Center" />
                            <TextBlock Text="TranscriptCleaner" 
                                       FontSize="24" 
                                       FontWeight="Bold" 
                                       HorizontalAlignment="Center"
                                       Foreground="#4472c4" />
                            <TextBlock Text="Microsoft Teams 議事録自動訂正システム" 
                                       FontSize="14" 
                                       HorizontalAlignment="Center"
                                       Foreground="#6c757d" />
                        </StackPanel>

                        <!-- ユーザー名入力 -->
                        <StackPanel Spacing="8">
                            <TextBlock Text="ユーザー名" 
                                       FontSize="14" 
                                       Foreground="#495057"
                                       FontWeight="Bold" />
                            <TextBox x:Name="UsernameTextBox"
                                     PlaceholderText="ユーザー名を入力してください" />
                        </StackPanel>

                        <!-- パスワード入力 -->
                        <StackPanel Spacing="8">
                            <TextBlock Text="パスワード" 
                                       FontSize="14" 
                                       Foreground="#495057"
                                       FontWeight="Bold" />
                            <PasswordBox x:Name="PasswordBox"
                                         PlaceholderText="パスワードを入力してください" />
                        </StackPanel>

                        <!-- ログインボタン -->
                        <Button x:Name="LoginButton"
                                Content="ログイン"
                                Background="#4472c4" 
                                Foreground="White"
                                FontSize="16"
                                FontWeight="Bold"
                                Height="50"
                                CornerRadius="25"
                                Click="OnLoginClicked" />

                        <!-- デモユーザー情報 -->
                        <Border Background="#e3f2fd" 
                                BorderBrush="#bbdefb"
                                BorderThickness="1"
                                CornerRadius="8"
                                Padding="15">
                            <StackPanel Spacing="8">
                                <TextBlock Text="💡 デモアカウント" 
                                           FontSize="14" 
                                           FontWeight="Bold"
                                           Foreground="#1976d2" />
                                <TextBlock Text="ユーザー名: admin / パスワード: admin123" 
                                           FontSize="12" 
                                           Foreground="#1565c0" />
                                <TextBlock Text="ユーザー名: testuser / パスワード: test123" 
                                           FontSize="12" 
                                           Foreground="#1565c0" />
                            </StackPanel>
                        </Border>

                    </StackPanel>
                </Border>

                <!-- フッター -->
                <TextBlock Text="© 2024 TranscriptCleaner - AI駆動の議事録訂正ツール" 
                           FontSize="12" 
                           HorizontalAlignment="Center"
                           Foreground="White"
                           Opacity="0.8" />

            </StackPanel>
        </ScrollViewer>

    </Grid>

</Page>