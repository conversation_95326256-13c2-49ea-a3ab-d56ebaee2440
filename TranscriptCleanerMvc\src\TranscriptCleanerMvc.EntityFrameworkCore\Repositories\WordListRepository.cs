using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TranscriptCleanerMvc.Entities;
using TranscriptCleanerMvc.EntityFrameworkCore;
using TranscriptCleanerMvc.Repositories;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TranscriptCleanerMvc.EntityFrameworkCore.Repositories;

public class WordListRepository : EfCoreRepository<TranscriptCleanerMvcDbContext, WordList, Guid>, IWordListRepository
{
    public WordListRepository(IDbContextProvider<TranscriptCleanerMvcDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<WordList>> GetListAsync(
        int skipCount = 0,
        int maxResultCount = 10,
        string? sorting = null,
        string? filter = null,
        string? language = null,
        string? category = null,
        bool? isActive = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.AsQueryable();

        query = ApplyFilter(query, filter, language, category, isActive);

        if (!string.IsNullOrEmpty(sorting))
        {
            query = query.OrderBy(sorting);
        }
        else
        {
            query = query.OrderBy(x => x.Language).ThenBy(x => x.IncorrectWord);
        }

        return await query.Skip(skipCount).Take(maxResultCount).ToListAsync(cancellationToken);
    }

    public async Task<long> GetCountAsync(
        string? filter = null,
        string? language = null,
        string? category = null,
        bool? isActive = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.AsQueryable();

        query = ApplyFilter(query, filter, language, category, isActive);

        return await query.CountAsync(cancellationToken);
    }

    public async Task<List<WordList>> GetActiveByLanguageAsync(
        string language,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        
        return await dbSet
            .Where(x => x.Language == language && x.IsActive)
            .OrderBy(x => x.IncorrectWord)
            .ToListAsync(cancellationToken);
    }

    public async Task<WordList?> FindByIncorrectWordAsync(
        string incorrectWord,
        string language,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        
        return await dbSet
            .FirstOrDefaultAsync(x => x.IncorrectWord == incorrectWord && x.Language == language, 
                                cancellationToken);
    }

    public async Task<bool> IsIncorrectWordExistsAsync(
        string incorrectWord,
        string language,
        Guid? excludeId = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.Where(x => x.IncorrectWord == incorrectWord && x.Language == language);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId);
        }

        return await query.AnyAsync(cancellationToken);
    }

    public async Task<List<WordList>> GetMostUsedAsync(
        int count = 10,
        string? language = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.AsQueryable();

        if (!string.IsNullOrEmpty(language))
        {
            query = query.Where(x => x.Language == language);
        }

        return await query
            .Where(x => x.IsActive)
            .OrderByDescending(x => x.UsageCount)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<string>> GetCategoriesAsync(
        string? language = null,
        CancellationToken cancellationToken = default)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.AsQueryable();

        if (!string.IsNullOrEmpty(language))
        {
            query = query.Where(x => x.Language == language);
        }

        return await query
            .Where(x => !string.IsNullOrEmpty(x.Category))
            .Select(x => x.Category!)
            .Distinct()
            .OrderBy(x => x)
            .ToListAsync(cancellationToken);
    }

    private IQueryable<WordList> ApplyFilter(
        IQueryable<WordList> query,
        string? filter = null,
        string? language = null,
        string? category = null,
        bool? isActive = null)
    {
        if (!string.IsNullOrEmpty(filter))
        {
            query = query.Where(x => x.IncorrectWord.Contains(filter) || 
                                    x.CorrectWord.Contains(filter) ||
                                    (x.Description != null && x.Description.Contains(filter)));
        }

        if (!string.IsNullOrEmpty(language))
        {
            query = query.Where(x => x.Language == language);
        }

        if (!string.IsNullOrEmpty(category))
        {
            query = query.Where(x => x.Category == category);
        }

        if (isActive.HasValue)
        {
            query = query.Where(x => x.IsActive == isActive);
        }

        return query;
    }
}