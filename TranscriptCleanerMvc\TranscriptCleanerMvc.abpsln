{
  "id": "d66347c7-582a-4344-889f-5d96ddc20f39",
  "template": "app",
  "versions": {
    "AbpFramework": "9.2.0",
    "AbpStudio": "1.0.1",
    "TargetDotnetFramework": "net9.0"
  },
  "modules": {
    "TranscriptCleanerMvc": {
      "path": "TranscriptCleanerMvc.abpmdl"
    }
  },
  "runProfiles": {
    "Default": {
      "path": "etc/abp-studio/run-profiles/Default.abprun.json"
    }
  },
  "options": {
    "httpRequests": {
      "ignoredUrls": [
      
      ]
    }
  },
  "creatingStudioConfiguration": {
    "template": "app",
    "createdAbpStudioVersion": "1.0.1",
    "tiered": "false",
    "runInstallLibs": "true",
    "useLocalReferences": "false",
    "multiTenancy": "true",
    "includeTests": "true",
    "kubernetesConfiguration": "false",
    "uiFramework": "mvc",
    "mobileFramework": "none",
    "distributedEventBus": "none",
    "databaseProvider": "ef",
    "runDbMigrator": "true",
    "databaseManagementSystem": "sqlserver",
    "separateTenantSchema": "false",
    "createInitialMigration": "true",
    "theme": "leptonx-lite",
    "themeStyle": "",
    "themeMenuPlacement": "",
    "mobileFramework": "none",
    "publicWebsite": "false",
    "socialLogin": "true",
    "selectedLanguages": ["English", "English (United Kingdom)", "简体中文", "Español", "العربية", "हिन्दी", "Português (Brasil)", "Français", "Русский", "Deutsch (Deuthschland)", "Türkçe", "Italiano", "Čeština", "Magyar", "Română (România)", "Svenska", "Suomi", "Slovenčina", "Íslenska", "繁體中z文", ],
    "defaultLanguage": "English",
    "createCommand": "abp new TranscriptCleanerMvc -t app --ui-framework mvc --database-provider ef --database-management-system sqlserver --theme leptonx-lite --without-cms-kit --dont-run-bundling -no-file-management"
  }
}