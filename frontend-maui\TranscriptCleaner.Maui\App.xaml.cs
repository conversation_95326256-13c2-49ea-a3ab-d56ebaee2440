﻿using TranscriptCleaner.Maui.Services;

namespace TranscriptCleaner.Maui;

public partial class App : Application
{
    public App()
    {
        InitializeComponent();
    }

    protected override Window CreateWindow(IActivationState? activationState)
    {
        return new Window(new AppShell());
    }

    protected override async void OnStart()
    {
        base.OnStart();
        await InitializeAuthenticationAsync();
    }

    private async Task InitializeAuthenticationAsync()
    {
        try
        {
            var serviceProvider = Handler?.MauiContext?.Services;
            if (serviceProvider == null) return;

            var googleAuthService = serviceProvider.GetService<IGoogleAuthService>();
            var offlineAuthService = serviceProvider.GetService<IOfflineAuthService>();

            if (googleAuthService == null || offlineAuthService == null) return;

            // 簡素化：直接ログイン画面へ
            await Shell.Current.GoToAsync("//login");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"認証初期化エラー: {ex.Message}");
            await Shell.Current.GoToAsync("//login");
        }
    }
}