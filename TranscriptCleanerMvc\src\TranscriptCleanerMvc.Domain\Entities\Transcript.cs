using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using TranscriptCleanerMvc.Transcripts;
using Volo.Abp.Domain.Entities.Auditing;

namespace TranscriptCleanerMvc.Entities;

public class Transcript : FullAuditedEntity<Guid>
{
    [Required]
    [MaxLength(500)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    public string OriginalText { get; set; } = string.Empty;
    
    public string? CorrectedText { get; set; }
    
    [MaxLength(10)]
    public string Language { get; set; } = "ja";
    
    [MaxLength(50)]
    public string CorrectionType { get; set; } = "comprehensive";
    
    public TranscriptStatus Status { get; set; } = TranscriptStatus.Pending;
    
    public int OriginalLength { get; set; }
    
    public int CorrectedLength { get; set; }
    
    public int ProcessingTimeMs { get; set; }
    
    public decimal? ProcessingCost { get; set; }
    
    [MaxLength(1000)]
    public string? ErrorMessage { get; set; }
    
    public DateTime? ProcessedAt { get; set; }
    
    public virtual ICollection<CorrectionHistory> CorrectionHistories { get; set; } = new List<CorrectionHistory>();
    
    protected Transcript()
    {
    }
    
    public Transcript(
        Guid id,
        string title,
        string originalText,
        string language = "ja",
        string correctionType = "comprehensive"
    ) : base(id)
    {
        Title = title;
        OriginalText = originalText;
        Language = language;
        CorrectionType = correctionType;
        OriginalLength = originalText.Length;
        Status = TranscriptStatus.Pending;
    }
    
    public void SetCorrectedText(string correctedText, int processingTimeMs, decimal? cost = null)
    {
        CorrectedText = correctedText;
        CorrectedLength = correctedText.Length;
        ProcessingTimeMs = processingTimeMs;
        ProcessingCost = cost;
        ProcessedAt = DateTime.UtcNow;
        Status = TranscriptStatus.Completed;
        ErrorMessage = null;
    }
    
    public void SetError(string errorMessage)
    {
        ErrorMessage = errorMessage;
        Status = TranscriptStatus.Failed;
        ProcessedAt = DateTime.UtcNow;
    }
}