using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.UI.Xaml;
using NSubstitute;
using Shouldly;
using Xunit;
using TranscriptCleaner.WinUI.Services;
using Windows.Security.Credentials;

namespace TranscriptCleaner.WinUI.Tests.E2E
{
    /// <summary>
    /// WinUI Google認証のE2Eテスト
    /// </summary>
    public class GoogleAuthE2ETests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly IGoogleAuthService _googleAuthService;
        private readonly IAuthStateManager _authStateManager;
        private readonly IJwtTokenService _jwtTokenService;
        private readonly IAuthSyncService _authSyncService;

        public GoogleAuthE2ETests()
        {
            var services = new ServiceCollection();
            
            // テスト用のサービス設定
            _googleAuthService = Substitute.For<IGoogleAuthService>();
            _authStateManager = Substitute.For<IAuthStateManager>();
            _jwtTokenService = Substitute.For<IJwtTokenService>();
            _authSyncService = Substitute.For<IAuthSyncService>();

            services.AddSingleton(_googleAuthService);
            services.AddSingleton(_authStateManager);
            services.AddSingleton(_jwtTokenService);
            services.AddSingleton(_authSyncService);

            _serviceProvider = services.BuildServiceProvider();
        }

        [Fact]
        public async Task GoogleLogin_WithWebView2_ShouldSucceed()
        {
            // Arrange
            _googleAuthService.SignInWithWebView2Async().Returns(new GoogleAuthResult
            {
                IsSuccess = true,
                AccessToken = "webview2_token",
                IdToken = "webview2_id_token",
                UserInfo = new GoogleUserInfo
                {
                    Id = "google_user_456",
                    Email = "<EMAIL>",
                    Name = "WinUI User"
                }
            });

            _authSyncService.SyncWithServerAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(new AuthSyncResult { IsSuccess = true });

            // Act
            var result = await _googleAuthService.SignInWithWebView2Async();

            // Assert
            result.IsSuccess.ShouldBeTrue();
            result.UserInfo.Email.ShouldBe("<EMAIL>");
            
            await _googleAuthService.Received(1).SignInWithWebView2Async();
        }

        [Fact]
        public async Task WindowsCredentialManager_ShouldStoreTokensSecurely()
        {
            // Arrange
            var testToken = "secure_test_token";
            var testRefreshToken = "secure_refresh_token";

            _googleAuthService.StoreTokenInCredentialManagerAsync(testToken, testRefreshToken)
                .Returns(Task.CompletedTask);
            
            _googleAuthService.GetTokenFromCredentialManagerAsync()
                .Returns((testToken, testRefreshToken));

            // Act
            await _googleAuthService.StoreTokenInCredentialManagerAsync(testToken, testRefreshToken);
            var (storedToken, storedRefreshToken) = await _googleAuthService.GetTokenFromCredentialManagerAsync();

            // Assert
            storedToken.ShouldBe(testToken);
            storedRefreshToken.ShouldBe(testRefreshToken);
            
            await _googleAuthService.Received(1).StoreTokenInCredentialManagerAsync(testToken, testRefreshToken);
            await _googleAuthService.Received(1).GetTokenFromCredentialManagerAsync();
        }

        [Fact]
        public async Task AppMinimizeRestore_ShouldMaintainAuthState()
        {
            // Arrange
            _authStateManager.SaveAuthStateAsync(Arg.Any<AuthState>()).Returns(Task.CompletedTask);
            _authStateManager.RestoreAuthStateAsync().Returns(new AuthState
            {
                IsAuthenticated = true,
                UserName = "Test User",
                AccessToken = "restored_token",
                LastActivity = DateTime.UtcNow.AddMinutes(-5)
            });

            var authState = new AuthState
            {
                IsAuthenticated = true,
                UserName = "Test User",
                AccessToken = "test_token",
                LastActivity = DateTime.UtcNow
            };

            // Act - アプリ最小化をシミュレート
            await _authStateManager.SaveAuthStateAsync(authState);
            
            // アプリ復元をシミュレート
            var restoredState = await _authStateManager.RestoreAuthStateAsync();

            // Assert
            restoredState.IsAuthenticated.ShouldBeTrue();
            restoredState.UserName.ShouldBe("Test User");
            
            await _authStateManager.Received(1).SaveAuthStateAsync(Arg.Any<AuthState>());
            await _authStateManager.Received(1).RestoreAuthStateAsync();
        }

        [Fact]
        public async Task SessionTimeout_ShouldPromptReauthentication()
        {
            // Arrange
            var expiredState = new AuthState
            {
                IsAuthenticated = true,
                UserName = "Test User",
                AccessToken = "expired_token",
                LastActivity = DateTime.UtcNow.AddHours(-2) // 2時間前
            };

            _authStateManager.RestoreAuthStateAsync().Returns(expiredState);
            _authStateManager.IsSessionExpiredAsync(expiredState).Returns(true);
            _googleAuthService.RefreshTokenAsync("expired_token").Returns("new_token");

            // Act
            var state = await _authStateManager.RestoreAuthStateAsync();
            var isExpired = await _authStateManager.IsSessionExpiredAsync(state);
            
            if (isExpired)
            {
                var newToken = await _googleAuthService.RefreshTokenAsync(state.AccessToken);
                state.AccessToken = newToken;
            }

            // Assert
            isExpired.ShouldBeTrue();
            state.AccessToken.ShouldBe("new_token");
            
            await _googleAuthService.Received(1).RefreshTokenAsync("expired_token");
        }

        [Fact]
        public async Task MultipleWindows_ShouldSyncAuthState()
        {
            // Arrange
            var authState = new AuthState
            {
                IsAuthenticated = true,
                UserName = "Multi Window User",
                AccessToken = "multi_window_token"
            };

            _authStateManager.BroadcastAuthStateChangeAsync(authState).Returns(Task.CompletedTask);
            _authStateManager.OnAuthStateChanged += Arg.Any<EventHandler<AuthStateChangedEventArgs>>();

            // Act
            await _authStateManager.BroadcastAuthStateChangeAsync(authState);

            // Assert
            await _authStateManager.Received(1).BroadcastAuthStateChangeAsync(authState);
        }

        [Fact]
        public async Task SystemShutdown_ShouldCleanupResources()
        {
            // Arrange
            _googleAuthService.CleanupResourcesAsync().Returns(Task.CompletedTask);
            _authStateManager.SaveAuthStateAsync(Arg.Any<AuthState>()).Returns(Task.CompletedTask);

            // Act - システムシャットダウンをシミュレート
            await _authStateManager.SaveAuthStateAsync(new AuthState { IsAuthenticated = false });
            await _googleAuthService.CleanupResourcesAsync();

            // Assert
            await _authStateManager.Received(1).SaveAuthStateAsync(Arg.Any<AuthState>());
            await _googleAuthService.Received(1).CleanupResourcesAsync();
        }

        [Fact]
        public async Task WindowsHello_WhenAvailable_ShouldEnableBiometricAuth()
        {
            // Arrange
            _googleAuthService.IsWindowsHelloAvailableAsync().Returns(true);
            _googleAuthService.AuthenticateWithWindowsHelloAsync().Returns(new GoogleAuthResult
            {
                IsSuccess = true,
                AccessToken = "windows_hello_token"
            });

            // Act
            var isAvailable = await _googleAuthService.IsWindowsHelloAvailableAsync();
            GoogleAuthResult result = null;
            
            if (isAvailable)
            {
                result = await _googleAuthService.AuthenticateWithWindowsHelloAsync();
            }

            // Assert
            isAvailable.ShouldBeTrue();
            result.ShouldNotBeNull();
            result.IsSuccess.ShouldBeTrue();
            
            await _googleAuthService.Received(1).AuthenticateWithWindowsHelloAsync();
        }

        [Fact]
        public async Task NetworkConnectivity_ShouldHandleOfflineMode()
        {
            // Arrange
            _googleAuthService.IsNetworkAvailableAsync().Returns(false);
            _googleAuthService.GetCachedTokenAsync().Returns("cached_offline_token");
            _jwtTokenService.ValidateTokenAsync("cached_offline_token").Returns(true);

            // Act
            var isNetworkAvailable = await _googleAuthService.IsNetworkAvailableAsync();
            string token = null;
            bool isValid = false;
            
            if (!isNetworkAvailable)
            {
                token = await _googleAuthService.GetCachedTokenAsync();
                isValid = await _jwtTokenService.ValidateTokenAsync(token);
            }

            // Assert
            isNetworkAvailable.ShouldBeFalse();
            token.ShouldBe("cached_offline_token");
            isValid.ShouldBeTrue();
            
            await _googleAuthService.Received(1).GetCachedTokenAsync();
            await _jwtTokenService.Received(1).ValidateTokenAsync("cached_offline_token");
        }

        [Fact]
        public async Task ErrorHandling_ShouldProvideUserFriendlyMessages()
        {
            // Arrange
            _googleAuthService.SignInWithWebView2Async().Returns(new GoogleAuthResult
            {
                IsSuccess = false,
                ErrorCode = "NETWORK_ERROR",
                ErrorMessage = "ネットワークに接続できません。インターネット接続を確認してください。"
            });

            // Act
            var result = await _googleAuthService.SignInWithWebView2Async();

            // Assert
            result.IsSuccess.ShouldBeFalse();
            result.ErrorCode.ShouldBe("NETWORK_ERROR");
            result.ErrorMessage.ShouldContain("ネットワーク");
            result.ErrorMessage.ShouldContain("インターネット接続");
        }

        [Fact]
        public async Task TokenRefresh_ShouldHandleRefreshTokenExpiration()
        {
            // Arrange
            _jwtTokenService.IsTokenExpiredAsync("expired_access_token").Returns(true);
            _googleAuthService.RefreshTokenAsync("expired_refresh_token").Returns(
                Task.FromException<string>(new UnauthorizedAccessException("Refresh token expired"))
            );
            
            _googleAuthService.SignInWithWebView2Async().Returns(new GoogleAuthResult
            {
                IsSuccess = true,
                AccessToken = "new_access_token",
                RefreshToken = "new_refresh_token"
            });

            // Act & Assert
            var isExpired = await _jwtTokenService.IsTokenExpiredAsync("expired_access_token");
            isExpired.ShouldBeTrue();

            // リフレッシュトークンも期限切れの場合、再認証が必要
            await Should.ThrowAsync<UnauthorizedAccessException>(async () =>
                await _googleAuthService.RefreshTokenAsync("expired_refresh_token"));

            // 再認証を実行
            var newAuthResult = await _googleAuthService.SignInWithWebView2Async();
            newAuthResult.IsSuccess.ShouldBeTrue();
            newAuthResult.AccessToken.ShouldBe("new_access_token");
        }

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }

    // テスト用のデータ構造
    public class AuthState
    {
        public bool IsAuthenticated { get; set; }
        public string? UserName { get; set; }
        public string? AccessToken { get; set; }
        public DateTime LastActivity { get; set; }
    }



    public class GoogleUserInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class GoogleAuthResult
    {
        public bool IsSuccess { get; set; }
        public string? AccessToken { get; set; }
        public string? IdToken { get; set; }
        public string? RefreshToken { get; set; }
        public GoogleUserInfo? UserInfo { get; set; }
        public string? ErrorCode { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class AuthSyncResult
    {
        public bool IsSuccess { get; set; }
        public string? ErrorMessage { get; set; }
    }
}