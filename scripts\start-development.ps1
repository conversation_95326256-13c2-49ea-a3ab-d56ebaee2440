# TranscriptCleaner Development Environment Startup Script

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("maui", "winui", "both")]
    [string]$Frontend = "both"
)

# Color output function
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

Write-ColorOutput Green "=== TranscriptCleaner Development Environment Startup ==="
Write-Host ""

# Project root directory
$ProjectRoot = Split-Path $PSScriptRoot -Parent

# Switch to development environment
Write-ColorOutput Cyan "Switching to development environment..."
& "$PSScriptRoot\switch-environment.ps1" development

# Check secrets file
$SecretsPath = Join-Path $ProjectRoot "TranscriptCleanerMvc\src\TranscriptCleanerMvc.Web\appsettings.secrets.json"
if (-not (Test-Path $SecretsPath)) {
    Write-ColorOutput Yellow "OpenAI API key not configured"
    $setupSecrets = Read-Host "Would you like to set up API key? (y/N)"
    if ($setupSecrets -eq "y" -or $setupSecrets -eq "Y") {
        & "$PSScriptRoot\setup-secrets.ps1"
    }
    Write-Host ""
}

Write-ColorOutput Cyan "Starting backend..."
Write-Host ""

# Start backend
Start-Process powershell -ArgumentList @(
    "-NoExit",
    "-Command",
    "cd '$ProjectRoot\TranscriptCleanerMvc\src\TranscriptCleanerMvc.Web'; dotnet run"
) -WindowStyle Normal

Write-ColorOutput Green "Backend started (https://localhost:44396)"
Write-Host ""

# Frontend startup selection
switch ($Frontend) {
    "maui" {
        Write-ColorOutput Cyan "Starting MAUI app..."
        Start-Process powershell -ArgumentList @(
            "-NoExit",
            "-Command",
            "cd '$ProjectRoot\frontend-maui\TranscriptCleaner.Maui'; dotnet run"
        ) -WindowStyle Normal
        Write-ColorOutput Green "MAUI app started"
    }
    "winui" {
        Write-ColorOutput Cyan "Starting WinUI app..."
        Start-Process powershell -ArgumentList @(
            "-NoExit",
            "-Command",
            "cd '$ProjectRoot\frontend-winui\TranscriptCleaner.WinUI'; dotnet run"
        ) -WindowStyle Normal
        Write-ColorOutput Green "WinUI app started"
    }
    "both" {
        Write-ColorOutput Cyan "Starting both frontend apps..."
        Start-Process powershell -ArgumentList @(
            "-NoExit",
            "-Command",
            "cd '$ProjectRoot\frontend-maui\TranscriptCleaner.Maui'; dotnet run"
        ) -WindowStyle Normal
        
        Start-Sleep -Seconds 2
        
        Start-Process powershell -ArgumentList @(
            "-NoExit",
            "-Command",
            "cd '$ProjectRoot\frontend-winui\TranscriptCleaner.WinUI'; dotnet run"
        ) -WindowStyle Normal
        Write-ColorOutput Green "Both apps started"
    }
}

Write-Host ""
Write-ColorOutput Green "Development environment startup completed!"
Write-Host ""
Write-ColorOutput Cyan "Available services:"
Write-ColorOutput Gray "  • Backend API: https://localhost:44396"
Write-ColorOutput Gray "  • MAUI App: Cross-platform support"
Write-ColorOutput Gray "  • WinUI App: Windows-optimized version"
Write-Host ""
Write-ColorOutput Yellow "Tips:"
Write-ColorOutput Gray "  • Demo accounts: admin/admin123, testuser/test123"
Write-ColorOutput Gray "  • Press Ctrl+C in each window to stop services"
Write-ColorOutput Gray "  • Check logs in respective PowerShell windows"