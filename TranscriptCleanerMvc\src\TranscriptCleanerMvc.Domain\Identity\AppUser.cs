using System;
using Volo.Abp.Identity;

namespace TranscriptCleanerMvc.Identity
{
    /// <summary>
    /// アプリケーション固有のユーザーエンティティ
    /// ソーシャルアカウント連携のためのプロパティを追加
    /// </summary>
    public class AppUser : IdentityUser
    {
        #region Social account properties

        /// <summary>
        /// Google ID
        /// </summary>
        public virtual string? GoogleId { get; set; }

        /// <summary>
        /// Google メールアドレス
        /// </summary>
        public virtual string? GoogleEmail { get; set; }

        /// <summary>
        /// プロフィール画像URL
        /// </summary>
        public virtual string? ProfileImageUrl { get; set; }

        /// <summary>
        /// Googleアカウント連携済みフラグ
        /// </summary>
        public virtual bool IsGoogleLinked { get; set; }

        /// <summary>
        /// 最後のGoogle同期日時
        /// </summary>
        public virtual DateTime? LastGoogleSync { get; set; }

        #endregion

        protected AppUser()
        {
        }

        public AppUser(Guid id, string userName, string email, Guid? tenantId = null)
            : base(id, userName, email, tenantId)
        {
            IsGoogleLinked = false;
        }

        /// <summary>
        /// Googleアカウントを連携する
        /// </summary>
        /// <param name="googleId">Google ID</param>
        /// <param name="googleEmail">Googleメールアドレス</param>
        /// <param name="profileImageUrl">プロフィール画像URL</param>
        public void LinkGoogleAccount(string googleId, string googleEmail, string profileImageUrl = null)
        {
            GoogleId = googleId;
            GoogleEmail = googleEmail;
            
            if (!string.IsNullOrEmpty(profileImageUrl))
            {
                ProfileImageUrl = profileImageUrl;
            }
            
            IsGoogleLinked = true;
            LastGoogleSync = DateTime.UtcNow;
        }

        /// <summary>
        /// Googleアカウント連携を解除する
        /// </summary>
        public void UnlinkGoogleAccount()
        {
            GoogleId = null;
            GoogleEmail = null;
            IsGoogleLinked = false;
        }

        /// <summary>
        /// Googleプロフィール情報を更新する
        /// </summary>
        /// <param name="name">名前</param>
        /// <param name="surname">姓</param>
        /// <param name="profileImageUrl">プロフィール画像URL</param>
        public void UpdateGoogleProfile(string name, string surname, string profileImageUrl)
        {
            if (!string.IsNullOrEmpty(name))
            {
                Name = name;
            }
            
            if (!string.IsNullOrEmpty(surname))
            {
                Surname = surname;
            }
            
            if (!string.IsNullOrEmpty(profileImageUrl))
            {
                ProfileImageUrl = profileImageUrl;
            }
            
            LastGoogleSync = DateTime.UtcNow;
        }
    }
}