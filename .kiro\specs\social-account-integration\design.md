# 設計書

## 概要

TranscriptCleanerMvcアプリケーションにGoogle認証機能を統合し、既存のABP FrameworkのIdentity機能を拡張する。Web、MAUI、WinUIクライアントすべてでGoogle認証をサポートし、一貫した認証体験を提供する。

**技術スタック:**
- .NET 9.0 (最新安定版)
- ABP Framework 9.x (最新安定版)
- ASP.NET Core Identity
- OpenIddict (OAuth 2.0/OpenID Connect)
- Entity Framework Core 9.0

## アーキテクチャ

### 全体アーキテクチャ

```mermaid
graph TB
    subgraph "クライアントアプリケーション"
        WEB[Web Browser]
        MAUI[MAUI App]
        WINUI[WinUI App]
    end
    
    subgraph "ABP Framework (Port 44396)"
        AUTH[Authentication Server]
        API[Web API]
        IDENTITY[Identity Module]
    end
    
    subgraph "外部サービス"
        GOOGLE[Google OAuth 2.0]
    end
    
    subgraph "データストレージ"
        DB[(SQL Server)]
    end
    
    WEB --> AUTH
    MAUI --> AUTH
    WINUI --> AUTH
    
    AUTH --> GOOGLE
    AUTH --> IDENTITY
    IDENTITY --> DB
    
    WEB --> API
    MAUI --> API
    WINUI --> API
```

### 認証フロー

```mermaid
sequenceDiagram
    participant Client as クライアント
    participant ABP as ABP Auth Server
    participant Google as Google OAuth
    participant DB as データベース
    
    Client->>ABP: Google認証リクエスト
    ABP->>Google: OAuth認証リダイレクト
    Google->>Client: 認証画面表示
    Client->>Google: 認証情報入力
    Google->>ABP: 認証コード返却
    ABP->>Google: アクセストークン取得
    Google->>ABP: ユーザー情報返却
    ABP->>DB: ユーザー情報確認/作成
    ABP->>Client: JWT トークン発行
```

## コンポーネントと インターフェース

### 1. サーバーサイドコンポーネント

#### 1.1 Google認証設定 (TranscriptCleanerMvcWebModule)

**責任:**
- Google OAuth 2.0プロバイダーの設定
- 認証ミドルウェアの構成
- リダイレクトURI設定

**主要メソッド:**
```csharp
private void ConfigureGoogleAuthentication(ServiceConfigurationContext context)
private void ConfigureExternalProviders(ServiceConfigurationContext context)
```

#### 1.2 外部ログインサービス (ExternalLoginService)

**責任:**
- Google認証フローの管理
- ユーザー情報の取得と同期
- アカウント連携処理

**インターフェース:**
```csharp
public interface IExternalLoginService
{
    Task<ExternalLoginResult> HandleGoogleCallbackAsync(string code, string state);
    Task<bool> LinkGoogleAccountAsync(Guid userId, string googleId);
    Task<bool> UnlinkGoogleAccountAsync(Guid userId);
    Task<UserProfileDto> SyncGoogleProfileAsync(Guid userId);
}
```

#### 1.3 ユーザー拡張エンティティ (AppUser)

**責任:**
- Google認証情報の保存
- プロフィール画像URL管理

**追加プロパティ:**
```csharp
public class AppUser : FullAuditedAggregateRoot<Guid>, IUser
{
    public string GoogleId { get; set; }
    public string GoogleEmail { get; set; }
    public string ProfileImageUrl { get; set; }
    public bool IsGoogleLinked { get; set; }
    public DateTime? LastGoogleSync { get; set; }
}
```

#### 1.4 外部認証コントローラー (ExternalAuthController)

**責任:**
- Google認証エンドポイントの提供
- 認証コールバック処理
- アカウント連携API

**エンドポイント:**
```csharp
[Route("api/auth/external")]
public class ExternalAuthController : AbpController
{
    [HttpGet("google/login")]
    public async Task<IActionResult> GoogleLogin(string returnUrl = null)
    
    [HttpGet("google/callback")]
    public async Task<IActionResult> GoogleCallback(string code, string state)
    
    [HttpPost("google/link")]
    public async Task<IActionResult> LinkGoogleAccount()
    
    [HttpDelete("google/unlink")]
    public async Task<IActionResult> UnlinkGoogleAccount()
}
```

### 2. クライアントサイドコンポーネント

#### 2.1 Web クライアント

**ログインページ拡張:**
- Googleログインボタンの追加
- 認証状態の管理
- エラーハンドリング

**ファイル:**
- `Pages/Account/Login.cshtml` - ログインページ
- `Pages/Account/Manage/ExternalLogins.cshtml` - アカウント連携管理

#### 2.2 MAUI クライアント (.NET 9.0対応)

**認証サービス:**
```csharp
public interface IGoogleAuthService
{
    Task<AuthResult> LoginWithGoogleAsync();
    Task<bool> IsGoogleLinkedAsync();
    Task<bool> LinkGoogleAccountAsync();
    Task LogoutAsync();
}
```

**実装クラス (.NET 9.0 + MAUI最新版):**
```csharp
public class GoogleAuthService : IGoogleAuthService
{
    private readonly HttpClient _httpClient;
    private readonly ISecureStorage _secureStorage;
    
    // .NET 9.0の新機能を活用した実装
    // - System.Text.Json の最新機能
    // - 新しい HttpClient 機能
    // - MAUI の最新認証API
}
```

#### 2.3 WinUI クライアント (.NET 9.0対応)

**認証サービス:**
```csharp
public class WinUIGoogleAuthService : IGoogleAuthService
{
    private readonly WebView2 _webView;
    private readonly HttpClient _httpClient;
    
    // .NET 9.0 + WinUI 3最新版での実装
    // - WebView2の最新機能活用
    // - Windows App SDKの最新機能
    // - .NET 9.0のパフォーマンス改善を活用
}
```

## データモデル

### 1. データベーススキーマ拡張

#### 1.1 AbpUsers テーブル拡張

```sql
ALTER TABLE AbpUsers ADD COLUMN GoogleId NVARCHAR(256) NULL;
ALTER TABLE AbpUsers ADD COLUMN GoogleEmail NVARCHAR(256) NULL;
ALTER TABLE AbpUsers ADD COLUMN ProfileImageUrl NVARCHAR(512) NULL;
ALTER TABLE AbpUsers ADD COLUMN IsGoogleLinked BIT NOT NULL DEFAULT 0;
ALTER TABLE AbpUsers ADD COLUMN LastGoogleSync DATETIME2 NULL;

CREATE INDEX IX_AbpUsers_GoogleId ON AbpUsers(GoogleId);
CREATE INDEX IX_AbpUsers_GoogleEmail ON AbpUsers(GoogleEmail);
```

#### 1.2 外部ログイン履歴テーブル

```sql
CREATE TABLE ExternalLoginLogs (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserId UNIQUEIDENTIFIER NOT NULL,
    Provider NVARCHAR(50) NOT NULL,
    ProviderKey NVARCHAR(256) NOT NULL,
    LoginTime DATETIME2 NOT NULL,
    IpAddress NVARCHAR(45) NULL,
    UserAgent NVARCHAR(512) NULL,
    Success BIT NOT NULL,
    ErrorMessage NVARCHAR(1024) NULL,
    
    FOREIGN KEY (UserId) REFERENCES AbpUsers(Id)
);

CREATE INDEX IX_ExternalLoginLogs_UserId ON ExternalLoginLogs(UserId);
CREATE INDEX IX_ExternalLoginLogs_Provider ON ExternalLoginLogs(Provider);
CREATE INDEX IX_ExternalLoginLogs_LoginTime ON ExternalLoginLogs(LoginTime);
```

### 2. DTOクラス

#### 2.1 外部認証関連DTO

```csharp
public class ExternalLoginResult
{
    public bool Success { get; set; }
    public string AccessToken { get; set; }
    public string RefreshToken { get; set; }
    public DateTime ExpiresAt { get; set; }
    public UserProfileDto User { get; set; }
    public string ErrorMessage { get; set; }
}

public class GoogleUserInfo
{
    public string Id { get; set; }
    public string Email { get; set; }
    public string Name { get; set; }
    public string GivenName { get; set; }
    public string FamilyName { get; set; }
    public string Picture { get; set; }
    public bool EmailVerified { get; set; }
}

public class ExternalAccountLinkDto
{
    public string Provider { get; set; }
    public string ProviderKey { get; set; }
    public string ProviderDisplayName { get; set; }
    public bool IsLinked { get; set; }
    public DateTime? LinkedAt { get; set; }
}
```

## エラーハンドリング

### 1. エラー分類

#### 1.1 認証エラー

```csharp
public static class GoogleAuthErrorCodes
{
    public const string InvalidAuthorizationCode = "GOOGLE_INVALID_AUTH_CODE";
    public const string TokenExchangeFailed = "GOOGLE_TOKEN_EXCHANGE_FAILED";
    public const string UserInfoRetrievalFailed = "GOOGLE_USER_INFO_FAILED";
    public const string AccountAlreadyLinked = "GOOGLE_ACCOUNT_ALREADY_LINKED";
    public const string EmailAlreadyExists = "GOOGLE_EMAIL_ALREADY_EXISTS";
    public const string ConfigurationError = "GOOGLE_CONFIG_ERROR";
}
```

#### 1.2 エラーハンドリング戦略

```csharp
public class GoogleAuthExceptionHandler : IExceptionHandler
{
    public async Task<bool> TryHandleAsync(
        HttpContext httpContext,
        Exception exception,
        CancellationToken cancellationToken)
    {
        switch (exception)
        {
            case GoogleAuthException googleEx:
                await HandleGoogleAuthException(httpContext, googleEx);
                return true;
            case HttpRequestException httpEx:
                await HandleHttpException(httpContext, httpEx);
                return true;
            default:
                return false;
        }
    }
}
```

### 2. ログ記録

```csharp
public class GoogleAuthLogger
{
    private readonly ILogger<GoogleAuthLogger> _logger;
    
    public void LogAuthenticationAttempt(string userId, string ipAddress)
    {
        _logger.LogInformation("Google authentication attempt for user {UserId} from {IpAddress}", 
            userId, ipAddress);
    }
    
    public void LogAuthenticationSuccess(string userId, string googleId)
    {
        _logger.LogInformation("Google authentication successful for user {UserId}, Google ID: {GoogleId}", 
            userId, googleId);
    }
    
    public void LogAuthenticationFailure(string error, string details)
    {
        _logger.LogWarning("Google authentication failed: {Error}. Details: {Details}", 
            error, details);
    }
}
```

## テスト戦略

### 1. 単体テスト

#### 1.1 サービステスト

```csharp
public class ExternalLoginServiceTests : TranscriptCleanerMvcTestBase<TranscriptCleanerMvcApplicationTestModule>
{
    private readonly IExternalLoginService _externalLoginService;
    
    [Fact]
    public async Task HandleGoogleCallbackAsync_ValidCode_ShouldReturnSuccess()
    {
        // Arrange
        var code = "valid_auth_code";
        var state = "test_state";
        
        // Act
        var result = await _externalLoginService.HandleGoogleCallbackAsync(code, state);
        
        // Assert
        result.Success.ShouldBeTrue();
        result.AccessToken.ShouldNotBeNullOrEmpty();
    }
}
```

#### 1.2 コントローラーテスト

```csharp
public class ExternalAuthControllerTests : TranscriptCleanerMvcWebTestBase
{
    [Fact]
    public async Task GoogleLogin_ShouldRedirectToGoogle()
    {
        // Arrange & Act
        var response = await GetResponseAsObjectAsync<RedirectResult>(
            "/api/auth/external/google/login"
        );
        
        // Assert
        response.Url.ShouldContain("accounts.google.com");
    }
}
```

### 2. 統合テスト

#### 2.1 認証フローテスト

```csharp
public class GoogleAuthIntegrationTests : TranscriptCleanerMvcWebTestBase
{
    [Fact]
    public async Task CompleteGoogleAuthFlow_ShouldCreateUserAndReturnToken()
    {
        // 完全な認証フローのテスト
        // モックされたGoogle APIレスポンスを使用
    }
}
```

### 3. E2Eテスト

#### 3.1 クライアントアプリテスト

```csharp
public class MAUIGoogleAuthE2ETests
{
    [Fact]
    public async Task GoogleLogin_FromMAUIApp_ShouldSucceed()
    {
        // MAUIアプリからのGoogle認証のE2Eテスト
    }
}
```

## セキュリティ考慮事項

### 1. OAuth 2.0セキュリティ

- **PKCE (Proof Key for Code Exchange)**: モバイルアプリでの認証セキュリティ強化
- **State パラメータ**: CSRF攻撃防止
- **Nonce**: リプレイ攻撃防止
- **HTTPS強制**: すべての認証通信でHTTPS必須

### 2. トークン管理

```csharp
public class SecureTokenStorage
{
    public async Task StoreTokenAsync(string key, string token, TimeSpan expiry)
    {
        // プラットフォーム固有のセキュアストレージ実装
        // Windows: DPAPI
        // Mobile: Keychain/Keystore
    }
}
```

### 3. データ保護

- **個人情報の最小化**: 必要最小限のユーザー情報のみ取得
- **データ暗号化**: 機密情報のデータベース暗号化
- **監査ログ**: すべての認証イベントのログ記録

## パフォーマンス最適化

### 1. キャッシュ戦略

```csharp
public class GoogleUserInfoCache
{
    private readonly IDistributedCache _cache;
    
    public async Task<GoogleUserInfo> GetCachedUserInfoAsync(string googleId)
    {
        var cacheKey = $"google_user_{googleId}";
        var cached = await _cache.GetStringAsync(cacheKey);
        
        if (cached != null)
        {
            return JsonSerializer.Deserialize<GoogleUserInfo>(cached);
        }
        
        return null;
    }
}
```

### 2. 非同期処理

- すべての外部API呼び出しを非同期で実装
- バックグラウンドでのプロフィール同期
- 並列処理によるレスポンス時間短縮

### 3. 接続プール

```csharp
services.AddHttpClient<IGoogleApiClient, GoogleApiClient>(client =>
{
    client.BaseAddress = new Uri("https://www.googleapis.com/");
    client.Timeout = TimeSpan.FromSeconds(30);
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
{
    MaxConnectionsPerServer = 10
});
```

## 設定管理

### 1. appsettings.json 構造

```json
{
  "Authentication": {
    "Google": {
      "ClientId": "",
      "ClientSecret": "",
      "Enabled": true,
      "Scopes": ["openid", "profile", "email"],
      "CallbackPath": "/signin-google",
      "SaveTokens": true
    }
  },
  "ExternalAuth": {
    "TokenCacheExpiry": "01:00:00",
    "ProfileSyncInterval": "24:00:00",
    "MaxRetryAttempts": 3
  }
}
```

### 2. 環境変数サポート

```csharp
public class GoogleAuthOptions
{
    public string ClientId { get; set; }
    public string ClientSecret { get; set; }
    public bool Enabled { get; set; } = true;
    public string[] Scopes { get; set; }
    public string CallbackPath { get; set; }
    
    public static GoogleAuthOptions FromConfiguration(IConfiguration configuration)
    {
        var options = new GoogleAuthOptions();
        configuration.GetSection("Authentication:Google").Bind(options);
        
        // 環境変数での上書き
        options.ClientId = Environment.GetEnvironmentVariable("GOOGLE_OAUTH_CLIENT_ID") ?? options.ClientId;
        options.ClientSecret = Environment.GetEnvironmentVariable("GOOGLE_OAUTH_CLIENT_SECRET") ?? options.ClientSecret;
        
        return options;
    }
}
```

## デプロイメント考慮事項

### 1. 環境別設定

- **開発環境**: localhost:44396でのリダイレクトURI
- **ステージング環境**: ステージングドメインでの設定
- **本番環境**: 本番ドメインでの設定

### 2. Google Cloud Console設定

```
承認済みのリダイレクト URI:
- https://localhost:44396/signin-google (開発)
- https://staging.transcriptcleaner.com/signin-google (ステージング)
- https://transcriptcleaner.com/signin-google (本番)

承認済みのJavaScript生成元:
- https://localhost:44396 (開発)
- https://staging.transcriptcleaner.com (ステージング)
- https://transcriptcleaner.com (本番)
```

### 3. 証明書管理

- 開発環境: 自己署名証明書
- 本番環境: 有効なSSL証明書必須##
 SDK更新考慮事項

### 1. .NET 9.0への移行

**現在の環境確認:**
```bash
dotnet --version
dotnet --list-sdks
```

**必要に応じてSDK更新:**
```bash
# 最新の.NET 9.0 SDKをインストール
# https://dotnet.microsoft.com/download/dotnet/9.0
```

**プロジェクトファイル更新:**
```xml
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
</Project>
```

### 2. ABP Framework最新版対応

**パッケージ更新:**
```xml
<PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite" Version="9.0.*" />
<PackageReference Include="Volo.Abp.Account.Web.OpenIddict" Version="9.0.*" />
<PackageReference Include="Volo.Abp.Identity.AspNetCore" Version="9.0.*" />
```

### 3. 新機能活用

**.NET 9.0の新機能:**
- パフォーマンス改善されたSystem.Text.Json
- 新しいHttpClientファクトリー機能
- 改善されたDependency Injection

**ABP Framework最新版の新機能:**
- 改善されたOpenIddict統合
- 新しい外部認証プロバイダーサポート
- セキュリティ強化

### 4. 互換性確認

**既存コードの互換性チェック:**
- 非推奨APIの確認と更新
- 新しいセキュリティ要件への対応
- パフォーマンス最適化の適用