<style>
    .transcript-sidebar-controls {
        padding: 15px 10px;
        background: #2c3e50;
        color: #ecf0f1;
        border-radius: 6px;
        margin: 10px 0;
        font-size: 13px;
    }
    
    .transcript-sidebar-controls h6 {
        color: #ecf0f1;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid #34495e;
    }
    
    .sidebar-form-group {
        margin-bottom: 15px;
    }
    
    .sidebar-form-group label {
        display: block;
        font-size: 11px;
        font-weight: 500;
        color: #bdc3c7;
        margin-bottom: 5px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .sidebar-form-group select,
    .sidebar-form-group textarea {
        width: 100%;
        padding: 6px 8px;
        border: 1px solid #34495e;
        border-radius: 4px;
        background: #34495e;
        color: #ecf0f1;
        font-size: 12px;
        transition: border-color 0.3s;
    }
    
    .sidebar-form-group select:focus,
    .sidebar-form-group textarea:focus {
        outline: none;
        border-color: #3498db;
        background: #3a4f66;
    }
    
    .sidebar-form-group textarea {
        resize: vertical;
        min-height: 60px;
    }
    
    .sidebar-upload-section {
        margin-bottom: 15px;
    }
    
    .sidebar-upload-group {
        margin-bottom: 12px;
    }
    
    .sidebar-upload-area {
        border: 2px dashed #34495e;
        border-radius: 4px;
        padding: 12px 8px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #34495e;
    }
    
    .sidebar-upload-area:hover {
        border-color: #3498db;
        background: #3a4f66;
    }
    
    .sidebar-upload-area.has-file {
        border-color: #27ae60;
        background: #2d5a3d;
    }
    
    .sidebar-upload-area i {
        font-size: 16px;
        color: #7f8c8d;
        margin-bottom: 6px;
        display: block;
    }
    
    .sidebar-upload-area p {
        margin: 0;
        font-size: 11px;
        color: #95a5a6;
        line-height: 1.3;
    }
    
    .sidebar-execute-btn {
        width: 100%;
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 10px;
    }
    
    .sidebar-execute-btn:hover {
        background: linear-gradient(135deg, #2980b9, #1f5f8b);
        transform: translateY(-1px);
    }
    
    .sidebar-execute-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
    
    .sidebar-execute-btn i {
        margin-right: 6px;
    }
</style>

<div class="transcript-sidebar-controls">
    <h6><i class="fas fa-cogs"></i> 訂正設定</h6>
    
    <div class="sidebar-form-group">
        <label for="sidebarModel">AIモデル</label>
        <select id="sidebarModel" name="model">
            <option value="gpt-4">GPT-4</option>
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
        </select>
    </div>
    
    <div class="sidebar-form-group">
        <label for="sidebarMode">処理モード</label>
        <select id="sidebarMode" name="mode">
            <option value="grammar">文法修正</option>
            <option value="formatting">フォーマット</option>
            <option value="comprehensive">包括的</option>
        </select>
    </div>
    
    <div class="sidebar-form-group">
        <label for="sidebarCustomPrompt">カスタムプロンプト</label>
        <textarea id="sidebarCustomPrompt" name="customPrompt" placeholder="カスタムプロンプト"></textarea>
    </div>
    
    <div class="sidebar-upload-section">
        <div class="sidebar-upload-group">
            <label><i class="fas fa-file-alt"></i> 議事録ファイル</label>
            <div class="sidebar-upload-area" onclick="sidebarUploadTxt()">
                <i class="fas fa-cloud-upload-alt"></i>
                <p id="sidebarTxtUploadText">議事録ファイルを選択</p>
            </div>
            <input type="file" id="sidebarTxtFileInput" accept=".txt" style="display: none;">
        </div>
        
        <div class="sidebar-upload-group">
            <label><i class="fas fa-table"></i> 誤字脱字一覧</label>
            <div class="sidebar-upload-area" onclick="sidebarUploadCsv()">
                <i class="fas fa-cloud-upload-alt"></i>
                <p id="sidebarCsvUploadText">CSVファイルを選択</p>
            </div>
            <input type="file" id="sidebarCsvFileInput" accept=".csv" style="display: none;">
        </div>
    </div>
    
    <button type="button" id="sidebarExecuteBtn" class="sidebar-execute-btn" onclick="sidebarExecuteCorrection()">
        <i class="fas fa-play"></i> 実行
    </button>
</div>

<script>
    // サイドバー用のグローバル変数
    window.sidebarOriginalText = '';
    window.sidebarCorrectedText = '';
    window.sidebarWordListCsv = '';
    
    // ファイルハンドラーの初期化
    document.addEventListener('DOMContentLoaded', function() {
        initializeSidebarFileHandlers();
    });
    
    function initializeSidebarFileHandlers() {
        const txtFileInput = document.getElementById('sidebarTxtFileInput');
        const csvFileInput = document.getElementById('sidebarCsvFileInput');
        
        if (txtFileInput) {
            txtFileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleSidebarTxtFileSelect(e.target.files[0]);
                }
            });
        }
        
        if (csvFileInput) {
            csvFileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleSidebarCsvFileSelect(e.target.files[0]);
                }
            });
        }
    }
    
    function sidebarUploadTxt() {
        document.getElementById('sidebarTxtFileInput').click();
    }
    
    function sidebarUploadCsv() {
        document.getElementById('sidebarCsvFileInput').click();
    }
    
    function handleSidebarTxtFileSelect(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            window.sidebarOriginalText = e.target.result;
            document.getElementById('sidebarTxtUploadText').textContent = `選択済み: ${file.name}`;
            document.querySelector('[onclick="sidebarUploadTxt()"]').classList.add('has-file');
            
            // メインページのパネルにも反映
            if (typeof displayOriginalTextInMainPanel === 'function') {
                displayOriginalTextInMainPanel(window.sidebarOriginalText);
            }
        };
        reader.readAsText(file, 'UTF-8');
    }
    
    function handleSidebarCsvFileSelect(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            window.sidebarWordListCsv = e.target.result;
            document.getElementById('sidebarCsvUploadText').textContent = `選択済み: ${file.name}`;
            document.querySelector('[onclick="sidebarUploadCsv()"]').classList.add('has-file');
        };
        reader.readAsText(file, 'UTF-8');
    }
    
    async function sidebarExecuteCorrection() {
        if (!window.sidebarOriginalText) {
            alert('ファイルを選択してください。');
            return;
        }
        
        const requestData = {
            text: window.sidebarOriginalText,
            language: 'ja',
            correctionType: document.getElementById('sidebarMode').value,
            customPrompt: document.getElementById('sidebarCustomPrompt').value,
            wordList: window.sidebarWordListCsv || null
        };
        
        console.log('Request data:', requestData);
        console.log('Original text length:', window.sidebarOriginalText ? window.sidebarOriginalText.length : 0);
        console.log('Mode element exists:', !!document.getElementById('sidebarMode'));
        console.log('Custom prompt element exists:', !!document.getElementById('sidebarCustomPrompt'));
        
        document.getElementById('sidebarExecuteBtn').disabled = true;
        document.getElementById('sidebarExecuteBtn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> 処理中...';
        
        // メインページのローディング表示
        if (typeof showMainPageLoading === 'function') {
            showMainPageLoading();
        }
        
        try {
            console.log('Sending request to:', '/api/manual/transcript/correct');
            console.log('Request body:', JSON.stringify(requestData));
            
            const response = await fetch('/api/manual/transcript/correct', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(requestData)
            });
            
            console.log('Response status:', response.status);
            console.log('Response headers:', Object.fromEntries(response.headers.entries()));
            
            if (response.ok) {
                const result = await response.json();
                console.log('Main endpoint result:', result);
                
                if (result && result.success && result.correctedText) {
                    window.sidebarCorrectedText = result.correctedText;
                    
                    // メインページに結果を表示
                    if (typeof displayCorrectedTextInMainPanel === 'function') {
                        displayCorrectedTextInMainPanel(window.sidebarCorrectedText);
                    }
                } else {
                    throw new Error(result?.errorMessage || 'No corrected text received');
                }
            } else {
                let errorMessage = `HTTP ${response.status}`;
                
                try {
                    const errorResponse = await response.text();
                    console.log('Error response body:', errorResponse);
                    
                    const errorJson = JSON.parse(errorResponse);
                    errorMessage = errorJson.errorMessage || errorJson.title || errorMessage;
                    console.log('Parsed error message:', errorMessage);
                } catch (parseError) {
                    console.log('Failed to parse error response:', parseError);
                }
                
                throw new Error(errorMessage);
            }
        } catch (error) {
            console.error('Correction failed:', error);
            
            // エラーメッセージを表示
            let errorMessage = 'エラーが発生しました';
            if (error.message) {
                errorMessage = error.message;
            }
            alert(errorMessage);
        } finally {
            document.getElementById('sidebarExecuteBtn').disabled = false;
            document.getElementById('sidebarExecuteBtn').innerHTML = '<i class="fas fa-play"></i> 実行';
            
            // メインページのローディング非表示
            if (typeof hideMainPageLoading === 'function') {
                hideMainPageLoading();
            }
        }
    }
</script>
