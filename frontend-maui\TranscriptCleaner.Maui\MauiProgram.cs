﻿using Microsoft.Extensions.Logging;
using TranscriptCleaner.Maui.Services;
using TranscriptCleaner.Maui.ViewModels;

namespace TranscriptCleaner.Maui;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                // システムフォントのConsolasを使用（ファイルではなく）
            });

        // Register services
        builder.Services.AddSingleton<AuthenticationService>();
        builder.Services.AddSingleton<IGoogleAuthService, GoogleAuthService>();
        builder.Services.AddSingleton<IOfflineAuthService, OfflineAuthService>();
        builder.Services.AddSingleton<INetworkService, NetworkService>();
        builder.Services.AddSingleton<IJwtTokenService, JwtTokenService>();
        builder.Services.AddSingleton<IAuthSyncService, AuthSyncService>();

        // Register ViewModels
        builder.Services.AddTransient<LoginPageViewModel>();

        // Register Pages
        builder.Services.AddTransient<LoginPage>();
        builder.Services.AddTransient<MainPage>();

#if DEBUG
        builder.Logging.AddDebug();
#endif

        return builder.Build();
    }
}
